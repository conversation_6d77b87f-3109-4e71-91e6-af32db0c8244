
  'http://127.0.0.1:8000/api/student/dashboard' \
  -H 'accept: application/json'

{
  "success": true,
  "data": {
    "student": {
      "username": "string",
      "email": "string",
      "mobile": "string",
      "user_type": "string",
      "country": "string",
      "profile_picture": "string",
      "id": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
      "is_email_verified": false,
      "is_mobile_verified": false
    },
    "classes": [
      {
        "id": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
        "name": "string",
        "teacher_name": "string",
        "teacher_id": "3fa85f64-5717-4562-b3fc-2c963f66afa6"
      }
    ],
    "exams": [
      {
        "id": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
        "title": "string",
        "description": "string",
        "total_marks": 0,
        "total_duration": 0,
        "start_time": "2025-07-23T23:41:35.867Z",
        "end_time": "2025-07-23T23:41:35.867Z"
      }
    ],
    "performance": {
      "overall_grade": 0,
      "subject_grades": {
        "additionalProp1": 0,
        "additionalProp2": 0,
        "additionalProp3": 0
      },
      "recent_scores": [
        {
          "additionalProp1": {}
        }
      ],
      "improvement_trend": "stable",
      "rank_in_class": 0
    },
    "assignments": [
      {
        "id": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
        "name": "string",
        "description": "string",
        "status": "pending",
        "deadline": "2025-07-23T23:41:35.867Z",
        "accept_after_deadline": false,
        "created_at": "2025-07-23T23:41:35.867Z",
        "updated_at": "2025-07-23T23:41:35.867Z",
        "subject": {
          "name": "string",
          "id": "3fa85f64-5717-4562-b3fc-2c963f66afa6"
        },
        "chapters": [],
        "topics": [],
        "subtopics": []
      }
    ],
    "recent_activity": [
      {
        "id": "string",
        "type": "string",
        "title": "string",
        "description": "string",
        "timestamp": "2025-07-23T23:41:35.867Z",
        "metadata": {
          "additionalProp1": {}
        }
      }
    ],
    "study_metrics": {
      "total_points": 0,
      "level": 1,
      "tasks_completed": 0,
      "exams_taken": 0,
      "average_grade": 0,
      "badges_earned": [
        "string"
      ]
    },
    "schedule": [
      {
        "id": "string",
        "title": "string",
        "type": "string",
        "start_time": "2025-07-23T23:41:35.867Z",
        "end_time": "2025-07-23T23:41:35.867Z",
        "classroom_id": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
        "subject": "string",
        "location": "string"
      }
    ],
    "quick_actions": [
      {
        "id": "string",
        "title": "string",
        "description": "string",
        "action_type": "string",
        "url": "string",
        "priority": "medium",
        "due_date": "2025-07-23T23:41:35.867Z"
      }
    ],
    "unread_notifications_count": 0,
    "last_updated": "2025-07-23T23:41:35.868Z"
  }
}




-------------------------------------------------

curl --location 'http://127.0.0.1:8000/api/student/dashboard/summary' \
--header 'accept: application/json' \
--header 'Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************.c6HTqciOPFWSGRZblqHtU9r2JV9LRIDBO9ko8b_49GI'
{
    "success": true,
    "data": {
        "total_classes": 1,
        "pending_assignments": 0,
        "upcoming_exams": 0,
        "overall_grade": 0.0,
        "unread_notifications": 0,
        "total_points": 0,
        "current_level": 1,
        "quick_actions_count": 0,
        "last_updated": "2025-07-23T23:44:06.912691+00:00"
    }
}



---------------------------------------------------------------------------

curl --location 'http://127.0.0.1:8000/api/student/dashboard/quick-actions' \
--header 'accept: application/json' \
--header 'Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************.c6HTqciOPFWSGRZblqHtU9r2JV9LRIDBO9ko8b_49GI'
{
    "success": true,
    "data": {
        "quick_actions": [],
        "last_updated": "2025-07-23T23:45:08.658264+00:00"
    }
}

-----------------------------------------------------------------

curl --location 'http://127.0.0.1:8000/api/student/dashboard/performance' \
--header 'accept: application/json' \
--header 'Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************.c6HTqciOPFWSGRZblqHtU9r2JV9LRIDBO9ko8b_49GI'
{
    "success": true,
    "data": {
        "performance": {
            "overall_grade": 0.0,
            "subject_grades": {},
            "recent_scores": [],
            "improvement_trend": "stable",
            "rank_in_class": null
        },
        "study_metrics": {
            "total_points": 0,
            "level": 1,
            "tasks_completed": 0,
            "exams_taken": 0,
            "average_grade": 0.0,
            "badges_earned": []
        },
        "last_updated": "2025-07-23T23:46:04.355595+00:00"
    }
}



-----------------------------------------------------------------------

curl --location 'http://127.0.0.1:8000/api/student/dashboard/schedule' \
--header 'accept: application/json' \
--header 'Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************.c6HTqciOPFWSGRZblqHtU9r2JV9LRIDBO9ko8b_49GI'
