// navigationConfig.js
import { 
  HomeIcon, 
  ChatBubbleLeftRightIcon, 
  CalendarIcon, 
  Cog6ToothIcon,
  UserIcon,
  BellIcon,
  ShieldCheckIcon,
  ChartBarIcon,
  BookOpenIcon,
  UsersIcon,
  DocumentTextIcon
} from "@heroicons/react/24/outline";

const navigationConfig = [
    {
      label: "Dashboard",
      icon: HomeIcon,
      path: "/",

    },
    {
      label: "Messages",
      icon: ChatBubbleLeftRightIcon,
      path: "/messages",
      badge: 4,
    },
    {
      label: "Calendar",
      icon: CalendarIcon,
      path: "/calendar",
    },
    {
      label: "Settings",
      icon: Cog6ToothIcon,
      children: [
        { label: "Profile", path: "/settings/profile" },
        { label: "Notifications", path: "/settings/notifications" },
        { label: "Security", path: "/settings/security" },
      ],
    },
    // Example of how easy it is to add new navigation items:
    // {
    //   label: "Courses",
    //   icon: BookOpenIcon,
    //   path: "/courses",
    // },
    // {
    //   label: "Students",
    //   icon: UsersIcon,
    //   path: "/students",
    // },
    // {
    //   label: "Documents",
    //   icon: DocumentTextIcon,
    //   path: "/documents",
    // },
  ];
  
  export default navigationConfig; 