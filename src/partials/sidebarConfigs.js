import { 
  HomeIcon, 
  UserIcon, 
  Cog6ToothIcon, 
  BookOpenIcon, 
  UsersIcon, 
  BuildingOfficeIcon, 
  CurrencyDollarIcon,
  AcademicCapIcon,
  DocumentTextIcon,
  ClipboardDocumentListIcon,
  UserGroupIcon,
  ChartBarIcon,
  CreditCardIcon,
  CogIcon,
  BellIcon,
  EnvelopeIcon
} from "@heroicons/react/24/outline";

export const sidebarConfigs = {
  admin: [
    { label: "Dashboard", icon: HomeIcon, path: "/admin/dashboard" },
    { label: "Users", icon: UsersIcon, path: "/admin/users" },
    {
      label: "Config",
      icon: DocumentTextIcon,
      children: [
        { label: "Material", icon: DocumentTextIcon, path: "/admin/material" },
      ],
    },
    { label: "Plans", icon: CreditCardIcon, path: "/admin/plans" },
    { label: "Settings", icon: Cog6ToothIcon, path: "/admin/settings" },
  ],
  student: [
    { label: "Dashboard", icon: HomeIcon, path: "/student/dashboard" },
    { label: "Classes", icon: BookOpenIcon, path: "/student/classes" },
    { label: "Exams", icon: AcademicCapIcon, path: "/student/exams" },
    { label: "Settings", icon: Cog6ToothIcon, path: "/student/settings" },
  ],
  teacher: [
    { label: "Dashboard", icon: HomeIcon, path: "/teacher/dashboard" },
    { label: "My Classes", icon: AcademicCapIcon, path: "/teacher/classes" },
    { label: "Settings", icon: Cog6ToothIcon, path: "/teacher/settings" },
    { label: "Exams", icon: AcademicCapIcon, path: "/teacher/exams" },
  ],
  mentor: [
    { label: "Dashboard", icon: HomeIcon, path: "/mentor/dashboard" },
    { label: "Students", icon: UserGroupIcon, path: "/mentor/students" },
    { label: "Settings", icon: Cog6ToothIcon, path: "/mentor/settings" },
  ],
  institute: [
    { label: "Dashboard", icon: HomeIcon, path: "/institute/dashboard" },
    { label: "Teachers", icon: AcademicCapIcon, path: "/institute/teachers" },
    { label: "Students", icon: UserGroupIcon, path: "/institute/students" },
    { label: "Analytics", icon: ChartBarIcon, path: "/institute/analytics" },
    { label: "Settings", icon: Cog6ToothIcon, path: "/institute/settings" },
  ],
  sponsor: [
    { label: "Dashboard", icon: HomeIcon, path: "/sponsor/dashboard" },
    { label: "Institutes", icon: BuildingOfficeIcon, path: "/sponsor/institutes" },
    { label: "Funding", icon: CurrencyDollarIcon, path: "/sponsor/funding" },
    { label: "Reports", icon: ChartBarIcon, path: "/sponsor/reports" },
    { label: "Settings", icon: Cog6ToothIcon, path: "/sponsor/settings" },
  ],
}; 