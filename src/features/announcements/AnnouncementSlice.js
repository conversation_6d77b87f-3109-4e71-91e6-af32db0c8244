import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import axios from "axios";
import URL from "../api_url/API_URL";

const BASE_URL = `${URL}/api/announcements`;
const getAuthToken = () => localStorage.getItem("token");

// Thunks

// Create announcement
export const createAnnouncement = createAsyncThunk(
  "announcements/createAnnouncement",
  async (params, thunkAPI) => {
    try {
      const { data, classroom_id } = params;
      
      console.log('Creating announcement with data:', data);
      console.log('Classroom ID:', classroom_id);
      
      if (!data || !classroom_id) {
        throw new Error('Data and classroom_id are required');
      }
      
      const res = await axios.post(`${BASE_URL}/${classroom_id}`, data, {
        headers: {
          Authorization: `Bearer ${getAuthToken()}`,
          "Content-Type": "application/json",
        },
      });
      return res.data;
    } catch (err) {
      console.error('Error creating announcement:', err.response?.data);
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// Get all announcements for a classroom (teacher view)
export const fetchAnnouncements = createAsyncThunk(
  "announcements/fetchAnnouncements",
  async ({ classroom_id, skip = 0, limit = 100 }, thunkAPI) => {
    try {
      const res = await axios.get(`${BASE_URL}/?classroom_id=${classroom_id}&skip=${skip}&limit=${limit}`, {
        headers: { Authorization: `Bearer ${getAuthToken()}` },
      });
      console.log('Fetch announcements response:', res.data);
      return res.data;
    } catch (err) {
      console.error('Error fetching announcements:', err.response?.data);
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// Get announcements for student in a classroom
export const fetchAnnouncementsForStudent = createAsyncThunk(
  "announcements/fetchAnnouncementsForStudent",
  async ({ classroom_id, skip = 0, limit = 100 }, thunkAPI) => {
    try {
      const res = await axios.get(`${BASE_URL}/student/${classroom_id}?skip=${skip}&limit=${limit}`, {
        headers: { Authorization: `Bearer ${getAuthToken()}` },
      });
      console.log('Fetch student announcements response:', res.data);
      return res.data;
    } catch (err) {
      console.error('Error fetching student announcements:', err.response?.data);
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// Get announcement by ID
export const fetchAnnouncementById = createAsyncThunk(
  "announcements/fetchAnnouncementById",
  async (id, thunkAPI) => {
    try {
      const res = await axios.get(`${BASE_URL}/${id}`, {
        headers: { Authorization: `Bearer ${getAuthToken()}` },
      });
      return res.data;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// Update announcement by ID
export const updateAnnouncement = createAsyncThunk(
  "announcements/updateAnnouncement",
  async ({ id, data }, thunkAPI) => {
    try {
      const res = await axios.put(`${BASE_URL}/${id}`, data, {
        headers: {
          Authorization: `Bearer ${getAuthToken()}`,
          "Content-Type": "application/json",
        },
      });
      return res.data;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// Delete announcement by ID
export const deleteAnnouncement = createAsyncThunk(
  "announcements/deleteAnnouncement",
  async (id, thunkAPI) => {
    try {
      await axios.delete(`${BASE_URL}/${id}`, {
        headers: { Authorization: `Bearer ${getAuthToken()}` },
      });
      return id;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// Initial State
const initialState = {
  announcements: [],
  total: 0,
  announcement: null,
  loading: false,
  error: null,
  success: null,
};

const announcementSlice = createSlice({
  name: "announcements",
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder
      // Create
      .addCase(createAnnouncement.pending, (state) => {
        state.loading = true;
        state.error = null;
        state.success = null;
      })
      .addCase(createAnnouncement.fulfilled, (state, action) => {
        state.loading = false;
        state.success = "Announcement created successfully.";
        state.announcement = action.payload;
      })
      .addCase(createAnnouncement.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // Fetch all
      .addCase(fetchAnnouncements.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchAnnouncements.fulfilled, (state, action) => {
        state.loading = false;
        console.log('Setting announcements in state:', action.payload);
        // Handle different response formats
        if (Array.isArray(action.payload)) {
          state.announcements = action.payload;
          state.total = action.payload.length;
        } else if (action.payload && Array.isArray(action.payload.announcements)) {
          state.announcements = action.payload.announcements;
          state.total = action.payload.total || action.payload.announcements.length;
        } else {
          state.announcements = [];
          state.total = 0;
        }
      })
      .addCase(fetchAnnouncements.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // Fetch announcements for student
      .addCase(fetchAnnouncementsForStudent.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchAnnouncementsForStudent.fulfilled, (state, action) => {
        state.loading = false;
        console.log('Setting student announcements in state:', action.payload);
        // Handle different response formats
        if (Array.isArray(action.payload)) {
          state.announcements = action.payload;
          state.total = action.payload.length;
        } else if (action.payload && Array.isArray(action.payload.announcements)) {
          state.announcements = action.payload.announcements;
          state.total = action.payload.total || action.payload.announcements.length;
        } else {
          state.announcements = [];
          state.total = 0;
        }
      })
      .addCase(fetchAnnouncementsForStudent.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // Fetch by ID
      .addCase(fetchAnnouncementById.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchAnnouncementById.fulfilled, (state, action) => {
        state.loading = false;
        state.announcement = action.payload;
      })
      .addCase(fetchAnnouncementById.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // Update
      .addCase(updateAnnouncement.pending, (state) => {
        state.loading = true;
        state.error = null;
        state.success = null;
      })
      .addCase(updateAnnouncement.fulfilled, (state, action) => {
        state.loading = false;
        state.success = "Announcement updated successfully.";
        state.announcement = action.payload;
      })
      .addCase(updateAnnouncement.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // Delete
      .addCase(deleteAnnouncement.pending, (state) => {
        state.loading = true;
        state.error = null;
        state.success = null;
      })
      .addCase(deleteAnnouncement.fulfilled, (state, action) => {
        state.loading = false;
        state.success = "Announcement deleted successfully.";
        state.announcement = null;
      })
      .addCase(deleteAnnouncement.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      });
  },
});

export default announcementSlice.reducer;
