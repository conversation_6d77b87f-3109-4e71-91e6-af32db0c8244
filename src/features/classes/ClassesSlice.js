import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import axios from "axios";
import URL from "../api_url/API_URL";

const BASE_URL = `${URL}/api`;
const getAuthToken = () => localStorage.getItem("token");

// Thunks
// 1. Fetch all classes
export const fetchClasses = createAsyncThunk(
  "classes/fetchClasses",
  async (_, thunkAPI) => {
    try {
      const res = await axios.get(`${BASE_URL}/classes/`, {
        headers: { Authorization: `Bearer ${getAuthToken()}` },
      });
      return res.data;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// 2. Create class
export const createClass = createAsyncThunk(
  "classes/createClass",
  async (classData, thunkAPI) => {
    try {
      const res = await axios.post(`${BASE_URL}/classes/`, classData, {
        headers: {
          Authorization: `Bearer ${getAuthToken()}`,
          "Content-Type": "application/json",
        },
      });
      return res.data;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// 3. Fetch class by ID
export const fetchClassById = createAsyncThunk(
  "classes/fetchClassById",
  async (class_id, thunkAPI) => {
    try {
      const res = await axios.get(`${BASE_URL}/classes/${class_id}`, {
        headers: { Authorization: `Bearer ${getAuthToken()}` },
      });
      return res.data;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// 4. Update class by ID
export const updateClass = createAsyncThunk(
  "classes/updateClass",
  async ({ class_id, classData }, thunkAPI) => {
    try {
      const res = await axios.put(`${BASE_URL}/classes/${class_id}`, classData, {
        headers: {
          Authorization: `Bearer ${getAuthToken()}`,
          "Content-Type": "application/json",
        },
      });
      return res.data;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// 5. Delete class by ID
export const deleteClass = createAsyncThunk(
  "classes/deleteClass",
  async (class_id, thunkAPI) => {
    try {
      await axios.delete(`${BASE_URL}/classes/${class_id}`, {
        headers: { Authorization: `Bearer ${getAuthToken()}` },
      });
      return class_id;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

const initialState = {
  classes: [],
  currentClass: null,
  loading: false,
  error: null,
  success: null,
};

const classesSlice = createSlice({
  name: "classes",
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder
      // Fetch all
      .addCase(fetchClasses.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchClasses.fulfilled, (state, action) => {
        state.loading = false;
        state.classes = action.payload;
      })
      .addCase(fetchClasses.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      // Create
      .addCase(createClass.pending, (state) => {
        state.loading = true;
        state.error = null;
        state.success = null;
      })
      .addCase(createClass.fulfilled, (state, action) => {
        state.loading = false;
        state.success = "Class created successfully.";
        state.classes.push(action.payload);
      })
      .addCase(createClass.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      // Fetch by ID
      .addCase(fetchClassById.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchClassById.fulfilled, (state, action) => {
        state.loading = false;
        state.currentClass = action.payload;
      })
      .addCase(fetchClassById.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      // Update
      .addCase(updateClass.pending, (state) => {
        state.loading = true;
        state.error = null;
        state.success = null;
      })
      .addCase(updateClass.fulfilled, (state, action) => {
        state.loading = false;
        state.success = "Class updated successfully.";
        const idx = state.classes.findIndex(c => c.id === action.payload.id);
        if (idx !== -1) state.classes[idx] = action.payload;
        if (state.currentClass && state.currentClass.id === action.payload.id) {
          state.currentClass = action.payload;
        }
      })
      .addCase(updateClass.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      // Delete
      .addCase(deleteClass.pending, (state) => {
        state.loading = true;
        state.error = null;
        state.success = null;
      })
      .addCase(deleteClass.fulfilled, (state, action) => {
        state.loading = false;
        state.success = "Class deleted successfully.";
        state.classes = state.classes.filter(c => c.id !== action.payload);
        if (state.currentClass && state.currentClass.id === action.payload) {
          state.currentClass = null;
        }
      })
      .addCase(deleteClass.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      });
  },
});

export default classesSlice.reducer;
export { fetchClasses, createClass, fetchClassById, updateClass, deleteClass };
