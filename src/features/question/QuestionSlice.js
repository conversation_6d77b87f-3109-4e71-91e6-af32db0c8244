import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import axios from "axios";
import URL from "../api_url/API_URL";

const BASE_URL = `${URL}/api/questions`;
const getAuthToken = () => localStorage.getItem("token");

// Thunks
export const fetchQuestions = createAsyncThunk(
  "questions/fetchQuestions",
  async (_, thunkAPI) => {
    try {
      const res = await axios.get(`${BASE_URL}/`, {
        headers: { Authorization: `Bearer ${getAuthToken()}` },
      });
      return res.data;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

export const fetchQuestionById = createAsyncThunk(
  "questions/fetchQuestionById",
  async (id, thunkAPI) => {
    try {
      const res = await axios.get(`${BASE_URL}/${id}`, {
        headers: { Authorization: `Bearer ${getAuthToken()}` },
      });
      return res.data;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

export const createQuestion = createAsyncThunk(
  "questions/createQuestion",
  async (questionData, thunkAPI) => {
    try {
      const res = await axios.post(`${BASE_URL}/`, questionData, {
        headers: {
          Authorization: `Bearer ${getAuthToken()}`,
          "Content-Type": "application/json",
        },
      });
      return res.data;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

export const updateQuestion = createAsyncThunk(
  "questions/updateQuestion",
  async ({ id, questionData }, thunkAPI) => {
    try {
      const res = await axios.put(`${BASE_URL}/${id}`, questionData, {
        headers: {
          Authorization: `Bearer ${getAuthToken()}`,
          "Content-Type": "application/json",
        },
      });
      return res.data;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

export const deleteQuestion = createAsyncThunk(
  "questions/deleteQuestion",
  async (id, thunkAPI) => {
    try {
      await axios.delete(`${BASE_URL}/${id}`, {
        headers: { Authorization: `Bearer ${getAuthToken()}` },
      });
      return id;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

export const updateQuestionOption = createAsyncThunk(
  "questions/updateQuestionOption",
  async ({ optionId, optionData }, thunkAPI) => {
    try {
      const res = await axios.put(`${BASE_URL}/options/${optionId}`, optionData, {
        headers: {
          Authorization: `Bearer ${getAuthToken()}`,
          "Content-Type": "application/json",
        },
      });
      return res.data;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// AI Generate Questions
export const aiGenerateQuestions = createAsyncThunk(
  "questions/aiGenerateQuestions",
  async (payload, thunkAPI) => {
    try {
      const res = await axios.post(`${BASE_URL}/ai-generate`, payload, {
        headers: {
          Authorization: `Bearer ${getAuthToken()}`,
          "Content-Type": "application/json",
        },
      });
      return res.data;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

const initialState = {
  questions: [],
  currentQuestion: null,
  loading: false,
  error: null,
  success: null,
  aiGeneratedQuestions: null,
};

const questionSlice = createSlice({
  name: "questions",
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder
      // Fetch all
      .addCase(fetchQuestions.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchQuestions.fulfilled, (state, action) => {
        state.loading = false;
        state.questions = action.payload;
      })
      .addCase(fetchQuestions.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      // Fetch by ID
      .addCase(fetchQuestionById.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchQuestionById.fulfilled, (state, action) => {
        state.loading = false;
        state.currentQuestion = action.payload;
      })
      .addCase(fetchQuestionById.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      // Create
      .addCase(createQuestion.pending, (state) => {
        state.loading = true;
        state.error = null;
        state.success = null;
      })
      .addCase(createQuestion.fulfilled, (state, action) => {
        state.loading = false;
        state.success = "Question created successfully.";
        state.questions.push(action.payload);
      })
      .addCase(createQuestion.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      // Update
      .addCase(updateQuestion.pending, (state) => {
        state.loading = true;
        state.error = null;
        state.success = null;
      })
      .addCase(updateQuestion.fulfilled, (state, action) => {
        state.loading = false;
        state.success = "Question updated successfully.";
        const idx = state.questions.findIndex(q => q.id === action.payload.id);
        if (idx !== -1) state.questions[idx] = action.payload;
        if (state.currentQuestion && state.currentQuestion.id === action.payload.id) {
          state.currentQuestion = action.payload;
        }
      })
      .addCase(updateQuestion.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      // Delete
      .addCase(deleteQuestion.pending, (state) => {
        state.loading = true;
        state.error = null;
        state.success = null;
      })
      .addCase(deleteQuestion.fulfilled, (state, action) => {
        state.loading = false;
        state.success = "Question deleted successfully.";
        state.questions = state.questions.filter(q => q.id !== action.payload);
        if (state.currentQuestion && state.currentQuestion.id === action.payload) {
          state.currentQuestion = null;
        }
      })
      .addCase(deleteQuestion.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      // Update Option
      .addCase(updateQuestionOption.pending, (state) => {
        state.loading = true;
        state.error = null;
        state.success = null;
      })
      .addCase(updateQuestionOption.fulfilled, (state, action) => {
        state.loading = false;
        state.success = "Question option updated successfully.";
      })
      .addCase(updateQuestionOption.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      // AI Generate Questions
      .addCase(aiGenerateQuestions.pending, (state) => {
        state.loading = true;
        state.error = null;
        state.aiGeneratedQuestions = null;
      })
      .addCase(aiGenerateQuestions.fulfilled, (state, action) => {
        state.loading = false;
        state.aiGeneratedQuestions = action.payload;
      })
      .addCase(aiGenerateQuestions.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
        state.aiGeneratedQuestions = null;
      });
  },
});

export default questionSlice.reducer;
