import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import axios from "axios";

import API_BASE_URL from "../api_url/API_URL";

const getAuthToken = () => localStorage.getItem("token");

// Thunk: Get all users
export const fetchAllUsers = createAsyncThunk("users/fetchAll", async (_, thunkAPI) => {
  try {
    const res = await axios.get(`${API_BASE_URL}/api/users/`, {
      headers: {
        Authorization: `Bearer ${getAuthToken()}`,
      },
    });
    return res.data;
  } catch (err) {
    return thunkAPI.rejectWithValue(err.response?.data || "Failed to fetch users");
  }
});

// Thunk: Create new user
export const createUser = createAsyncThunk("users/createUser", async (userData, thunkAPI) => {
  try {
    const res = await axios.post(`${API_BASE_URL}/api/users/`, userData, {
      headers: {
        Authorization: `Bearer ${getAuthToken()}`,
        "Content-Type": "application/json",
      },
    });
    return res.data;
  } catch (err) {
    return thunkAPI.rejectWithValue(err.response?.data || "Failed to create user");
  }
});

// Thunk: Get single user by ID
export const fetchSingleUser = createAsyncThunk("users/fetchSingle", async (userId, thunkAPI) => {
  try {
    const res = await axios.get(`${API_BASE_URL}/api/users/${userId}`, {
      headers: {
        Authorization: `Bearer ${getAuthToken()}`,
      },
    });
    return res.data;
  } catch (err) {
    return thunkAPI.rejectWithValue(err.response?.data || "Failed to fetch user");
  }
});

// ✅ Thunk: Get current authenticated user (me)
export const fetchCurrentUser = createAsyncThunk("users/fetchMe", async (_, thunkAPI) => {
  try {
    const res = await axios.get(`${API_BASE_URL}/api/users/me`, {
      headers: {
        Authorization: `Bearer ${getAuthToken()}`,
      },
    });
    return res.data;
  } catch (err) {
    return thunkAPI.rejectWithValue(err.response?.data || "Failed to fetch current user");
  }
});

// Thunk: Update user profile
export const updateUserProfile = createAsyncThunk("users/updateProfile", async (profileData, thunkAPI) => {
  try {
    const res = await axios.put(`${API_BASE_URL}/api/users/me`, profileData, {
      headers: {
        Authorization: `Bearer ${getAuthToken()}`,
        "Content-Type": "application/json",
      },
    });
    return res.data;
  } catch (err) {
    return thunkAPI.rejectWithValue(err.response?.data || "Failed to update profile");
  }
});

// Thunk: Upload profile picture
export const uploadProfilePicture = createAsyncThunk("users/uploadProfilePicture", async ({ user_id, profile_pic_url }, thunkAPI) => {
  try {
    const res = await axios.post(`${API_BASE_URL}/api/users/profile_picture`, null, {
      headers: {
        Authorization: `Bearer ${getAuthToken()}`,
      },
      params: {
        user_id,
        profile_pic_url,
      },
    });
    return res.data;
  } catch (err) {
    return thunkAPI.rejectWithValue(err.response?.data || "Failed to upload profile picture");
  }
});

// Thunk: Update profile picture
export const updateProfilePicture = createAsyncThunk("users/updateProfilePicture", async ({ user_id, profile_pic_url }, thunkAPI) => {
  try {
    const res = await axios.put(`${API_BASE_URL}/api/users/profile_picture`, null, {
      headers: {
        Authorization: `Bearer ${getAuthToken()}`,
      },
      params: {
        user_id,
        profile_pic_url,
      },
    });
    return res.data;
  } catch (err) {
    return thunkAPI.rejectWithValue(err.response?.data || "Failed to update profile picture");
  }
});

// Thunk: Generate SAS URL
export const generateSasUrl = createAsyncThunk("users/generateSasUrl", async (blob_name, thunkAPI) => {
  try {
    const res = await axios.get(`${API_BASE_URL}/api/users/generate_sas_url/${blob_name}`, {
      headers: {
        Authorization: `Bearer ${getAuthToken()}`,
      },
    });
    return res.data;
  } catch (err) {
    return thunkAPI.rejectWithValue(err.response?.data || "Failed to generate SAS URL");
  }
});

// Thunk: Get all Students
export const fetchAllStudents = createAsyncThunk("users/fetchAllStudents", async (_, thunkAPI) => {
  try {
    const res = await axios.get(`${API_BASE_URL}/api/users/students/all`, {
      headers: {
        Authorization: `Bearer ${getAuthToken()}`,
      },
    });
    return res.data;
  } catch (err) {
    return thunkAPI.rejectWithValue(err.response?.data || "Failed to fetch students");
  }
});

// Thunk: Get all Teachers
export const fetchAllTeachers = createAsyncThunk("users/fetchAllTeachers", async (_, thunkAPI) => {
  try {
    const res = await axios.get(`${API_BASE_URL}/api/users/teachers/all`, {
      headers: {
        Authorization: `Bearer ${getAuthToken()}`,
      },
    });
    return res.data;
  } catch (err) {
    return thunkAPI.rejectWithValue(err.response?.data || "Failed to fetch teachers");
  }
});

// Thunk: Get all Sponsors
export const fetchAllSponsors = createAsyncThunk("users/fetchAllSponsors", async (_, thunkAPI) => {
  try {
    const res = await axios.get(`${API_BASE_URL}/api/users/sponsors/all`, {
      headers: {
        Authorization: `Bearer ${getAuthToken()}`,
      },
    });
    return res.data;
  } catch (err) {
    return thunkAPI.rejectWithValue(err.response?.data || "Failed to fetch sponsors");
  }
});

// Thunk: Get all Institutes
export const fetchAllInstitutes = createAsyncThunk("users/fetchAllInstitutes", async (_, thunkAPI) => {
  try {
    const res = await axios.get(`${API_BASE_URL}/api/users/institutes/all`, {
      headers: {
        Authorization: `Bearer ${getAuthToken()}`,
      },
    });
    return res.data;
  } catch (err) {
    return thunkAPI.rejectWithValue(err.response?.data || "Failed to fetch institutes");
  }
});

const userSlice = createSlice({
  name: "users",
  initialState: {
    allUsers: [],
    allStudents: [],
    allTeachers: [],
    allSponsors: [],
    allInstitutes: [],
    selectedUser: null,
    currentUser: null,
    sasUrl: null,
    loading: false,
    error: null,
    success: null,
  },
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    clearSuccess: (state) => {
      state.success = null;
    },
    clearSasUrl: (state) => {
      state.sasUrl = null;
    },
  },
  extraReducers: (builder) => {
    builder
      // --- Fetch All Users ---
      .addCase(fetchAllUsers.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchAllUsers.fulfilled, (state, action) => {
        state.loading = false;
        state.allUsers = action.payload;
      })
      .addCase(fetchAllUsers.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // --- Create User ---
      .addCase(createUser.pending, (state) => {
        state.loading = true;
        state.error = null;
        state.success = null;
      })
      .addCase(createUser.fulfilled, (state, action) => {
        state.loading = false;
        state.success = "User created successfully";
        state.allUsers.push(action.payload);
      })
      .addCase(createUser.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // --- Fetch Single User ---
      .addCase(fetchSingleUser.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchSingleUser.fulfilled, (state, action) => {
        state.loading = false;
        state.selectedUser = action.payload;
      })
      .addCase(fetchSingleUser.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // --- Fetch Current User (me) ---
      .addCase(fetchCurrentUser.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchCurrentUser.fulfilled, (state, action) => {
        state.loading = false;
        state.currentUser = action.payload;
      })
      .addCase(fetchCurrentUser.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // --- Update User Profile ---
      .addCase(updateUserProfile.pending, (state) => {
        state.loading = true;
        state.error = null;
        state.success = null;
      })
      .addCase(updateUserProfile.fulfilled, (state, action) => {
        state.loading = false;
        state.success = "Profile updated successfully";
        state.currentUser = action.payload;
      })
      .addCase(updateUserProfile.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // --- Upload Profile Picture ---
      .addCase(uploadProfilePicture.pending, (state) => {
        state.loading = true;
        state.error = null;
        state.success = null;
      })
      .addCase(uploadProfilePicture.fulfilled, (state, action) => {
        state.loading = false;
        state.success = "Profile picture uploaded successfully";
        // Update current user if it's the same user
        if (state.currentUser && state.currentUser.id === action.payload.id) {
          state.currentUser = action.payload;
        }
      })
      .addCase(uploadProfilePicture.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // --- Update Profile Picture ---
      .addCase(updateProfilePicture.pending, (state) => {
        state.loading = true;
        state.error = null;
        state.success = null;
      })
      .addCase(updateProfilePicture.fulfilled, (state, action) => {
        state.loading = false;
        state.success = "Profile picture updated successfully";
        // Update current user if it's the same user
        if (state.currentUser && state.currentUser.id === action.payload.id) {
          state.currentUser = action.payload;
        }
      })
      .addCase(updateProfilePicture.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // --- Generate SAS URL ---
      .addCase(generateSasUrl.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(generateSasUrl.fulfilled, (state, action) => {
        state.loading = false;
        state.sasUrl = action.payload;
      })
      .addCase(generateSasUrl.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // --- Fetch All Students ---
      .addCase(fetchAllStudents.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchAllStudents.fulfilled, (state, action) => {
        state.loading = false;
        state.allStudents = action.payload;
      })
      .addCase(fetchAllStudents.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // --- Fetch All Teachers ---
      .addCase(fetchAllTeachers.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchAllTeachers.fulfilled, (state, action) => {
        state.loading = false;
        state.allTeachers = action.payload;
      })
      .addCase(fetchAllTeachers.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // --- Fetch All Sponsors ---
      .addCase(fetchAllSponsors.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchAllSponsors.fulfilled, (state, action) => {
        state.loading = false;
        state.allSponsors = action.payload;
      })
      .addCase(fetchAllSponsors.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // --- Fetch All Institutes ---
      .addCase(fetchAllInstitutes.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchAllInstitutes.fulfilled, (state, action) => {
        state.loading = false;
        state.allInstitutes = action.payload;
      })
      .addCase(fetchAllInstitutes.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      });
  },
});

export const { clearError, clearSuccess, clearSasUrl } = userSlice.actions;
export default userSlice.reducer;
