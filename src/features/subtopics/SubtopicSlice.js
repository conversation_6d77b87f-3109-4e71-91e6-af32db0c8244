import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import axios from "axios";
import URL from "../api_url/API_URL";

const BASE_URL = `${URL}/api/subtopics`;
const getAuthToken = () => localStorage.getItem("token");

// Thunks

// Fetch all subtopics with pagination
export const fetchSubtopics = createAsyncThunk(
  "subtopics/fetchSubtopics",
  async ({ skip = 0, limit = 100 } = {}, thunkAPI) => {
    try {
      const res = await axios.get(`${BASE_URL}/?skip=${skip}&limit=${limit}`, {
        headers: { Authorization: `Bearer ${getAuthToken()}` },
      });
      return res.data; // { subtopics: [...], total: N }
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// Create subtopic
export const createSubtopic = createAsyncThunk(
  "subtopics/createSubtopic",
  async (subtopicData, thunkAPI) => {
    try {
      // Ensure required fields are present
      const requiredData = {
        name: subtopicData.name,
        topic_id: subtopicData.topic_id,
        description: subtopicData.description || "", // Provide default empty string if missing
      };
      
      console.log('Creating subtopic with data:', requiredData);
      
      const res = await axios.post(`${BASE_URL}/`, requiredData, {
        headers: {
          Authorization: `Bearer ${getAuthToken()}`,
          "Content-Type": "application/json",
        },
      });
      return res.data;
    } catch (err) {
      console.error('Error creating subtopic:', err.response?.data);
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// Fetch subtopic by ID
export const fetchSubtopicById = createAsyncThunk(
  "subtopics/fetchSubtopicById",
  async (id, thunkAPI) => {
    try {
      const res = await axios.get(`${BASE_URL}/${id}`, {
        headers: { Authorization: `Bearer ${getAuthToken()}` },
      });
      return res.data;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// Update subtopic
export const updateSubtopic = createAsyncThunk(
  "subtopics/updateSubtopic",
  async ({ id, subtopicData }, thunkAPI) => {
    try {
      const res = await axios.put(`${BASE_URL}/${id}`, subtopicData, {
        headers: {
          Authorization: `Bearer ${getAuthToken()}`,
          "Content-Type": "application/json",
        },
      });
      return res.data;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// Delete subtopic (handle detail response)
export const deleteSubtopic = createAsyncThunk(
  "subtopics/deleteSubtopic",
  async (id, thunkAPI) => {
    try {
      const res = await axios.delete(`${BASE_URL}/${id}`, {
        headers: { Authorization: `Bearer ${getAuthToken()}` },
      });
      return { id, detail: res.data.detail };
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// Fetch subtopics by topic with pagination
export const fetchSubtopicsByTopic = createAsyncThunk(
  "subtopics/fetchSubtopicsByTopic",
  async ({ topicId, skip = 0, limit = 100 }, thunkAPI) => {
    try {
      const res = await axios.get(
        `${BASE_URL}/topics/${topicId}/subtopics?skip=${skip}&limit=${limit}`,
        {
          headers: { Authorization: `Bearer ${getAuthToken()}` },
        }
      );
      return res.data; // { subtopics: [...], total: N }
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// Initial State
const initialState = {
  subtopics: [],
  total: 0,
  currentSubtopic: null,
  subtopicsByTopic: [],
  subtopicsByTopicTotal: 0,
  loading: false,
  error: null,
  deleteDetail: null,
};

// Slice
const subtopicSlice = createSlice({
  name: "subtopics",
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder
      // Fetch All
      .addCase(fetchSubtopics.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchSubtopics.fulfilled, (state, action) => {
        state.loading = false;
        state.subtopics = action.payload.subtopics;
        state.total = action.payload.total;
      })
      .addCase(fetchSubtopics.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // Create
      .addCase(createSubtopic.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(createSubtopic.fulfilled, (state, action) => {
        state.loading = false;
        state.subtopics.push(action.payload);
      })
      .addCase(createSubtopic.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // Fetch by ID
      .addCase(fetchSubtopicById.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchSubtopicById.fulfilled, (state, action) => {
        state.loading = false;
        state.currentSubtopic = action.payload;
      })
      .addCase(fetchSubtopicById.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // Update
      .addCase(updateSubtopic.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(updateSubtopic.fulfilled, (state, action) => {
        state.loading = false;
        const idx = state.subtopics.findIndex(s => s.id === action.payload.id);
        if (idx !== -1) {
          state.subtopics[idx] = action.payload;
        }
        if (state.currentSubtopic && state.currentSubtopic.id === action.payload.id) {
          state.currentSubtopic = action.payload;
        }
      })
      .addCase(updateSubtopic.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // Delete
      .addCase(deleteSubtopic.pending, (state) => {
        state.loading = true;
        state.error = null;
        state.deleteDetail = null;
      })
      .addCase(deleteSubtopic.fulfilled, (state, action) => {
        state.loading = false;
        state.subtopics = state.subtopics.filter(
          (subtopic) => subtopic.id !== action.payload.id
        );
        state.deleteDetail = action.payload.detail;
      })
      .addCase(deleteSubtopic.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // Fetch subtopics by topic
      .addCase(fetchSubtopicsByTopic.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchSubtopicsByTopic.fulfilled, (state, action) => {
        state.loading = false;
        state.subtopicsByTopic = action.payload.subtopics;
        state.subtopicsByTopicTotal = action.payload.total;
      })
      .addCase(fetchSubtopicsByTopic.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      });
  },
});

export default subtopicSlice.reducer;
