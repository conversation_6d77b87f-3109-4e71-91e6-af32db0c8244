import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import axios from "axios";
import URL from "../api_url/API_URL";

const BASE_URL = `${URL}/api`;
const getAuthToken = () => localStorage.getItem("token");

// Thunks
export const fetchExams = createAsyncThunk(
  "exams/fetchExams",
  async (_, thunkAPI) => {
    try {
      const res = await axios.get(`${BASE_URL}/exams/`, {
        headers: { Authorization: `Bearer ${getAuthToken()}` },
      });
      return res.data;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

export const fetchExamById = createAsyncThunk(
  "exams/fetchExamById",
  async (id, thunkAPI) => {
    try {
      const res = await axios.get(`${BASE_URL}/exams/${id}`, {
        headers: { Authorization: `Bearer ${getAuthToken()}` },
      });
      return res.data;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

export const createExam = createAsyncThunk(
  "exams/createExam",
  async (examData, thunkAPI) => {
    try {
      const res = await axios.post(`${BASE_URL}/exams/`, examData, {
        headers: {
          Authorization: `Bearer ${getAuthToken()}`,
          "Content-Type": "application/json",
        },
      });
      return res.data;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

export const updateExam = createAsyncThunk(
  "exams/updateExam",
  async ({ id, examData }, thunkAPI) => {
    try {
      const res = await axios.put(`${BASE_URL}/exams/${id}`, examData, {
        headers: {
          Authorization: `Bearer ${getAuthToken()}`,
          "Content-Type": "application/json",
        },
      });
      return res.data;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

export const deleteExam = createAsyncThunk(
  "exams/deleteExam",
  async (id, thunkAPI) => {
    try {
      await axios.delete(`${BASE_URL}/exams/${id}`, {
        headers: { Authorization: `Bearer ${getAuthToken()}` },
      });
      return id;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

export const createExamWithAssignment = createAsyncThunk(
  "exams/createExamWithAssignment",
  async (payload, thunkAPI) => {
    try {
      const res = await axios.post(`${BASE_URL}/exams/create-with-assignment`, payload, {
        headers: {
          Authorization: `Bearer ${getAuthToken()}`,
          "Content-Type": "application/json",
        },
      });
      return res.data;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// 1. Get Teacher Exams
export const getTeacherExams = createAsyncThunk(
  "exams/getTeacherExams",
  async (_, thunkAPI) => {
    try {
      const res = await axios.get(`${BASE_URL}/exams/my-exams`, {
        headers: { Authorization: `Bearer ${getAuthToken()}` },
      });
      return res.data;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// 2. Get Student Upcoming Exams
export const getStudentUpcomingExams = createAsyncThunk(
  "exams/getStudentUpcomingExams",
  async (_, thunkAPI) => {
    try {
      const res = await axios.get(`${BASE_URL}/exams/student/upcoming`, {
        headers: { Authorization: `Bearer ${getAuthToken()}` },
      });
      return res.data;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// 3. Get Student Exam by ID
export const getStudentExam = createAsyncThunk(
  "exams/getStudentExam",
  async (exam_id, thunkAPI) => {
    try {
      const res = await axios.get(`${BASE_URL}/exams/student/${exam_id}`, {
        headers: { Authorization: `Bearer ${getAuthToken()}` },
      });
      return res.data;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

const initialState = {
  exams: [],
  currentExam: null,
  loading: false,
  error: null,
  success: null,
  assignmentResult: null,
};

const examSlice = createSlice({
  name: "exams",
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder
      // Fetch all
      .addCase(fetchExams.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchExams.fulfilled, (state, action) => {
        state.loading = false;
        state.exams = action.payload;
      })
      .addCase(fetchExams.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      // Fetch by ID
      .addCase(fetchExamById.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchExamById.fulfilled, (state, action) => {
        state.loading = false;
        state.currentExam = action.payload;
      })
      .addCase(fetchExamById.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      // Create
      .addCase(createExam.pending, (state) => {
        state.loading = true;
        state.error = null;
        state.success = null;
      })
      .addCase(createExam.fulfilled, (state, action) => {
        state.loading = false;
        state.success = "Exam created successfully.";
        state.exams.push(action.payload);
      })
      .addCase(createExam.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      // Update
      .addCase(updateExam.pending, (state) => {
        state.loading = true;
        state.error = null;
        state.success = null;
      })
      .addCase(updateExam.fulfilled, (state, action) => {
        state.loading = false;
        state.success = "Exam updated successfully.";
        const idx = state.exams.findIndex(e => e.id === action.payload.id);
        if (idx !== -1) state.exams[idx] = action.payload;
        if (state.currentExam && state.currentExam.id === action.payload.id) {
          state.currentExam = action.payload;
        }
      })
      .addCase(updateExam.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      // Delete
      .addCase(deleteExam.pending, (state) => {
        state.loading = true;
        state.error = null;
        state.success = null;
      })
      .addCase(deleteExam.fulfilled, (state, action) => {
        state.loading = false;
        state.success = "Exam deleted successfully.";
        state.exams = state.exams.filter(e => e.id !== action.payload);
        if (state.currentExam && state.currentExam.id === action.payload) {
          state.currentExam = null;
        }
      })
      .addCase(deleteExam.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      // Create with assignment
      .addCase(createExamWithAssignment.pending, (state) => {
        state.loading = true;
        state.error = null;
        state.success = null;
        state.assignmentResult = null;
      })
      .addCase(createExamWithAssignment.fulfilled, (state, action) => {
        state.loading = false;
        state.success = "Exam with assignment created successfully.";
        state.assignmentResult = action.payload;
      })
      .addCase(createExamWithAssignment.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
        state.assignmentResult = null;
      })
      // Get Teacher Exams
      .addCase(getTeacherExams.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getTeacherExams.fulfilled, (state, action) => {
        state.loading = false;
        state.exams = action.payload; // Assuming action.payload is the array of exams
      })
      .addCase(getTeacherExams.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      // Get Student Upcoming Exams
      .addCase(getStudentUpcomingExams.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getStudentUpcomingExams.fulfilled, (state, action) => {
        state.loading = false;
        state.exams = action.payload; // Assuming action.payload is the array of exams
      })
      .addCase(getStudentUpcomingExams.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      // Get Student Exam by ID
      .addCase(getStudentExam.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getStudentExam.fulfilled, (state, action) => {
        state.loading = false;
        state.currentExam = action.payload; // Assuming action.payload is the exam object
      })
      .addCase(getStudentExam.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      });
  },
});

export default examSlice.reducer;
