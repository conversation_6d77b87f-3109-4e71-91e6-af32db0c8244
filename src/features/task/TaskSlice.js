import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import axios from "axios";
import URL from "../api_url/API_URL";

const BASE_URL = `${URL}/api/tasks`;
const getAuthToken = () => localStorage.getItem("token");

// === Thunks for provided API endpoints ===

// 1. Create Task For Student
export const createTaskForStudent = createAsyncThunk(
  "tasks/createForStudent",
  async (payload, thunkAPI) => {
    try {
      const res = await axios.post(`${BASE_URL}/for-student`, payload, {
        headers: { Authorization: `Bearer ${getAuthToken()}` },
      });
      return res.data;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// 2. Create Task For Classroom
export const createTaskForClassroom = createAsyncThunk(
  "tasks/createForClassroom",
  async (payload, thunkAPI) => {
    try {
      const res = await axios.post(`${BASE_URL}/for-classroom/`, payload, {
        headers: { Authorization: `Bearer ${getAuthToken()}` },
      });
      return res.data;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// 3. Create Task For Multiple Students
export const createTaskForMultipleStudents = createAsyncThunk(
  "tasks/createForMultipleStudents",
  async (payload, thunkAPI) => {
    try {
      const res = await axios.post(`${BASE_URL}/for-multiple-students`, payload, {
        headers: { Authorization: `Bearer ${getAuthToken()}` },
      });
      return res.data;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// 4. Create Task For Multiple Classrooms
export const createTaskForMultipleClassrooms = createAsyncThunk(
  "tasks/createForMultipleClassrooms",
  async (payload, thunkAPI) => {
    try {
      const res = await axios.post(`${BASE_URL}/for-multiple-classrooms`, payload, {
        headers: { Authorization: `Bearer ${getAuthToken()}` },
      });
      return res.data;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// 5. Assign Task To Multiple Students
export const assignTaskToMultipleStudents = createAsyncThunk(
  "tasks/assignToMultipleStudents",
  async ({ task_id, studentIds }, thunkAPI) => {
    try {
      const res = await axios.post(
        `${BASE_URL}/${task_id}/students/bulk`,
        studentIds,
        { headers: { Authorization: `Bearer ${getAuthToken()}` } }
      );
      return res.data;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// 6. Get Task By Id
export const fetchTaskById = createAsyncThunk(
  "tasks/fetchById",
  async (task_id, thunkAPI) => {
    try {
      const res = await axios.get(`${BASE_URL}/${task_id}`, {
        headers: { Authorization: `Bearer ${getAuthToken()}` },
      });
      return res.data;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// 7. Update Task
export const updateTask = createAsyncThunk(
  "tasks/update",
  async ({ task_id, data }, thunkAPI) => {
    try {
      const res = await axios.put(`${BASE_URL}/${task_id}`, data, {
        headers: { Authorization: `Bearer ${getAuthToken()}` },
      });
      return res.data;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// 8. Delete Task
export const deleteTask = createAsyncThunk(
  "tasks/delete",
  async (task_id, thunkAPI) => {
    try {
      await axios.delete(`${BASE_URL}/${task_id}`, {
        headers: { Authorization: `Bearer ${getAuthToken()}` },
      });
      return task_id;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// 9. Get All Tasks With Filters
export const fetchAllTasksWithFilters = createAsyncThunk(
  "tasks/fetchAllWithFilters",
  async (params, thunkAPI) => {
    try {
      const res = await axios.get(`${BASE_URL}/`, {
        headers: { Authorization: `Bearer ${getAuthToken()}` },
        params,
      });
      return res.data;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// 10. Get Tasks By Classroom
export const fetchTasksByClassroom = createAsyncThunk(
  "tasks/fetchByClassroom",
  async ({ classroom_id, skip = 0, limit = 100 }, thunkAPI) => {
    try {
      const res = await axios.get(`${BASE_URL}/classrooms/${classroom_id}`, {
        headers: { Authorization: `Bearer ${getAuthToken()}` },
        params: { skip, limit },
      });
      return res.data;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// 11. Get Tasks By Student (My Tasks)
export const fetchTasksByStudent = createAsyncThunk(
  "tasks/fetchByStudent",
  async ({ skip = 0, limit = 100 } = {}, thunkAPI) => {
    try {
      const res = await axios.get(`${BASE_URL}/my/tasks`, {
        headers: { Authorization: `Bearer ${getAuthToken()}` },
        params: { skip, limit },
      });
      return res.data;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// 12. Get Students By Task
export const fetchStudentsByTask = createAsyncThunk(
  "tasks/fetchStudentsByTask",
  async ({ task_id, skip = 0, limit = 100 }, thunkAPI) => {
    try {
      const res = await axios.get(`${BASE_URL}/${task_id}/students`, {
        headers: { Authorization: `Bearer ${getAuthToken()}` },
        params: { skip, limit },
      });
      return res.data;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// === Initial State ===
const initialState = {
  tasks: [],
  task: null,
  loading: false,
  error: null,
};

// === Slice ===
const taskSlice = createSlice({
  name: "tasks",
  initialState,
  reducers: {
    clearTaskState: (state) => {
      state.loading = false;
      state.error = null;
      state.task = null;
    },
  },
  extraReducers: (builder) => {
    // Add loading/error/success handling for each thunk
    builder
      // Create Task For Student
      .addCase(createTaskForStudent.pending, (state) => { state.loading = true; state.error = null; })
      .addCase(createTaskForStudent.fulfilled, (state, action) => { state.loading = false; state.tasks.push(action.payload); })
      .addCase(createTaskForStudent.rejected, (state, action) => { state.loading = false; state.error = action.payload; })
      // Create Task For Classroom
      .addCase(createTaskForClassroom.pending, (state) => { state.loading = true; state.error = null; })
      .addCase(createTaskForClassroom.fulfilled, (state, action) => { state.loading = false; state.tasks.push(action.payload); })
      .addCase(createTaskForClassroom.rejected, (state, action) => { state.loading = false; state.error = action.payload; })
      // Create Task For Multiple Students
      .addCase(createTaskForMultipleStudents.pending, (state) => { state.loading = true; state.error = null; })
      .addCase(createTaskForMultipleStudents.fulfilled, (state, action) => { state.loading = false; state.tasks.push(action.payload); })
      .addCase(createTaskForMultipleStudents.rejected, (state, action) => { state.loading = false; state.error = action.payload; })
      // Create Task For Multiple Classrooms
      .addCase(createTaskForMultipleClassrooms.pending, (state) => { state.loading = true; state.error = null; })
      .addCase(createTaskForMultipleClassrooms.fulfilled, (state, action) => { state.loading = false; state.tasks.push(action.payload); })
      .addCase(createTaskForMultipleClassrooms.rejected, (state, action) => { state.loading = false; state.error = action.payload; })
      // Assign Task To Multiple Students
      .addCase(assignTaskToMultipleStudents.pending, (state) => { state.loading = true; state.error = null; })
      .addCase(assignTaskToMultipleStudents.fulfilled, (state) => { state.loading = false; })
      .addCase(assignTaskToMultipleStudents.rejected, (state, action) => { state.loading = false; state.error = action.payload; })
      // Get Task By Id
      .addCase(fetchTaskById.pending, (state) => { state.loading = true; state.error = null; state.task = null; })
      .addCase(fetchTaskById.fulfilled, (state, action) => { state.loading = false; state.task = action.payload; })
      .addCase(fetchTaskById.rejected, (state, action) => { state.loading = false; state.error = action.payload; })
      // Update Task
      .addCase(updateTask.pending, (state) => { state.loading = true; state.error = null; })
      .addCase(updateTask.fulfilled, (state, action) => { state.loading = false; state.task = action.payload; })
      .addCase(updateTask.rejected, (state, action) => { state.loading = false; state.error = action.payload; })
      // Delete Task
      .addCase(deleteTask.pending, (state) => { state.loading = true; state.error = null; })
      .addCase(deleteTask.fulfilled, (state, action) => { state.loading = false; state.tasks = state.tasks.filter(t => t.id !== action.payload); })
      .addCase(deleteTask.rejected, (state, action) => { state.loading = false; state.error = action.payload; })
      // Get All Tasks With Filters
      .addCase(fetchAllTasksWithFilters.pending, (state) => { state.loading = true; state.error = null; })
      .addCase(fetchAllTasksWithFilters.fulfilled, (state, action) => { state.loading = false; state.tasks = action.payload?.tasks || []; })
      .addCase(fetchAllTasksWithFilters.rejected, (state, action) => { state.loading = false; state.error = action.payload; })
      // Get Tasks By Classroom
      .addCase(fetchTasksByClassroom.pending, (state) => { state.loading = true; state.error = null; })
      .addCase(fetchTasksByClassroom.fulfilled, (state, action) => { state.loading = false; state.tasks = action.payload?.tasks || []; })
      .addCase(fetchTasksByClassroom.rejected, (state, action) => { state.loading = false; state.error = action.payload; })
      // Get Tasks By Student (My Tasks)
      .addCase(fetchTasksByStudent.pending, (state) => { state.loading = true; state.error = null; })
      .addCase(fetchTasksByStudent.fulfilled, (state, action) => { state.loading = false; state.tasks = action.payload?.tasks || []; })
      .addCase(fetchTasksByStudent.rejected, (state, action) => { state.loading = false; state.error = action.payload; })
      // Get Students By Task
      .addCase(fetchStudentsByTask.pending, (state) => { state.loading = true; state.error = null; })
      .addCase(fetchStudentsByTask.fulfilled, (state, action) => { state.loading = false; state.task = { ...state.task, students: action.payload } })
      .addCase(fetchStudentsByTask.rejected, (state, action) => { state.loading = false; state.error = action.payload; });
  },
});

export const { clearTaskState } = taskSlice.actions;
export default taskSlice.reducer;
