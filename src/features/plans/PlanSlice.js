import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import axios from "axios";
import URL from "../api_url/API_URL";

const BASE_URL = `${URL}/api/plans`;
const getAuthToken = () => localStorage.getItem("token");

// Thunks

// Create a new plan
export const createPlan = createAsyncThunk(
  "plans/createPlan",
  async (data, thunkAPI) => {
    try {
      const res = await axios.post(`${BASE_URL}/`, data, {
        headers: {
          Authorization: `Bearer ${getAuthToken()}`,
          "Content-Type": "application/json",
        },
      });
      return res.data;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// Get all plans
export const fetchPlans = createAsyncThunk(
  "plans/fetchPlans",
  async (params = { skip: 0, limit: 100, active_only: false }, thunkAPI) => {
    try {
      const { skip, limit, active_only } = params;
      const res = await axios.get(`${BASE_URL}/?skip=${skip}&limit=${limit}&active_only=${active_only}`, {
        headers: { Authorization: `Bearer ${getAuthToken()}` },
      });
      return res.data;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// Get plan by ID
export const fetchPlanById = createAsyncThunk(
  "plans/fetchPlanById",
  async (id, thunkAPI) => {
    try {
      const res = await axios.get(`${BASE_URL}/${id}`, {
        headers: { Authorization: `Bearer ${getAuthToken()}` },
      });
      return res.data;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// Update plan by ID
export const updatePlan = createAsyncThunk(
  "plans/updatePlan",
  async ({ id, data }, thunkAPI) => {
    try {
      const res = await axios.put(`${BASE_URL}/${id}`, data, {
        headers: {
          Authorization: `Bearer ${getAuthToken()}`,
          "Content-Type": "application/json",
        },
      });
      return res.data;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// Delete plan by ID
export const deletePlan = createAsyncThunk(
  "plans/deletePlan",
  async (id, thunkAPI) => {
    try {
      const res = await axios.delete(`${BASE_URL}/${id}`, {
        headers: { Authorization: `Bearer ${getAuthToken()}` },
      });
      return res.data;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// Get plan by name
export const fetchPlanByName = createAsyncThunk(
  "plans/fetchPlanByName",
  async (name, thunkAPI) => {
    try {
      const res = await axios.get(`${BASE_URL}/by-name/${name}`, {
        headers: { Authorization: `Bearer ${getAuthToken()}` },
      });
      return res.data;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// Get active plans
export const fetchActivePlans = createAsyncThunk(
  "plans/fetchActivePlans",
  async (_, thunkAPI) => {
    try {
      const res = await axios.get(`${BASE_URL}/active/`, {
        headers: { Authorization: `Bearer ${getAuthToken()}` },
      });
      return res.data;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// Get plans with subscription count
export const fetchPlansWithSubscriptionCount = createAsyncThunk(
  "plans/fetchPlansWithSubscriptionCount",
  async (_, thunkAPI) => {
    try {
      const res = await axios.get(`${BASE_URL}/with-subscription-count/`, {
        headers: { Authorization: `Bearer ${getAuthToken()}` },
      });
      return res.data;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// Activate plan
export const activatePlan = createAsyncThunk(
  "plans/activatePlan",
  async (id, thunkAPI) => {
    try {
      const res = await axios.patch(`${BASE_URL}/${id}/activate`, {}, {
        headers: { Authorization: `Bearer ${getAuthToken()}` },
      });
      return res.data;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// Deactivate plan
export const deactivatePlan = createAsyncThunk(
  "plans/deactivatePlan",
  async (id, thunkAPI) => {
    try {
      const res = await axios.patch(`${BASE_URL}/${id}/deactivate`, {}, {
        headers: { Authorization: `Bearer ${getAuthToken()}` },
      });
      return res.data;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// Get plan statistics
export const fetchPlanStatistics = createAsyncThunk(
  "plans/fetchPlanStatistics",
  async (_, thunkAPI) => {
    try {
      const res = await axios.get(`${BASE_URL}/statistics/`, {
        headers: { Authorization: `Bearer ${getAuthToken()}` },
      });
      return res.data;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// Initial State
const initialState = {
  plans: [],
  plan: null,
  activePlans: [],
  plansWithSubscriptionCount: [],
  statistics: {},
  loading: false,
  error: null,
  success: null,
};

const planSlice = createSlice({
  name: "plans",
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder
      // Create
      .addCase(createPlan.pending, (state) => {
        state.loading = true;
        state.error = null;
        state.success = null;
      })
      .addCase(createPlan.fulfilled, (state, action) => {
        state.loading = false;
        state.success = "Plan created successfully.";
        state.plan = action.payload;
      })
      .addCase(createPlan.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // Fetch all
      .addCase(fetchPlans.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchPlans.fulfilled, (state, action) => {
        state.loading = false;
        state.plans = action.payload;
      })
      .addCase(fetchPlans.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // Fetch by ID
      .addCase(fetchPlanById.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchPlanById.fulfilled, (state, action) => {
        state.loading = false;
        state.plan = action.payload;
      })
      .addCase(fetchPlanById.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // Update
      .addCase(updatePlan.pending, (state) => {
        state.loading = true;
        state.error = null;
        state.success = null;
      })
      .addCase(updatePlan.fulfilled, (state, action) => {
        state.loading = false;
        state.success = "Plan updated successfully.";
        state.plan = action.payload;
      })
      .addCase(updatePlan.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // Delete
      .addCase(deletePlan.pending, (state) => {
        state.loading = true;
        state.error = null;
        state.success = null;
      })
      .addCase(deletePlan.fulfilled, (state, action) => {
        state.loading = false;
        state.success = "Plan deleted successfully.";
        state.plan = null;
      })
      .addCase(deletePlan.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // Fetch by name
      .addCase(fetchPlanByName.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchPlanByName.fulfilled, (state, action) => {
        state.loading = false;
        state.plan = action.payload;
      })
      .addCase(fetchPlanByName.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // Fetch active
      .addCase(fetchActivePlans.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchActivePlans.fulfilled, (state, action) => {
        state.loading = false;
        state.activePlans = action.payload;
      })
      .addCase(fetchActivePlans.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // Fetch with subscription count
      .addCase(fetchPlansWithSubscriptionCount.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchPlansWithSubscriptionCount.fulfilled, (state, action) => {
        state.loading = false;
        state.plansWithSubscriptionCount = action.payload;
      })
      .addCase(fetchPlansWithSubscriptionCount.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // Activate
      .addCase(activatePlan.pending, (state) => {
        state.loading = true;
        state.error = null;
        state.success = null;
      })
      .addCase(activatePlan.fulfilled, (state, action) => {
        state.loading = false;
        state.success = "Plan activated successfully.";
        state.plan = action.payload;
      })
      .addCase(activatePlan.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // Deactivate
      .addCase(deactivatePlan.pending, (state) => {
        state.loading = true;
        state.error = null;
        state.success = null;
      })
      .addCase(deactivatePlan.fulfilled, (state, action) => {
        state.loading = false;
        state.success = "Plan deactivated successfully.";
        state.plan = action.payload;
      })
      .addCase(deactivatePlan.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // Statistics
      .addCase(fetchPlanStatistics.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchPlanStatistics.fulfilled, (state, action) => {
        state.loading = false;
        state.statistics = action.payload;
      })
      .addCase(fetchPlanStatistics.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      });
  },
});

export default planSlice.reducer;
