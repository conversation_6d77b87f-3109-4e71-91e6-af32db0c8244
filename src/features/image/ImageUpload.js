// src/api/uploadImage.js
import { ref, uploadBytes, getDownloadURL } from "firebase/storage";
import { storage } from "../../firebase/Firebase";

/**
 * Uploads an image to Firebase Storage and returns the download URL.
 * @param {File} image - The image file to upload.
 * @returns {Promise<string>} - The download URL of the uploaded image.
 */
export const uploadImageToFirebase = async (image) => {
  if (!image) throw new Error("No image file provided");

  const imageRef = ref(storage, `images/${Date.now()}_${image.name}`);

  // Upload the image to Firebase
  const snapshot = await uploadBytes(imageRef, image);

  // Get the image's public download URL
  const downloadURL = await getDownloadURL(snapshot.ref);

  return downloadURL;
};
