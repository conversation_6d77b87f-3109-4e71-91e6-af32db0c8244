/**
 * Performance optimization utilities for React components
 * Provides helpers for memoization, debouncing, and performance monitoring
 */

import { useCallback, useMemo, useRef, useEffect, useState } from 'react';
import logger from './logger';

/**
 * Custom hook for debouncing values
 * Useful for search inputs and API calls
 */
export const useDebounce = (value, delay) => {
  const [debouncedValue, setDebouncedValue] = useState(value);

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);

  return debouncedValue;
};

/**
 * Custom hook for throttling function calls
 * Useful for scroll handlers and resize events
 */
export const useThrottle = (callback, delay) => {
  const lastRun = useRef(Date.now());

  return useCallback(
    (...args) => {
      if (Date.now() - lastRun.current >= delay) {
        callback(...args);
        lastRun.current = Date.now();
      }
    },
    [callback, delay]
  );
};

/**
 * Custom hook for memoizing expensive calculations
 * Automatically logs performance metrics in development
 */
export const useExpensiveMemo = (factory, deps, operationName = 'calculation') => {
  return useMemo(() => {
    const start = performance.now();
    const result = factory();
    const duration = performance.now() - start;
    
    if (duration > 10) { // Log if calculation takes more than 10ms
      logger.performance(operationName, duration);
    }
    
    return result;
  }, deps);
};

/**
 * Custom hook for stable callback references
 * Prevents unnecessary re-renders in child components
 */
export const useStableCallback = (callback, deps) => {
  return useCallback(callback, deps);
};

/**
 * Custom hook for monitoring component render performance
 */
export const useRenderPerformance = (componentName) => {
  const renderCount = useRef(0);
  const lastRenderTime = useRef(performance.now());

  useEffect(() => {
    renderCount.current += 1;
    const currentTime = performance.now();
    const timeSinceLastRender = currentTime - lastRenderTime.current;
    
    if (renderCount.current > 1 && timeSinceLastRender < 16) {
      logger.warn(
        `Potential performance issue: ${componentName} re-rendered after ${timeSinceLastRender.toFixed(2)}ms`,
        { renderCount: renderCount.current },
        componentName
      );
    }
    
    lastRenderTime.current = currentTime;
  });

  return renderCount.current;
};

/**
 * Performance monitoring hook for components
 * Use this directly in your component instead of the HOC
 */
export const usePerformanceMonitoring = (componentName) => {
  return useRenderPerformance(componentName);
};

/**
 * Custom hook for lazy loading data
 */
export const useLazyLoad = (loadFunction, dependencies = []) => {
  const [data, setData] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const hasLoaded = useRef(false);

  const load = useCallback(async () => {
    if (hasLoaded.current) return;
    
    setLoading(true);
    setError(null);
    
    try {
      const start = performance.now();
      const result = await loadFunction();
      const duration = performance.now() - start;
      
      logger.performance('Lazy load operation', duration);
      setData(result);
      hasLoaded.current = true;
    } catch (err) {
      setError(err);
      logger.error('Lazy load failed', err);
    } finally {
      setLoading(false);
    }
  }, dependencies);

  return { data, loading, error, load };
};

/**
 * Custom hook for intersection observer (lazy loading images, infinite scroll)
 */
export const useIntersectionObserver = (options = {}) => {
  const [isIntersecting, setIsIntersecting] = useState(false);
  const [hasIntersected, setHasIntersected] = useState(false);
  const elementRef = useRef(null);

  useEffect(() => {
    const element = elementRef.current;
    if (!element) return;

    const observer = new IntersectionObserver(
      ([entry]) => {
        setIsIntersecting(entry.isIntersecting);
        if (entry.isIntersecting && !hasIntersected) {
          setHasIntersected(true);
        }
      },
      {
        threshold: 0.1,
        rootMargin: '50px',
        ...options,
      }
    );

    observer.observe(element);

    return () => {
      observer.unobserve(element);
    };
  }, [hasIntersected, options]);

  return { elementRef, isIntersecting, hasIntersected };
};

/**
 * Custom hook for virtual scrolling
 */
export const useVirtualScroll = (items, itemHeight, containerHeight) => {
  const [scrollTop, setScrollTop] = useState(0);
  
  const visibleItems = useMemo(() => {
    const startIndex = Math.floor(scrollTop / itemHeight);
    const endIndex = Math.min(
      startIndex + Math.ceil(containerHeight / itemHeight) + 1,
      items.length
    );
    
    return {
      startIndex,
      endIndex,
      items: items.slice(startIndex, endIndex),
      totalHeight: items.length * itemHeight,
      offsetY: startIndex * itemHeight,
    };
  }, [items, itemHeight, containerHeight, scrollTop]);

  const handleScroll = useCallback((e) => {
    setScrollTop(e.target.scrollTop);
  }, []);

  return { visibleItems, handleScroll };
};

/**
 * Performance optimization for Redux selectors
 */
export const createMemoizedSelector = (selector, equalityFn) => {
  let lastArgs = null;
  let lastResult = null;

  return (...args) => {
    if (lastArgs === null || !equalityFn(args, lastArgs)) {
      lastArgs = args;
      lastResult = selector(...args);
    }
    return lastResult;
  };
};

/**
 * Shallow equality check for props
 */
export const shallowEqual = (obj1, obj2) => {
  const keys1 = Object.keys(obj1);
  const keys2 = Object.keys(obj2);

  if (keys1.length !== keys2.length) {
    return false;
  }

  for (let key of keys1) {
    if (obj1[key] !== obj2[key]) {
      return false;
    }
  }

  return true;
};

/**
 * Deep equality check for complex objects
 */
export const deepEqual = (obj1, obj2) => {
  if (obj1 === obj2) return true;
  
  if (obj1 == null || obj2 == null) return false;
  
  if (typeof obj1 !== typeof obj2) return false;
  
  if (typeof obj1 !== 'object') return obj1 === obj2;
  
  const keys1 = Object.keys(obj1);
  const keys2 = Object.keys(obj2);
  
  if (keys1.length !== keys2.length) return false;
  
  for (let key of keys1) {
    if (!keys2.includes(key)) return false;
    if (!deepEqual(obj1[key], obj2[key])) return false;
  }
  
  return true;
};

/**
 * Bundle size analyzer helper
 */
export const analyzeBundleSize = () => {
  if (process.env.NODE_ENV === 'development') {
    const scripts = Array.from(document.querySelectorAll('script[src]'));
    const totalSize = scripts.reduce((total, script) => {
      // This is a rough estimation - in a real app you'd use webpack-bundle-analyzer
      return total + (script.src.length * 100); // Rough estimate
    }, 0);
    
    logger.info('Estimated bundle size', { 
      scripts: scripts.length, 
      estimatedSize: `${(totalSize / 1024).toFixed(2)}KB` 
    });
  }
};

/**
 * Memory usage monitor
 */
export const monitorMemoryUsage = () => {
  if (process.env.NODE_ENV === 'development' && 'memory' in performance) {
    const memInfo = performance.memory;
    logger.info('Memory usage', {
      used: `${(memInfo.usedJSHeapSize / 1024 / 1024).toFixed(2)}MB`,
      total: `${(memInfo.totalJSHeapSize / 1024 / 1024).toFixed(2)}MB`,
      limit: `${(memInfo.jsHeapSizeLimit / 1024 / 1024).toFixed(2)}MB`,
    });
  }
};

export default {
  useDebounce,
  useThrottle,
  useExpensiveMemo,
  useStableCallback,
  useRenderPerformance,
  usePerformanceMonitoring,
  useLazyLoad,
  useIntersectionObserver,
  useVirtualScroll,
  createMemoizedSelector,
  shallowEqual,
  deepEqual,
  analyzeBundleSize,
  monitorMemoryUsage,
};
