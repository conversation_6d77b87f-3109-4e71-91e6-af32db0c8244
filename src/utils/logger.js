/**
 * Centralized logging utility for the EduFair application
 * Provides consistent logging across development and production environments
 */

// Log levels
const LOG_LEVELS = {
  ERROR: 0,
  WARN: 1,
  INFO: 2,
  DEBUG: 3,
};

// Current log level based on environment
const CURRENT_LOG_LEVEL = process.env.NODE_ENV === 'production' 
  ? LOG_LEVELS.ERROR 
  : LOG_LEVELS.DEBUG;

// Color codes for console output
const COLORS = {
  ERROR: '\x1b[31m', // Red
  WARN: '\x1b[33m',  // Yellow
  INFO: '\x1b[36m',  // Cyan
  DEBUG: '\x1b[37m', // White
  RESET: '\x1b[0m',  // Reset
};

/**
 * Format log message with timestamp and context
 */
const formatMessage = (level, message, context = '') => {
  const timestamp = new Date().toISOString();
  const contextStr = context ? ` [${context}]` : '';
  return `${timestamp} [${level}]${contextStr} ${message}`;
};

/**
 * Send logs to external service in production
 */
const sendToExternalService = (level, message, data, context) => {
  // In a real application, you would send logs to services like:
  // - Sentry for error tracking
  // - LogRocket for session replay
  // - DataDog for monitoring
  // - Custom analytics endpoint
  
  if (process.env.NODE_ENV === 'production' && level === 'ERROR') {
    // Example: Send to error tracking service
    // errorTrackingService.captureException(new Error(message), { extra: data, tags: { context } });
  }
};

/**
 * Core logging function
 */
const log = (level, levelValue, message, data = null, context = '') => {
  if (levelValue > CURRENT_LOG_LEVEL) return;

  const formattedMessage = formatMessage(level, message, context);
  
  if (process.env.NODE_ENV === 'development') {
    const color = COLORS[level] || COLORS.DEBUG;
    const resetColor = COLORS.RESET;
    
    if (data) {
      console.group(`${color}${formattedMessage}${resetColor}`);
      console.log('Data:', data);
      console.groupEnd();
    } else {
      console.log(`${color}${formattedMessage}${resetColor}`);
    }
  }
  
  // Send to external service if needed
  sendToExternalService(level, message, data, context);
};

/**
 * Logger object with different log levels
 */
const logger = {
  /**
   * Log error messages
   * @param {string} message - Error message
   * @param {any} error - Error object or additional data
   * @param {string} context - Context where error occurred (component name, function, etc.)
   */
  error: (message, error = null, context = '') => {
    log('ERROR', LOG_LEVELS.ERROR, message, error, context);
  },

  /**
   * Log warning messages
   * @param {string} message - Warning message
   * @param {any} data - Additional data
   * @param {string} context - Context where warning occurred
   */
  warn: (message, data = null, context = '') => {
    log('WARN', LOG_LEVELS.WARN, message, data, context);
  },

  /**
   * Log informational messages
   * @param {string} message - Info message
   * @param {any} data - Additional data
   * @param {string} context - Context where info was logged
   */
  info: (message, data = null, context = '') => {
    log('INFO', LOG_LEVELS.INFO, message, data, context);
  },

  /**
   * Log debug messages (development only)
   * @param {string} message - Debug message
   * @param {any} data - Additional data
   * @param {string} context - Context where debug was logged
   */
  debug: (message, data = null, context = '') => {
    log('DEBUG', LOG_LEVELS.DEBUG, message, data, context);
  },

  /**
   * Log API requests and responses
   * @param {string} method - HTTP method
   * @param {string} url - Request URL
   * @param {any} requestData - Request payload
   * @param {any} responseData - Response data
   * @param {number} status - HTTP status code
   */
  api: (method, url, requestData = null, responseData = null, status = null) => {
    const message = `${method.toUpperCase()} ${url}${status ? ` (${status})` : ''}`;
    const data = {
      request: requestData,
      response: responseData,
      status,
    };
    log('INFO', LOG_LEVELS.INFO, message, data, 'API');
  },

  /**
   * Log user interactions for analytics
   * @param {string} action - User action
   * @param {any} data - Additional data about the action
   * @param {string} component - Component where action occurred
   */
  userAction: (action, data = null, component = '') => {
    const message = `User action: ${action}`;
    log('INFO', LOG_LEVELS.INFO, message, data, component);
  },

  /**
   * Log performance metrics
   * @param {string} operation - Operation being measured
   * @param {number} duration - Duration in milliseconds
   * @param {any} data - Additional performance data
   */
  performance: (operation, duration, data = null) => {
    const message = `Performance: ${operation} took ${duration}ms`;
    log('INFO', LOG_LEVELS.INFO, message, data, 'PERFORMANCE');
  },

  /**
   * Create a scoped logger for a specific component or module
   * @param {string} scope - Scope name (component name, module name, etc.)
   */
  scope: (scope) => ({
    error: (message, error = null) => logger.error(message, error, scope),
    warn: (message, data = null) => logger.warn(message, data, scope),
    info: (message, data = null) => logger.info(message, data, scope),
    debug: (message, data = null) => logger.debug(message, data, scope),
  }),
};

// Helper function to measure execution time
export const measureTime = (operation, fn) => {
  const start = performance.now();
  const result = fn();
  const duration = performance.now() - start;
  
  if (result instanceof Promise) {
    return result.finally(() => {
      logger.performance(operation, duration);
    });
  } else {
    logger.performance(operation, duration);
    return result;
  }
};

// Helper function for async operations
export const measureTimeAsync = async (operation, asyncFn) => {
  const start = performance.now();
  try {
    const result = await asyncFn();
    const duration = performance.now() - start;
    logger.performance(operation, duration);
    return result;
  } catch (error) {
    const duration = performance.now() - start;
    logger.performance(`${operation} (failed)`, duration);
    throw error;
  }
};

export default logger;
