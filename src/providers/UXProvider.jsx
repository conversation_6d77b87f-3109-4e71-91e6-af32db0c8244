import React, { createContext, useContext } from 'react';
import { ToastProvider } from '../components/ui/Toast';
import ErrorBoundary from '../components/ui/ErrorBoundary';

// UX Context for global UX state and utilities
const UXContext = createContext();

// Global UX configuration
const UX_CONFIG = {
  // Animation preferences
  animations: {
    enabled: true,
    duration: 200,
    easing: 'ease-in-out'
  },
  
  // Loading states
  loading: {
    skeletonEnabled: true,
    minimumLoadingTime: 300, // Prevent flash of loading state
    timeoutDuration: 30000 // 30 seconds timeout
  },
  
  // Form validation
  forms: {
    validateOnBlur: true,
    validateOnChange: true,
    showCharacterCount: true,
    autoSave: {
      enabled: true,
      delay: 2000
    }
  },
  
  // Mobile optimizations
  mobile: {
    touchTargetSize: 44, // Minimum touch target size in pixels
    swipeGestures: true,
    hapticFeedback: true
  },
  
  // Accessibility
  a11y: {
    focusVisible: true,
    screenReaderAnnouncements: true,
    highContrast: false,
    reducedMotion: false
  },
  
  // Error handling
  errors: {
    showRetryButton: true,
    maxRetries: 3,
    retryDelay: 1000,
    showErrorDetails: process.env.NODE_ENV === 'development'
  }
};

// UX Provider component
export const UXProvider = ({ children, config = {} }) => {
  const mergedConfig = { ...UX_CONFIG, ...config };
  
  // Global UX utilities
  const uxUtils = {
    // Check if device is mobile
    isMobile: () => {
      return window.innerWidth <= 768 || 
             /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
    },
    
    // Check if user prefers reduced motion
    prefersReducedMotion: () => {
      return window.matchMedia('(prefers-reduced-motion: reduce)').matches;
    },
    
    // Check if device supports touch
    isTouchDevice: () => {
      return 'ontouchstart' in window || navigator.maxTouchPoints > 0;
    },
    
    // Get optimal loading strategy based on device
    getLoadingStrategy: () => {
      const isMobile = uxUtils.isMobile();
      const isSlowConnection = navigator.connection?.effectiveType === 'slow-2g' || 
                              navigator.connection?.effectiveType === '2g';
      
      return {
        useSkeletons: mergedConfig.loading.skeletonEnabled && !isSlowConnection,
        showProgressBars: isMobile || isSlowConnection,
        minimumLoadingTime: isSlowConnection ? 0 : mergedConfig.loading.minimumLoadingTime
      };
    },
    
    // Get form configuration based on device
    getFormConfig: () => {
      const isMobile = uxUtils.isMobile();
      
      return {
        ...mergedConfig.forms,
        autoSave: {
          ...mergedConfig.forms.autoSave,
          enabled: mergedConfig.forms.autoSave.enabled && !isMobile // Disable auto-save on mobile to save battery
        },
        validateOnChange: isMobile ? false : mergedConfig.forms.validateOnChange // Less aggressive validation on mobile
      };
    },
    
    // Announce to screen readers
    announce: (message, priority = 'polite') => {
      if (!mergedConfig.a11y.screenReaderAnnouncements) return;
      
      const announcement = document.createElement('div');
      announcement.setAttribute('aria-live', priority);
      announcement.setAttribute('aria-atomic', 'true');
      announcement.className = 'sr-only';
      announcement.textContent = message;
      
      document.body.appendChild(announcement);
      
      setTimeout(() => {
        document.body.removeChild(announcement);
      }, 1000);
    },
    
    // Haptic feedback for mobile
    hapticFeedback: (type = 'light') => {
      if (!mergedConfig.mobile.hapticFeedback || !uxUtils.isTouchDevice()) return;
      
      if (navigator.vibrate) {
        const patterns = {
          light: [10],
          medium: [20],
          heavy: [30],
          success: [10, 50, 10],
          error: [50, 50, 50]
        };
        
        navigator.vibrate(patterns[type] || patterns.light);
      }
    },
    
    // Focus management
    focusManagement: {
      // Trap focus within an element
      trapFocus: (element) => {
        const focusableElements = element.querySelectorAll(
          'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
        );
        
        const firstElement = focusableElements[0];
        const lastElement = focusableElements[focusableElements.length - 1];
        
        const handleTabKey = (e) => {
          if (e.key === 'Tab') {
            if (e.shiftKey) {
              if (document.activeElement === firstElement) {
                lastElement.focus();
                e.preventDefault();
              }
            } else {
              if (document.activeElement === lastElement) {
                firstElement.focus();
                e.preventDefault();
              }
            }
          }
        };
        
        element.addEventListener('keydown', handleTabKey);
        
        return () => {
          element.removeEventListener('keydown', handleTabKey);
        };
      },
      
      // Return focus to previous element
      returnFocus: (previousElement) => {
        if (previousElement && typeof previousElement.focus === 'function') {
          previousElement.focus();
        }
      }
    }
  };
  
  const contextValue = {
    config: mergedConfig,
    utils: uxUtils
  };
  
  return (
    <UXContext.Provider value={contextValue}>
      <ErrorBoundary>
        <ToastProvider>
          {children}
        </ToastProvider>
      </ErrorBoundary>
    </UXContext.Provider>
  );
};

// Hook to use UX context
export const useUX = () => {
  const context = useContext(UXContext);
  if (!context) {
    throw new Error('useUX must be used within a UXProvider');
  }
  return context;
};

// Higher-order component for UX enhancements
export const withUXEnhancements = (Component) => {
  return React.forwardRef((props, ref) => {
    const { utils } = useUX();
    
    return (
      <Component
        ref={ref}
        {...props}
        uxUtils={utils}
        isMobile={utils.isMobile()}
        isTouchDevice={utils.isTouchDevice()}
        prefersReducedMotion={utils.prefersReducedMotion()}
      />
    );
  });
};

// UX-aware component wrapper
export const UXAware = ({ children, className = '', ...props }) => {
  const { utils, config } = useUX();
  
  const enhancedClassName = `
    ${className}
    ${utils.isMobile() ? 'mobile-optimized' : ''}
    ${utils.isTouchDevice() ? 'touch-optimized' : ''}
    ${utils.prefersReducedMotion() ? 'reduced-motion' : ''}
    ${config.a11y.focusVisible ? 'focus-visible' : ''}
  `.trim();
  
  return (
    <div className={enhancedClassName} {...props}>
      {children}
    </div>
  );
};

export default UXProvider;
