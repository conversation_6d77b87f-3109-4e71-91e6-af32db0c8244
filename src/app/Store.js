import { configureStore } from '@reduxjs/toolkit';
import loginReducer from '../features/auth/login/LoginSlice';
import signupReducer from "../features/auth/signup/SignupSlice";
import userReducer from "../features/users/userSlice";
import classroomReducer from "../features/classroom/ClassroomSlice";
import subjectReducer from "../features/subjects/SubjectSlice";
import chapterReducer from "../features/chapters/ChapterSlice";
import topicReducer from "../features/topics/TopicSlice";
import subtopicReducer from "../features/subtopics/SubtopicSlice";
import taskReducer from "../features/task/TaskSlice";
import teacherReducer from "../features/teacher/TeacherSlice";
import planReducer from "../features/plans/PlanSlice";
import announcementReducer from "../features/announcements/AnnouncementSlice";
import teacherSubscriptionReducer from "../features/teacher/TeacherSubscriptionSlice";
import questionReducer from "../features/question/QuestionSlice";
import examReducer from "../features/exams/ExamSlice";

const store = configureStore({
  reducer: {
    login: loginReducer,
    signup: signupReducer,
    users: userReducer,
    classroom: classroomReducer,
    subjects: subjectReducer,
    chapters: chapterReducer,
    topics: topicReducer,
    subtopics: subtopicReducer,
    tasks: taskReducer,
    teacher: teacherReducer,
    plans: planReducer,
    announcements: announcementReducer,
    teacherSubscription: teacherSubscriptionReducer,
    questions: questionReducer,
    exams: examReducer,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: ['persist/PERSIST', 'persist/REHYDRATE'],
        ignoredPaths: ['register', 'rehydrate'],
      },
    }),
  devTools: process.env.NODE_ENV !== 'production',
});

export default store;