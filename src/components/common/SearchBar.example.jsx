import React, { useState } from 'react';
import { SearchBar } from './index';

// Example usage of the SearchBar component
const SearchBarExample = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [searchResults, setSearchResults] = useState([]);

  // Handle search input change
  const handleSearchChange = (value) => {
    setSearchTerm(value);
    // Perform real-time filtering here
    console.log('Search changed:', value);
  };

  // Handle search submission (Enter key pressed)
  const handleSearchSubmit = (value) => {
    console.log('Search submitted:', value);
    // Perform search action here
    // Example: API call, navigation, etc.
    setSearchResults([`Result for: ${value}`]);
  };

  // Handle search clear
  const handleSearchClear = () => {
    setSearchTerm('');
    setSearchResults([]);
    console.log('Search cleared');
  };

  return (
    <div className="p-6 space-y-6">
      <h2 className="text-2xl font-bold">SearchBar Component Examples</h2>
      
      {/* Basic Usage */}
      <div>
        <h3 className="text-lg font-semibold mb-2">Basic Usage</h3>
        <SearchBar
          value={searchTerm}
          onChange={handleSearchChange}
          onSearch={handleSearchSubmit}
          onClear={handleSearchClear}
          placeholder="Search anything..."
        />
      </div>

      {/* With Results Count */}
      <div>
        <h3 className="text-lg font-semibold mb-2">With Results Count</h3>
        <SearchBar
          value={searchTerm}
          onChange={handleSearchChange}
          onSearch={handleSearchSubmit}
          onClear={handleSearchClear}
          placeholder="Search with results count..."
          showResultsCount={true}
          resultsCount={searchResults.length}
          resultsType="results"
        />
      </div>

      {/* Small Size */}
      <div>
        <h3 className="text-lg font-semibold mb-2">Small Size</h3>
        <SearchBar
          value={searchTerm}
          onChange={handleSearchChange}
          onSearch={handleSearchSubmit}
          onClear={handleSearchClear}
          placeholder="Small search bar..."
          size="small"
          showKeyboardShortcut={false}
        />
      </div>

      {/* Large Size */}
      <div>
        <h3 className="text-lg font-semibold mb-2">Large Size</h3>
        <SearchBar
          value={searchTerm}
          onChange={handleSearchChange}
          onSearch={handleSearchSubmit}
          onClear={handleSearchClear}
          placeholder="Large search bar..."
          size="large"
        />
      </div>

      {/* Disabled State */}
      <div>
        <h3 className="text-lg font-semibold mb-2">Disabled State</h3>
        <SearchBar
          value="Disabled search"
          placeholder="This is disabled..."
          disabled={true}
        />
      </div>

      {/* Auto Focus */}
      <div>
        <h3 className="text-lg font-semibold mb-2">Auto Focus</h3>
        <SearchBar
          value={searchTerm}
          onChange={handleSearchChange}
          onSearch={handleSearchSubmit}
          onClear={handleSearchClear}
          placeholder="Auto focused search..."
          autoFocus={true}
          showKeyboardShortcut={false}
        />
      </div>

      {/* Search Results Display */}
      {searchResults.length > 0 && (
        <div>
          <h3 className="text-lg font-semibold mb-2">Search Results</h3>
          <ul className="list-disc list-inside">
            {searchResults.map((result, index) => (
              <li key={index}>{result}</li>
            ))}
          </ul>
        </div>
      )}
    </div>
  );
};

export default SearchBarExample;
