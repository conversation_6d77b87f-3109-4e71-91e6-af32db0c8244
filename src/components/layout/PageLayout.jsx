import React, { useState, useEffect } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import SkeletonLoader from '../ui/SkeletonLoader';
import ErrorStates from '../ui/ErrorStates';
import { useUX } from '../../providers/UXProvider';
import { FiHome, FiChevronRight, FiRefreshCw, FiSearch, FiMenu } from 'react-icons/fi';

// Breadcrumb component
const Breadcrumb = ({ items = [], className = '' }) => {
  const navigate = useNavigate();
  
  if (items.length === 0) return null;
  
  return (
    <nav className={`flex items-center space-x-2 text-sm ${className}`} aria-label="Breadcrumb">
      <button
        onClick={() => navigate('/')}
        className="flex items-center text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 transition-colors"
        aria-label="Go to home"
      >
        <FiHome className="w-4 h-4" />
      </button>
      
      {items.map((item, index) => (
        <React.Fragment key={index}>
          <FiChevronRight className="w-4 h-4 text-gray-400" />
          {item.path && index < items.length - 1 ? (
            <button
              onClick={() => navigate(item.path)}
              className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 transition-colors"
            >
              {item.label}
            </button>
          ) : (
            <span className="text-gray-900 dark:text-gray-100 font-medium">
              {item.label}
            </span>
          )}
        </React.Fragment>
      ))}
    </nav>
  );
};

// Page header component
const PageHeader = ({
  title,
  subtitle,
  breadcrumbs = [],
  actions = [],
  onRefresh,
  isLoading = false,
  className = ''
}) => {
  const { utils } = useUX();
  
  return (
    <div className={`space-y-4 ${className}`}>
      {/* Breadcrumbs */}
      {breadcrumbs.length > 0 && (
        <Breadcrumb items={breadcrumbs} />
      )}
      
      {/* Header content */}
      <div className="flex flex-col space-y-4 sm:space-y-0 sm:flex-row sm:items-center sm:justify-between">
        <div className="min-w-0 flex-1">
          <h1 className="text-2xl md:text-3xl font-bold text-gray-900 dark:text-gray-100 truncate">
            {title}
          </h1>
          {subtitle && (
            <p className="mt-1 text-sm text-gray-600 dark:text-gray-400">
              {subtitle}
            </p>
          )}
        </div>
        
        {/* Actions */}
        <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-3">
          {onRefresh && (
            <button
              onClick={onRefresh}
              disabled={isLoading}
              className="btn bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600 text-gray-600 dark:text-gray-300 min-h-[44px] px-4 py-2"
              aria-label="Refresh page data"
            >
              <FiRefreshCw className={`w-4 h-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
              <span className="sm:hidden">Refresh Data</span>
              <span className="hidden sm:inline">Refresh</span>
            </button>
          )}
          
          {actions.map((action, index) => (
            <React.Fragment key={index}>
              {action}
            </React.Fragment>
          ))}
        </div>
      </div>
    </div>
  );
};

// Main page layout component
const PageLayout = ({
  title,
  subtitle,
  breadcrumbs = [],
  actions = [],
  onRefresh,
  isLoading = false,
  error = null,
  onRetry,
  children,
  className = '',
  contentClassName = '',
  showHeader = true,
  showSearch = false,
  searchPlaceholder = 'Search...',
  onSearch,
  searchValue = '',
  onSearchChange,
  sidebar,
  sidebarWidth = 'w-64',
  sidebarCollapsible = true
}) => {
  const { utils, config } = useUX();
  const location = useLocation();
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const [searchFocused, setSearchFocused] = useState(false);
  
  // Auto-generate breadcrumbs from route if not provided
  const autoBreadcrumbs = React.useMemo(() => {
    if (breadcrumbs.length > 0) return breadcrumbs;
    
    const pathSegments = location.pathname.split('/').filter(Boolean);
    return pathSegments.map((segment, index) => {
      const path = '/' + pathSegments.slice(0, index + 1).join('/');
      const label = segment.charAt(0).toUpperCase() + segment.slice(1).replace(/-/g, ' ');
      
      return {
        label,
        path: index < pathSegments.length - 1 ? path : null
      };
    });
  }, [location.pathname, breadcrumbs]);
  
  // Handle keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e) => {
      // Ctrl/Cmd + K for search
      if ((e.ctrlKey || e.metaKey) && e.key === 'k' && showSearch) {
        e.preventDefault();
        document.querySelector('[data-search-input]')?.focus();
      }
      
      // Escape to close search
      if (e.key === 'Escape' && searchFocused) {
        document.querySelector('[data-search-input]')?.blur();
      }
    };
    
    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [showSearch, searchFocused]);
  
  // Error state
  if (error && !isLoading) {
    return (
      <div className={`min-h-screen flex items-center justify-center p-4 ${className}`}>
        <ErrorStates.Retryable
          title="Failed to Load Page"
          message={typeof error === 'string' ? error : 'We encountered an error while loading this page.'}
          onRetry={onRetry || onRefresh}
          retryText="Reload Page"
          showSupport={true}
        />
      </div>
    );
  }
  
  return (
    <div className={`min-h-screen bg-gray-50 dark:bg-gray-900 ${className}`}>
      {/* Sidebar */}
      {sidebar && (
        <>
          {/* Mobile sidebar overlay */}
          {!sidebarCollapsed && utils.isMobile() && (
            <div
              className="fixed inset-0 z-40 bg-black bg-opacity-50 lg:hidden"
              onClick={() => setSidebarCollapsed(true)}
            />
          )}
          
          {/* Sidebar */}
          <div className={`
            fixed inset-y-0 left-0 z-50 transform transition-transform duration-300 ease-in-out
            ${sidebarCollapsed ? '-translate-x-full' : 'translate-x-0'}
            ${sidebarWidth}
            lg:translate-x-0 lg:static lg:inset-0
          `}>
            {sidebar}
          </div>
        </>
      )}
      
      {/* Main content */}
      <div className={`${sidebar ? `lg:${sidebarWidth.replace('w-', 'ml-')}` : ''}`}>
        {/* Header */}
        {showHeader && (
          <header className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 px-4 sm:px-6 lg:px-8 py-6">
            <div className="flex items-center justify-between mb-6">
              {/* Mobile menu button */}
              {sidebar && sidebarCollapsible && (
                <button
                  onClick={() => setSidebarCollapsed(!sidebarCollapsed)}
                  className="lg:hidden p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 dark:hover:bg-gray-700"
                  aria-label="Toggle sidebar"
                >
                  <FiMenu className="w-6 h-6" />
                </button>
              )}
              
              {/* Search */}
              {showSearch && (
                <div className="flex-1 max-w-lg mx-4">
                  <div className="relative">
                    <FiSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                    <input
                      data-search-input
                      type="text"
                      value={searchValue}
                      onChange={(e) => onSearchChange?.(e.target.value)}
                      onFocus={() => setSearchFocused(true)}
                      onBlur={() => setSearchFocused(false)}
                      onKeyDown={(e) => {
                        if (e.key === 'Enter') {
                          onSearch?.(searchValue);
                        }
                      }}
                      placeholder={`${searchPlaceholder} ${utils.isMobile() ? '' : '(Ctrl+K)'}`}
                      className="form-input w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-violet-500 focus:border-violet-500"
                    />
                  </div>
                </div>
              )}
            </div>
            
            <PageHeader
              title={title}
              subtitle={subtitle}
              breadcrumbs={autoBreadcrumbs}
              actions={actions}
              onRefresh={onRefresh}
              isLoading={isLoading}
            />
          </header>
        )}
        
        {/* Page content */}
        <main className={`px-4 sm:px-6 lg:px-8 py-6 ${contentClassName}`}>
          {isLoading ? (
            <SkeletonLoader type="page" showStats={true} showTable={true} />
          ) : (
            children
          )}
        </main>
      </div>
    </div>
  );
};

export default PageLayout;
