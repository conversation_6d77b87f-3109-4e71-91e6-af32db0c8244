import React, { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { getAllRequestsSentByTeacher, clearSentRequests } from '../features/classroom/ClassroomSlice';
import { useThemeProvider } from '../utils/ThemeContext';
import {
  FiUser,
  FiMail,
  FiClock,
  FiRefreshCw,
  FiX,
  FiUsers,
  FiSend
} from 'react-icons/fi';

function TeacherSentRequests() {
  const dispatch = useDispatch();
  const { currentTheme } = useThemeProvider();
  const { sentRequests, loading, error } = useSelector((state) => state.classroom);

  // Theme classes
  const bgPrimary = currentTheme === "dark" ? "bg-gray-900" : "bg-gray-50";
  const bgSecondary = currentTheme === "dark" ? "bg-gray-800" : "bg-white";
  const textPrimary = currentTheme === "dark" ? "text-gray-100" : "text-gray-900";
  const textSecondary = currentTheme === "dark" ? "text-gray-400" : "text-gray-600";
  const borderColor = currentTheme === "dark" ? "border-gray-700" : "border-gray-200";

  useEffect(() => {
    // Fetch sent requests when component mounts
    dispatch(getAllRequestsSentByTeacher());

    // Cleanup when component unmounts
    return () => {
      dispatch(clearSentRequests());
    };
  }, [dispatch]);

  const handleRefresh = () => {
    dispatch(getAllRequestsSentByTeacher());
  };

  if (loading) {
    return (
      <div className={`${bgSecondary} rounded-lg p-6 border ${borderColor}`}>
        <div className="flex items-center justify-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          <span className={`ml-3 ${textSecondary}`}>Loading sent requests...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`${bgSecondary} rounded-lg p-6 border ${borderColor}`}>
        <div className="text-center py-8">
          <FiX className={`w-12 h-12 ${textSecondary} mx-auto mb-4`} />
          <h3 className={`text-lg font-medium ${textPrimary} mb-2`}>Error Loading Requests</h3>
          <p className={`${textSecondary} mb-4`}>
            {typeof error === 'string' ? error : error?.detail || 'Failed to load sent requests'}
          </p>
          <button
            onClick={handleRefresh}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className={`${bgSecondary} rounded-lg border ${borderColor}`}>
      {/* Header */}
      <div className="p-6 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center justify-between">
          <div>
            <h2 className={`text-xl font-semibold ${textPrimary} mb-1`}>
              Sent Requests
            </h2>
            <p className={`text-sm ${textSecondary}`}>
              All student join requests you've sent across your classrooms
            </p>
          </div>
          <button
            onClick={handleRefresh}
            className="flex items-center space-x-2 px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            <FiRefreshCw className="w-4 h-4" />
            <span>Refresh</span>
          </button>
        </div>
      </div>

      {/* Content */}
      <div className="p-6">
        {sentRequests && sentRequests.length > 0 ? (
          <div className="space-y-4">
            {sentRequests.map((request) => (
              <div
                key={request.id}
                className={`border ${borderColor} rounded-lg p-4 hover:shadow-md transition-shadow`}
              >
                <div className="flex items-start space-x-4">
                  {/* Student Avatar */}
                  <div className="flex-shrink-0">
                    {request.student_user?.profile_picture ? (
                      <img
                        src={request.student_user.profile_picture}
                        alt={request.student_user.username}
                        className="w-12 h-12 rounded-full object-cover"
                      />
                    ) : (
                      <div className="w-12 h-12 bg-blue-100 dark:bg-blue-900/30 rounded-full flex items-center justify-center">
                        <FiUser className="w-6 h-6 text-blue-600" />
                      </div>
                    )}
                  </div>

                  {/* Request Details */}
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center space-x-2 mb-2">
                      <h3 className={`font-medium ${textPrimary}`}>
                        {request.student_user?.username || 'Unknown Student'}
                      </h3>
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400">
                        <FiClock className="w-3 h-3 mr-1" />
                        Pending
                      </span>
                    </div>

                    <div className="space-y-1 text-sm">
                      <div className="flex items-center space-x-2">
                        <FiMail className="w-4 h-4 text-gray-400" />
                        <span className={textSecondary}>
                          {request.student_user?.email || 'No email'}
                        </span>
                      </div>
                      
                      <div className="flex items-center space-x-2">
                        <FiUsers className="w-4 h-4 text-gray-400" />
                        <span className={textSecondary}>
                          Classroom ID: {request.classroom_id}
                        </span>
                      </div>

                      {request.student_user?.country && (
                        <div className="flex items-center space-x-2">
                          <span className="w-4 h-4 text-center text-xs">🌍</span>
                          <span className={textSecondary}>
                            {request.student_user.country}
                          </span>
                        </div>
                      )}
                    </div>

                    {/* Verification Status */}
                    <div className="flex items-center space-x-4 mt-3">
                      <span className={`text-xs px-2 py-1 rounded-full ${
                        request.student_user?.is_email_verified
                          ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400'
                          : 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400'
                      }`}>
                        Email {request.student_user?.is_email_verified ? 'Verified' : 'Unverified'}
                      </span>
                      
                      <span className={`text-xs px-2 py-1 rounded-full ${
                        request.student_user?.is_mobile_verified
                          ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400'
                          : 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400'
                      }`}>
                        Mobile {request.student_user?.is_mobile_verified ? 'Verified' : 'Unverified'}
                      </span>
                    </div>
                  </div>

                  {/* Actions */}
                  <div className="flex-shrink-0">
                    <button className={`p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 ${textSecondary} transition-colors`}>
                      <FiX className="w-4 h-4" />
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-12">
            <FiSend className={`w-12 h-12 ${textSecondary} mx-auto mb-4`} />
            <h3 className={`text-lg font-medium ${textPrimary} mb-2`}>
              No sent requests
            </h3>
            <p className={textSecondary}>
              You haven't sent any student join requests yet.
            </p>
          </div>
        )}
      </div>

      {/* Summary */}
      {sentRequests && sentRequests.length > 0 && (
        <div className={`px-6 py-4 border-t ${borderColor} bg-gray-50 dark:bg-gray-700/50`}>
          <div className="flex items-center justify-between text-sm">
            <span className={textSecondary}>
              Total sent requests: {sentRequests.length}
            </span>
            <span className={textSecondary}>
              All requests are pending student response
            </span>
          </div>
        </div>
      )}
    </div>
  );
}

export default TeacherSentRequests;
