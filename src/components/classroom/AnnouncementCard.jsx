import React from 'react';
import { FiUser, FiClock, FiPaperclip, FiDownload, FiEye } from 'react-icons/fi';

const AnnouncementCard = ({ 
  announcement, 
  currentTheme,
  isTeacher = false,
  onEdit,
  onDelete 
}) => {
  // Theme classes
  const bgSecondary = currentTheme === "dark" ? "bg-gray-800" : "bg-white";
  const textPrimary = currentTheme === "dark" ? "text-gray-100" : "text-gray-900";
  const textSecondary = currentTheme === "dark" ? "text-gray-400" : "text-gray-600";
  const borderColor = currentTheme === "dark" ? "border-gray-700" : "border-gray-200";

  const formatDate = (dateString) => {
    if (!dateString) return 'Unknown date';
    try {
      const date = new Date(dateString);
      return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      });
    } catch (error) {
      return 'Invalid date';
    }
  };

  return (
    <div className={`${bgSecondary} rounded-lg p-6 border ${borderColor} shadow-sm`}>
      <div className="flex items-start space-x-4">
        <div className="flex-shrink-0">
          <div className="w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center">
            <FiUser className="w-5 h-5 text-white" />
          </div>
        </div>
        
        <div className="flex-1 min-w-0">
          <div className="flex items-center justify-between">
            <div>
              <h3 className={`font-medium ${textPrimary}`}>
                {announcement.teacher?.username || 'Teacher'}
              </h3>
              <div className="flex items-center space-x-2 mt-1">
                <FiClock className={`w-4 h-4 ${textSecondary}`} />
                <span className={`text-sm ${textSecondary}`}>
                  {formatDate(announcement.created_at)}
                </span>
              </div>
            </div>
            
            {isTeacher && (
              <div className="flex items-center space-x-2">
                <button
                  onClick={() => onEdit && onEdit(announcement)}
                  className={`text-sm ${textSecondary} hover:${textPrimary} transition-colors`}
                >
                  Edit
                </button>
                <button
                  onClick={() => onDelete && onDelete(announcement)}
                  className="text-sm text-red-600 hover:text-red-700 transition-colors"
                >
                  Delete
                </button>
              </div>
            )}
          </div>
          
          <div className="mt-4">
            <h4 className={`text-lg font-medium ${textPrimary} mb-2`}>
              {announcement.title}
            </h4>
            <div 
              className={`${textSecondary} prose prose-sm max-w-none`}
              dangerouslySetInnerHTML={{ __html: announcement.content }}
            />
          </div>
          
          {announcement.attachments && announcement.attachments.length > 0 && (
            <div className="mt-4 space-y-2">
              <h5 className={`text-sm font-medium ${textPrimary} flex items-center`}>
                <FiPaperclip className="w-4 h-4 mr-1" />
                Attachments
              </h5>
              <div className="space-y-2">
                {announcement.attachments.map((attachment, index) => (
                  <div 
                    key={index}
                    className={`flex items-center justify-between p-3 border ${borderColor} rounded-lg`}
                  >
                    <div className="flex items-center space-x-3">
                      <div className="w-8 h-8 bg-gray-100 dark:bg-gray-700 rounded flex items-center justify-center">
                        <FiEye className={`w-4 h-4 ${textSecondary}`} />
                      </div>
                      <div>
                        <p className={`text-sm font-medium ${textPrimary}`}>
                          {attachment.name || `Attachment ${index + 1}`}
                        </p>
                        <p className={`text-xs ${textSecondary}`}>
                          {attachment.size || 'Unknown size'}
                        </p>
                      </div>
                    </div>
                    <button className={`p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded ${textSecondary} hover:${textPrimary} transition-colors`}>
                      <FiDownload className="w-4 h-4" />
                    </button>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default AnnouncementCard;
