import React from 'react';
import { FiUsers, FiPlus } from 'react-icons/fi';

const StudentsTab = ({ 
  classroom, 
  students, 
  onRequestStudents,
  currentTheme 
}) => {
  // Theme classes
  const bgSecondary = currentTheme === "dark" ? "bg-gray-800" : "bg-white";
  const textPrimary = currentTheme === "dark" ? "text-gray-100" : "text-gray-900";
  const textSecondary = currentTheme === "dark" ? "text-gray-400" : "text-gray-600";
  const borderColor = currentTheme === "dark" ? "border-gray-700" : "border-gray-200";

  const getInitials = (name) => {
    if (!name) return '?';
    return name.charAt(0).toUpperCase();
  };

  const getAvatarColor = (index) => {
    const colors = [
      'bg-blue-600', 'bg-green-600', 'bg-purple-600', 'bg-red-600', 
      'bg-yellow-600', 'bg-indigo-600', 'bg-pink-600', 'bg-gray-600'
    ];
    return colors[index % colors.length];
  };

  return (
    <div className="space-y-6">
      <div className={`${bgSecondary} rounded-lg p-6 border ${borderColor}`}>
        <div className="flex items-center justify-between mb-6">
          <h2 className={`text-xl font-semibold ${textPrimary}`}>Students</h2>
          <button
            onClick={onRequestStudents}
            className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            <FiPlus className="w-4 h-4" />
            <span>Request Students</span>
          </button>
        </div>

        {/* Students List */}
        <div className="space-y-3">
          {students && students.length > 0 ? (
            students.map((student, index) => (
              <div 
                key={student.id || index} 
                className="flex items-center justify-between p-4 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700/50 border border-gray-200 dark:border-gray-700"
              >
                <div className="flex items-center space-x-3">
                  <div className={`w-12 h-12 ${getAvatarColor(index)} rounded-full flex items-center justify-center`}>
                    <span className="text-white font-medium text-lg">
                      {getInitials(student.username || student.user?.username)}
                    </span>
                  </div>
                  <div>
                    <p className={`font-medium ${textPrimary}`}>
                      {student.username || student.user?.username || 'Student'}
                    </p>
                    <p className={`text-sm ${textSecondary}`}>
                      {student.email || student.user?.email || '<EMAIL>'}
                    </p>
                  </div>
                </div>
                
                <div className="flex items-center space-x-2">
                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400">
                    Active
                  </span>
                  <button className={`p-2 hover:bg-gray-100 dark:hover:bg-gray-600 rounded ${textSecondary} hover:${textPrimary} transition-colors`}>
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z" />
                    </svg>
                  </button>
                </div>
              </div>
            ))
          ) : (
            <div className="text-center py-12">
              <FiUsers className={`w-12 h-12 ${textSecondary} mx-auto mb-4`} />
              <h3 className={`text-lg font-medium ${textPrimary} mb-2`}>No students enrolled</h3>
              <p className={`${textSecondary} mb-4`}>
                Start building your class by requesting students to join.
              </p>
              <button
                onClick={onRequestStudents}
                className="inline-flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                <FiPlus className="w-4 h-4" />
                <span>Request Students</span>
              </button>
            </div>
          )}
        </div>

        {/* Class Statistics */}
        {students && students.length > 0 && (
          <div className="mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="text-center">
                <p className={`text-2xl font-bold ${textPrimary}`}>{students.length}</p>
                <p className={`text-sm ${textSecondary}`}>Total Students</p>
              </div>
              <div className="text-center">
                <p className={`text-2xl font-bold text-green-600`}>{students.length}</p>
                <p className={`text-sm ${textSecondary}`}>Active</p>
              </div>
              <div className="text-center">
                <p className={`text-2xl font-bold text-blue-600`}>
                  {Math.round((students.length / (students.length + 2)) * 100)}%
                </p>
                <p className={`text-sm ${textSecondary}`}>Engagement</p>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default StudentsTab;
