import React from 'react';
import { FiPlus, FiMessageSquare } from 'react-icons/fi';
import AnnouncementCard from '../AnnouncementCard';

const InfoTab = ({ 
  announcements, 
  announcementsLoading, 
  onCreateAnnouncement,
  onEditAnnouncement,
  onDeleteAnnouncement,
  currentTheme 
}) => {
  // Theme classes
  const bgSecondary = currentTheme === "dark" ? "bg-gray-800" : "bg-white";
  const textPrimary = currentTheme === "dark" ? "text-gray-100" : "text-gray-900";
  const textSecondary = currentTheme === "dark" ? "text-gray-400" : "text-gray-600";
  const borderColor = currentTheme === "dark" ? "border-gray-700" : "border-gray-200";

  return (
    <div className="space-y-6">
      {/* Create Announcement Button */}
      <div className="flex justify-end">
        <button
          onClick={onCreateAnnouncement}
          className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
        >
          <FiPlus className="w-4 h-4" />
          <span>Create Announcement</span>
        </button>
      </div>

      {/* Announcements List */}
      <div className="space-y-4">
        {announcementsLoading ? (
          <div className={`${bgSecondary} rounded-lg p-8 border ${borderColor}`}>
            <div className="flex items-center justify-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
              <span className={`ml-3 ${textSecondary}`}>Loading announcements...</span>
            </div>
          </div>
        ) : announcements && announcements.length > 0 ? (
          announcements.map((announcement) => (
            <AnnouncementCard
              key={announcement.id}
              announcement={announcement}
              currentTheme={currentTheme}
              isTeacher={true}
              onEdit={onEditAnnouncement}
              onDelete={onDeleteAnnouncement}
            />
          ))
        ) : (
          <div className={`${bgSecondary} rounded-lg p-12 border ${borderColor} text-center`}>
            <FiMessageSquare className={`w-12 h-12 ${textSecondary} mx-auto mb-4`} />
            <h3 className={`text-lg font-medium ${textPrimary} mb-2`}>No announcements yet</h3>
            <p className={`${textSecondary} mb-4`}>
              Create your first announcement to share updates with your students.
            </p>
            <button
              onClick={onCreateAnnouncement}
              className="inline-flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              <FiPlus className="w-4 h-4" />
              <span>Create Announcement</span>
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

export default InfoTab;
