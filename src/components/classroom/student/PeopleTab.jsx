import React from 'react';
import { FiUsers } from 'react-icons/fi';

const PeopleTab = ({ 
  classroom, 
  students, 
  currentTheme 
}) => {
  // Theme classes
  const bgSecondary = currentTheme === "dark" ? "bg-gray-800" : "bg-white";
  const textPrimary = currentTheme === "dark" ? "text-gray-100" : "text-gray-900";
  const textSecondary = currentTheme === "dark" ? "text-gray-400" : "text-gray-600";
  const borderColor = currentTheme === "dark" ? "border-gray-700" : "border-gray-200";

  const getInitials = (name) => {
    if (!name) return '?';
    return name.charAt(0).toUpperCase();
  };

  const getAvatarColor = (index) => {
    const colors = [
      'bg-blue-600', 'bg-green-600', 'bg-purple-600', 'bg-red-600', 
      'bg-yellow-600', 'bg-indigo-600', 'bg-pink-600', 'bg-gray-600'
    ];
    return colors[index % colors.length];
  };

  return (
    <div className="space-y-6">
      <div className={`${bgSecondary} rounded-lg p-6 border ${borderColor}`}>
        <h2 className={`text-xl font-semibold ${textPrimary} mb-6`}>People</h2>

        {/* Teachers Section */}
        <div className="mb-8">
          <h3 className={`text-lg font-medium ${textPrimary} mb-4 flex items-center`}>
            <span>Teachers</span>
            <span className={`ml-2 text-sm ${textSecondary}`}>1</span>
          </h3>
          <div className="space-y-3">
            <div className="flex items-center space-x-3 p-3 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700/50">
              <div className="w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center">
                <span className="text-white font-medium">
                  {getInitials(classroom?.teacher?.username)}
                </span>
              </div>
              <div>
                <p className={`font-medium ${textPrimary}`}>
                  {classroom?.teacher?.username || 'Teacher'}
                </p>
                <p className={`text-sm ${textSecondary}`}>
                  {classroom?.teacher?.email || '<EMAIL>'}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Students Section */}
        <div>
          <h3 className={`text-lg font-medium ${textPrimary} mb-4 flex items-center`}>
            <span>Students</span>
            <span className={`ml-2 text-sm ${textSecondary}`}>{students?.length || 0}</span>
          </h3>
          <div className="space-y-3">
            {students && students.length > 0 ? (
              students.map((student, index) => (
                <div 
                  key={student.id || index} 
                  className="flex items-center space-x-3 p-3 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700/50"
                >
                  <div className={`w-10 h-10 ${getAvatarColor(index)} rounded-full flex items-center justify-center`}>
                    <span className="text-white font-medium">
                      {getInitials(student.username || student.user?.username)}
                    </span>
                  </div>
                  <div>
                    <p className={`font-medium ${textPrimary}`}>
                      {student.username || student.user?.username || 'Student'}
                    </p>
                    <p className={`text-sm ${textSecondary}`}>
                      {student.email || student.user?.email || '<EMAIL>'}
                    </p>
                  </div>
                </div>
              ))
            ) : (
              <div className="text-center py-8">
                <FiUsers className={`w-12 h-12 ${textSecondary} mx-auto mb-4`} />
                <p className={textSecondary}>No students enrolled yet</p>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default PeopleTab;
