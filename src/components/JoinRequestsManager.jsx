import React, { useState } from 'react';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Fi<PERSON> } from 'react-icons/fi';
import { 
  <PERSON><PERSON><PERSON><PERSON>, 
  PageContainer, 
  Card, 
  Tabs,
  Button,
  RefreshButton,
  InfoCard,
  SuccessModal,
  ErrorModal
} from './ui';
import JoinRequestCard from './ui/cards/JoinRequestCard';
import JoinRequestTable from './ui/tables/JoinRequestTable';
import useJoinRequests from '../hooks/useJoinRequests';

/**
 * Complete Join Requests Manager component
 * Handles displaying and managing classroom join requests
 */
const JoinRequestsManager = ({ 
  viewMode = 'table', // 'table', 'cards'
  showTabs = true,
  className = ''
}) => {
  const [currentViewMode, setCurrentViewMode] = useState(viewMode);
  const [selectedTab, setSelectedTab] = useState('pending');
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [showErrorModal, setShowErrorModal] = useState(false);
  const [successMessage, setSuccessMessage] = useState('');
  const [errorMessage, setErrorMessage] = useState('');

  // Use the custom hook for join requests
  const {
    requests,
    pendingRequests,
    approvedRequests,
    rejectedRequests,
    pendingCount,
    loading,
    error,
    processingIds,
    approveRequest,
    rejectRequest,
    handleBulkAction,
    refresh,
    isProcessing
  } = useJoinRequests({
    autoFetch: true,
    refreshInterval: 30000, // Refresh every 30 seconds
    onSuccess: (data) => {
      if (data.action) {
        setSuccessMessage(
          data.action === 'approve' 
            ? 'Request approved successfully!' 
            : 'Request rejected successfully!'
        );
        setShowSuccessModal(true);
      }
    },
    onError: (err) => {
      setErrorMessage(err.message);
      setShowErrorModal(true);
    }
  });

  // Handle individual request approval
  const handleApprove = async (request) => {
    const success = await approveRequest(request.id);
    if (success) {
      // Success handled by hook callback
    }
  };

  // Handle individual request rejection
  const handleReject = async (request) => {
    const success = await rejectRequest(request.id);
    if (success) {
      // Success handled by hook callback
    }
  };

  // Handle bulk actions
  const handleBulkActions = async (action, selectedRequests) => {
    try {
      const result = await handleBulkAction(action, selectedRequests);
      setSuccessMessage(
        `${action === 'approve' ? 'Approved' : 'Rejected'} ${result.successful} of ${result.total} requests`
      );
      setShowSuccessModal(true);
    } catch (err) {
      setErrorMessage(err.message);
      setShowErrorModal(true);
    }
  };

  // Handle view request details
  const handleViewRequest = (request) => {
    // Implement view details modal or navigation
    console.log('View request:', request);
  };

  // Tab configuration
  const tabs = [
    {
      id: 'pending',
      label: 'Pending',
      badge: pendingCount > 0 ? pendingCount.toString() : null,
      content: currentViewMode === 'table' ? (
        <JoinRequestTable
          requests={pendingRequests}
          onApprove={handleApprove}
          onReject={handleReject}
          onView={handleViewRequest}
          onBulkAction={handleBulkActions}
          loading={loading}
          selectable={true}
          showClassroomInfo={true}
        />
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {pendingRequests.map(request => (
            <JoinRequestCard
              key={request.id}
              request={request}
              onApprove={handleApprove}
              onReject={handleReject}
              onView={handleViewRequest}
              showActions={true}
              className={isProcessing(request.id) ? 'opacity-50 pointer-events-none' : ''}
            />
          ))}
        </div>
      )
    },
    {
      id: 'approved',
      label: 'Approved',
      badge: approvedRequests.length > 0 ? approvedRequests.length.toString() : null,
      content: currentViewMode === 'table' ? (
        <JoinRequestTable
          requests={approvedRequests}
          onView={handleViewRequest}
          loading={loading}
          showActions={false}
          showClassroomInfo={true}
        />
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {approvedRequests.map(request => (
            <JoinRequestCard
              key={request.id}
              request={request}
              onView={handleViewRequest}
              showActions={false}
            />
          ))}
        </div>
      )
    },
    {
      id: 'rejected',
      label: 'Rejected',
      badge: rejectedRequests.length > 0 ? rejectedRequests.length.toString() : null,
      content: currentViewMode === 'table' ? (
        <JoinRequestTable
          requests={rejectedRequests}
          onView={handleViewRequest}
          loading={loading}
          showActions={false}
          showClassroomInfo={true}
        />
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {rejectedRequests.map(request => (
            <JoinRequestCard
              key={request.id}
              request={request}
              onView={handleViewRequest}
              showActions={false}
            />
          ))}
        </div>
      )
    }
  ];

  // Page header actions
  const headerActions = [
    {
      label: currentViewMode === 'table' ? 'Card View' : 'Table View',
      variant: 'outline',
      icon: currentViewMode === 'table' ? FiUsers : FiFilter,
      onClick: () => setCurrentViewMode(currentViewMode === 'table' ? 'cards' : 'table')
    },
    {
      label: 'Refresh',
      variant: 'outline',
      icon: FiRefreshCw,
      onClick: refresh,
      isLoading: loading
    }
  ];

  return (
    <PageContainer className={className}>
      <PageHeader
        title="Join Requests"
        subtitle="Manage student requests to join classrooms"
        description="Review and approve or reject student requests to join your classrooms."
        actions={headerActions}
      />

      {/* Error Display */}
      {error && (
        <InfoCard
          type="error"
          title="Error Loading Requests"
          message={error}
          onClose={() => {}}
          actions={[
            {
              label: 'Retry',
              onClick: refresh
            }
          ]}
          className="mb-6"
        />
      )}

      {/* Summary Stats */}
      {!loading && requests.length > 0 && (
        <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 mb-6">
          <Card className="text-center">
            <div className="flex items-center justify-center space-x-2">
              <FiUsers className="w-5 h-5 text-yellow-600" />
              <span className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                {pendingCount}
              </span>
            </div>
            <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
              Pending Requests
            </p>
          </Card>
          
          <Card className="text-center">
            <div className="flex items-center justify-center space-x-2">
              <FiCheck className="w-5 h-5 text-green-600" />
              <span className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                {approvedRequests.length}
              </span>
            </div>
            <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
              Approved
            </p>
          </Card>
          
          <Card className="text-center">
            <div className="flex items-center justify-center space-x-2">
              <FiX className="w-5 h-5 text-red-600" />
              <span className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                {rejectedRequests.length}
              </span>
            </div>
            <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
              Rejected
            </p>
          </Card>
        </div>
      )}

      {/* Main Content */}
      <Card>
        {showTabs ? (
          <Tabs
            tabs={tabs}
            activeTab={selectedTab}
            onTabChange={setSelectedTab}
            variant="underline"
          />
        ) : (
          <div className="p-6">
            {tabs.find(tab => tab.id === selectedTab)?.content}
          </div>
        )}
      </Card>

      {/* Success Modal */}
      <SuccessModal
        isOpen={showSuccessModal}
        onClose={() => setShowSuccessModal(false)}
        title="Success"
        message={successMessage}
      />

      {/* Error Modal */}
      <ErrorModal
        isOpen={showErrorModal}
        onClose={() => setShowErrorModal(false)}
        title="Error"
        message={errorMessage}
        onRetry={refresh}
      />
    </PageContainer>
  );
};

export default JoinRequestsManager;
