/**
 * Higher-order component for performance monitoring
 * Wraps components to monitor their render performance
 */

import React from 'react';
import { useRenderPerformance } from '../../utils/performance';

/**
 * HOC that adds performance monitoring to any component
 * @param {React.Component} WrappedComponent - Component to monitor
 * @param {string} componentName - Optional name for the component
 * @returns {React.Component} - Enhanced component with performance monitoring
 */
export const withPerformanceMonitoring = (WrappedComponent, componentName) => {
  const MonitoredComponent = (props) => {
    useRenderPerformance(componentName || WrappedComponent.name || 'UnknownComponent');
    return <WrappedComponent {...props} />;
  };

  MonitoredComponent.displayName = `withPerformanceMonitoring(${componentName || WrappedComponent.name || 'Component'})`;
  return MonitoredComponent;
};

/**
 * Performance monitoring wrapper component
 * Alternative to HOC for more explicit usage
 */
export const PerformanceMonitor = ({ children, componentName = 'PerformanceMonitor' }) => {
  useRenderPerformance(componentName);
  return children;
};

export default withPerformanceMonitoring;
