import React from 'react';
import { ErrorBoundaryFallback } from './ErrorStates';

class ErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { 
      hasError: false, 
      error: null,
      errorInfo: null 
    };
  }

  static getDerivedStateFromError(error) {
    // Update state so the next render will show the fallback UI
    return { hasError: true };
  }

  componentDidCatch(error, errorInfo) {
    // Log error to console in development
    if (process.env.NODE_ENV === 'development') {
      console.error('Error Boundary caught an error:', error, errorInfo);
    }

    // You can also log the error to an error reporting service here
    this.setState({
      error,
      errorInfo
    });

    // Optional: Send error to logging service
    if (this.props.onError) {
      this.props.onError(error, errorInfo);
    }
  }

  handleReset = () => {
    this.setState({ 
      hasError: false, 
      error: null, 
      errorInfo: null 
    });
    
    if (this.props.onReset) {
      this.props.onReset();
    }
  };

  render() {
    if (this.state.hasError) {
      // Custom fallback UI
      if (this.props.fallback) {
        return this.props.fallback(
          this.state.error, 
          this.handleReset,
          this.state.errorInfo
        );
      }

      // Default fallback UI
      return (
        <ErrorBoundaryFallback
          error={this.state.error}
          resetError={this.handleReset}
          className={this.props.className}
        />
      );
    }

    return this.props.children;
  }
}

// Hook-based error boundary for functional components
export const useErrorHandler = () => {
  const [error, setError] = React.useState(null);

  const resetError = React.useCallback(() => {
    setError(null);
  }, []);

  const captureError = React.useCallback((error) => {
    setError(error);
  }, []);

  React.useEffect(() => {
    if (error) {
      throw error;
    }
  }, [error]);

  return { captureError, resetError };
};

// Higher-order component for wrapping components with error boundary
export const withErrorBoundary = (Component, errorBoundaryProps = {}) => {
  const WrappedComponent = (props) => (
    <ErrorBoundary {...errorBoundaryProps}>
      <Component {...props} />
    </ErrorBoundary>
  );

  WrappedComponent.displayName = `withErrorBoundary(${Component.displayName || Component.name})`;
  
  return WrappedComponent;
};

// Async error boundary for handling async errors
export const AsyncErrorBoundary = ({ children, onError, fallback, ...props }) => {
  const [asyncError, setAsyncError] = React.useState(null);

  React.useEffect(() => {
    const handleUnhandledRejection = (event) => {
      setAsyncError(event.reason);
      if (onError) {
        onError(event.reason);
      }
    };

    window.addEventListener('unhandledrejection', handleUnhandledRejection);

    return () => {
      window.removeEventListener('unhandledrejection', handleUnhandledRejection);
    };
  }, [onError]);

  const resetAsyncError = React.useCallback(() => {
    setAsyncError(null);
  }, []);

  if (asyncError) {
    if (fallback) {
      return fallback(asyncError, resetAsyncError);
    }
    return (
      <ErrorBoundaryFallback
        error={asyncError}
        resetError={resetAsyncError}
        {...props}
      />
    );
  }

  return (
    <ErrorBoundary onError={onError} fallback={fallback} {...props}>
      {children}
    </ErrorBoundary>
  );
};

export default ErrorBoundary;
