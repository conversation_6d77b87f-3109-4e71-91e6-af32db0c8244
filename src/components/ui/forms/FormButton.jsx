import React from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>C<PERSON>ck, FiX, FiPlus, FiSave, FiTrash2 } from 'react-icons/fi';

/**
 * Reusable FormButton component for consistent form button styling
 * Supports different variants, sizes, and states
 */
const FormButton = ({
  children,
  type = 'button',
  variant = 'primary', // 'primary', 'secondary', 'danger', 'success', 'outline', 'ghost'
  size = 'default', // 'small', 'default', 'large'
  isLoading = false,
  disabled = false,
  icon: IconComponent,
  iconPosition = 'left', // 'left', 'right'
  fullWidth = false,
  onClick,
  className = '',
  ...props
}) => {
  const baseClasses = 'inline-flex items-center justify-center font-medium rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed';

  const sizeClasses = {
    small: 'px-3 py-1.5 text-sm',
    default: 'px-4 py-2 text-sm',
    large: 'px-6 py-3 text-base'
  };

  const variantClasses = {
    primary: 'bg-violet-600 text-white hover:bg-violet-700 focus:ring-violet-500 shadow-sm',
    secondary: 'bg-gray-600 text-white hover:bg-gray-700 focus:ring-gray-500 shadow-sm',
    danger: 'bg-red-600 text-white hover:bg-red-700 focus:ring-red-500 shadow-sm',
    success: 'bg-green-600 text-white hover:bg-green-700 focus:ring-green-500 shadow-sm',
    outline: 'border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:ring-violet-500',
    ghost: 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 focus:ring-violet-500'
  };

  const iconSpacing = size === 'small' ? 'space-x-1.5' : size === 'large' ? 'space-x-3' : 'space-x-2';

  const renderIcon = (position) => {
    if (!IconComponent || iconPosition !== position) return null;
    
    const iconSize = size === 'small' ? 'w-3 h-3' : size === 'large' ? 'w-5 h-5' : 'w-4 h-4';
    
    return <IconComponent className={iconSize} />;
  };

  const renderLoadingIcon = () => {
    const iconSize = size === 'small' ? 'w-3 h-3' : size === 'large' ? 'w-5 h-5' : 'w-4 h-4';
    return <FiLoader className={`${iconSize} animate-spin`} />;
  };

  return (
    <button
      type={type}
      onClick={onClick}
      disabled={disabled || isLoading}
      className={`
        ${baseClasses}
        ${sizeClasses[size]}
        ${variantClasses[variant]}
        ${fullWidth ? 'w-full' : ''}
        ${iconSpacing}
        ${className}
      `}
      {...props}
    >
      {isLoading ? renderLoadingIcon() : renderIcon('left')}
      {children}
      {!isLoading && renderIcon('right')}
    </button>
  );
};

/**
 * Specialized SubmitButton for form submissions
 */
export const SubmitButton = ({
  children = 'Submit',
  isLoading = false,
  disabled = false,
  variant = 'primary',
  ...props
}) => {
  return (
    <FormButton
      type="submit"
      variant={variant}
      isLoading={isLoading}
      disabled={disabled}
      icon={isLoading ? undefined : FiCheck}
      {...props}
    >
      {isLoading ? 'Submitting...' : children}
    </FormButton>
  );
};

/**
 * Specialized CancelButton for form cancellation
 */
export const CancelButton = ({
  children = 'Cancel',
  variant = 'outline',
  ...props
}) => {
  return (
    <FormButton
      type="button"
      variant={variant}
      icon={FiX}
      {...props}
    >
      {children}
    </FormButton>
  );
};

/**
 * Specialized SaveButton for save actions
 */
export const SaveButton = ({
  children = 'Save',
  isLoading = false,
  variant = 'primary',
  ...props
}) => {
  return (
    <FormButton
      type="submit"
      variant={variant}
      isLoading={isLoading}
      icon={isLoading ? undefined : FiSave}
      {...props}
    >
      {isLoading ? 'Saving...' : children}
    </FormButton>
  );
};

/**
 * Specialized DeleteButton for delete actions
 */
export const DeleteButton = ({
  children = 'Delete',
  isLoading = false,
  variant = 'danger',
  ...props
}) => {
  return (
    <FormButton
      type="button"
      variant={variant}
      isLoading={isLoading}
      icon={isLoading ? undefined : FiTrash2}
      {...props}
    >
      {isLoading ? 'Deleting...' : children}
    </FormButton>
  );
};

/**
 * Specialized AddButton for add/create actions
 */
export const AddButton = ({
  children = 'Add',
  variant = 'primary',
  ...props
}) => {
  return (
    <FormButton
      type="button"
      variant={variant}
      icon={FiPlus}
      {...props}
    >
      {children}
    </FormButton>
  );
};

/**
 * Button group for organizing multiple buttons
 */
export const ButtonGroup = ({
  children,
  orientation = 'horizontal', // 'horizontal', 'vertical'
  spacing = 'default', // 'tight', 'default', 'loose'
  alignment = 'left', // 'left', 'center', 'right', 'between'
  className = ''
}) => {
  const orientationClasses = {
    horizontal: 'flex-row',
    vertical: 'flex-col'
  };

  const spacingClasses = {
    tight: orientation === 'horizontal' ? 'space-x-2' : 'space-y-2',
    default: orientation === 'horizontal' ? 'space-x-3' : 'space-y-3',
    loose: orientation === 'horizontal' ? 'space-x-4' : 'space-y-4'
  };

  const alignmentClasses = {
    left: 'justify-start',
    center: 'justify-center',
    right: 'justify-end',
    between: 'justify-between'
  };

  return (
    <div className={`
      flex ${orientationClasses[orientation]} ${spacingClasses[spacing]} ${alignmentClasses[alignment]}
      ${className}
    `}>
      {children}
    </div>
  );
};

export default FormButton;
