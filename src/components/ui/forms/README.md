# Form Components

This directory contains reusable form components for the EduFair application. These components provide consistent form behavior, validation, and styling across the application.

## Components

### FormModal
A modal wrapper specifically designed for forms with consistent behavior and styling.

**Props:**
- `isOpen` (boolean): Whether the modal is open
- `onClose` (function): Close callback
- `title` (string): Modal title
- `subtitle` (string): Optional subtitle
- `onSubmit` (function): Form submission callback
- `submitLabel` (string): Submit button text (default: 'Submit')
- `cancelLabel` (string): Cancel button text (default: 'Cancel')
- `isSubmitting` (boolean): Loading state
- `submitDisabled` (boolean): Disable submit button
- `size` (string): Modal size - 'small', 'default', 'large', 'full'
- `closeOnOverlayClick` (boolean): Close on overlay click (default: true)
- `closeOnEscape` (boolean): Close on escape key (default: true)
- `preventClose` (boolean): Prevent closing during submission

**Usage:**
```jsx
import { FormModal } from '../components/ui/forms';

<FormModal
  isOpen={isModalOpen}
  onClose={handleClose}
  onSubmit={handleSubmit}
  title="Create Classroom"
  isSubmitting={isLoading}
>
  <FormField label="Name" required>
    <TextInput value={name} onChange={setName} />
  </FormField>
</FormModal>
```

### ConfirmModal
A specialized modal for confirmation dialogs.

**Props:**
- `isOpen` (boolean): Whether the modal is open
- `onClose` (function): Close callback
- `onConfirm` (function): Confirm callback
- `title` (string): Modal title (default: 'Confirm Action')
- `message` (string): Confirmation message
- `confirmLabel` (string): Confirm button text (default: 'Confirm')
- `cancelLabel` (string): Cancel button text (default: 'Cancel')
- `type` (string): Button type - 'default', 'danger', 'warning'
- `isLoading` (boolean): Loading state

**Usage:**
```jsx
import { ConfirmModal } from '../components/ui/forms';

<ConfirmModal
  isOpen={showDeleteConfirm}
  onClose={() => setShowDeleteConfirm(false)}
  onConfirm={handleDelete}
  title="Delete Classroom"
  message="Are you sure you want to delete this classroom? This action cannot be undone."
  type="danger"
  confirmLabel="Delete"
/>
```

### FormButton
Consistent button component with multiple variants and states.

**Props:**
- `variant` (string): Button style - 'primary', 'secondary', 'danger', 'success', 'outline', 'ghost'
- `size` (string): Button size - 'small', 'default', 'large'
- `isLoading` (boolean): Loading state with spinner
- `disabled` (boolean): Disabled state
- `icon` (component): Icon component
- `iconPosition` (string): Icon position - 'left', 'right'
- `fullWidth` (boolean): Full width button

**Specialized Buttons:**
- `SubmitButton`: For form submissions
- `CancelButton`: For cancellation actions
- `SaveButton`: For save actions
- `DeleteButton`: For delete actions
- `AddButton`: For add/create actions

**Usage:**
```jsx
import { FormButton, SubmitButton, DeleteButton, ButtonGroup } from '../components/ui/forms';

<ButtonGroup alignment="right">
  <FormButton variant="outline" onClick={handleCancel}>
    Cancel
  </FormButton>
  <SubmitButton isLoading={isSubmitting}>
    Create Classroom
  </SubmitButton>
</ButtonGroup>

<DeleteButton 
  onClick={handleDelete}
  isLoading={isDeleting}
>
  Delete Item
</DeleteButton>
```

### FormValidation
Comprehensive validation system with rules, hooks, and components.

**Validation Rules:**
- `required`: Field is required
- `email`: Valid email format
- `minLength(n)`: Minimum length
- `maxLength(n)`: Maximum length
- `pattern(regex, message)`: Custom pattern
- `phone`: Valid phone number
- `password`: Strong password requirements
- `confirmPassword(original)`: Password confirmation
- `url`: Valid URL format
- `number`: Valid number
- `min(n)`: Minimum value
- `max(n)`: Maximum value

**useFormValidation Hook:**
```jsx
import { useFormValidation, validationRules } from '../components/ui/forms';

const validationSchema = {
  email: [validationRules.required, validationRules.email],
  password: [validationRules.required, validationRules.password],
  confirmPassword: [validationRules.required, validationRules.confirmPassword(values.password)]
};

const {
  values,
  errors,
  touched,
  isValid,
  handleChange,
  handleBlur,
  validateForm,
  reset
} = useFormValidation({ email: '', password: '' }, validationSchema);
```

**Validation Components:**
- `ValidationMessage`: Display field validation messages
- `FieldValidationIndicator`: Inline validation icons
- `FormSummary`: Form-level error summary

**Usage:**
```jsx
import { 
  ValidationMessage, 
  FieldValidationIndicator, 
  FormSummary 
} from '../components/ui/forms';

<FormField label="Email" error={errors.email}>
  <div className="relative">
    <TextInput
      value={values.email}
      onChange={(e) => handleChange('email', e.target.value)}
      onBlur={() => handleBlur('email')}
      error={!!errors.email}
    />
    <FieldValidationIndicator 
      error={errors.email}
      success={!errors.email && touched.email && values.email}
    />
  </div>
</FormField>

<FormSummary errors={errors} />
```

## Integration with Existing Components

These form components work seamlessly with the existing `FormComponents.jsx`:

```jsx
import { 
  FormField, 
  TextInput, 
  Select 
} from '../components/ui/forms'; // Re-exported from FormComponents

import { 
  FormModal, 
  useFormValidation, 
  validationRules 
} from '../components/ui/forms'; // New components
```

## Best Practices

1. **Use FormModal for all modal forms** to ensure consistent behavior
2. **Implement validation** using the validation system for better UX
3. **Use specialized buttons** (SubmitButton, DeleteButton) for semantic clarity
4. **Group related buttons** using ButtonGroup component
5. **Handle loading states** properly with isLoading props
6. **Provide clear feedback** with validation messages and indicators

## Migration Guide

**Before:**
```jsx
<div className="fixed inset-0 z-50 flex items-center justify-center">
  <div className="bg-white p-8 rounded-xl">
    <h2>Create Item</h2>
    <form onSubmit={handleSubmit}>
      <input type="text" value={name} onChange={e => setName(e.target.value)} />
      <button type="submit">Submit</button>
    </form>
  </div>
</div>
```

**After:**
```jsx
<FormModal
  isOpen={isOpen}
  onClose={onClose}
  onSubmit={handleSubmit}
  title="Create Item"
  isSubmitting={isLoading}
>
  <FormField label="Name" required>
    <TextInput value={name} onChange={setName} />
  </FormField>
</FormModal>
```
