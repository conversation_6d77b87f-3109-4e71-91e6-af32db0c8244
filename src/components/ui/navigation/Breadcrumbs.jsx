import React from 'react';
import { FiChevronRight, FiHome } from 'react-icons/fi';
import { Link } from 'react-router-dom';

/**
 * Reusable Breadcrumbs component for navigation hierarchy
 * Provides consistent breadcrumb navigation across the application
 */
const Breadcrumbs = ({
  items = [],
  separator = 'chevron', // 'chevron', 'slash', 'arrow'
  showHome = true,
  homeUrl = '/',
  homeLabel = 'Home',
  className = '',
  itemClassName = '',
  separatorClassName = '',
  maxItems = null, // Limit number of items shown
  ...props
}) => {
  // Separator components
  const separators = {
    chevron: <FiChevronRight className="w-4 h-4 text-gray-400" />,
    slash: <span className="text-gray-400">/</span>,
    arrow: <span className="text-gray-400">→</span>
  };

  const SeparatorComponent = separators[separator] || separators.chevron;

  // Process items to handle max items limit
  const processedItems = React.useMemo(() => {
    if (!maxItems || items.length <= maxItems) {
      return items;
    }

    // Show first item, ellipsis, and last few items
    const firstItem = items[0];
    const lastItems = items.slice(-(maxItems - 2));
    
    return [
      firstItem,
      { label: '...', href: null, isEllipsis: true },
      ...lastItems
    ];
  }, [items, maxItems]);

  // Add home item if enabled
  const allItems = React.useMemo(() => {
    const breadcrumbItems = [...processedItems];
    
    if (showHome) {
      breadcrumbItems.unshift({
        label: homeLabel,
        href: homeUrl,
        icon: FiHome,
        isHome: true
      });
    }
    
    return breadcrumbItems;
  }, [processedItems, showHome, homeLabel, homeUrl]);

  if (allItems.length === 0) return null;

  return (
    <nav 
      aria-label="Breadcrumb" 
      className={`flex items-center space-x-2 ${className}`}
      {...props}
    >
      <ol className="flex items-center space-x-2">
        {allItems.map((item, index) => {
          const isLast = index === allItems.length - 1;
          const IconComponent = item.icon;

          return (
            <li key={index} className="flex items-center space-x-2">
              {/* Breadcrumb Item */}
              <div className={`flex items-center space-x-1 ${itemClassName}`}>
                {IconComponent && (
                  <IconComponent className="w-4 h-4 flex-shrink-0" />
                )}
                
                {item.isEllipsis ? (
                  <span className="text-gray-500 dark:text-gray-400 px-1">
                    {item.label}
                  </span>
                ) : item.href && !isLast ? (
                  <Link
                    to={item.href}
                    className="text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-200 transition-colors text-sm font-medium truncate max-w-xs"
                    title={item.label}
                  >
                    {item.label}
                  </Link>
                ) : (
                  <span 
                    className={`
                      text-sm font-medium truncate max-w-xs
                      ${isLast 
                        ? 'text-gray-900 dark:text-gray-100' 
                        : 'text-gray-600 dark:text-gray-400'
                      }
                    `}
                    title={item.label}
                  >
                    {item.label}
                  </span>
                )}
              </div>

              {/* Separator */}
              {!isLast && (
                <div className={`flex-shrink-0 ${separatorClassName}`}>
                  {SeparatorComponent}
                </div>
              )}
            </li>
          );
        })}
      </ol>
    </nav>
  );
};

/**
 * Breadcrumb item builder helper
 */
export const createBreadcrumbItem = (label, href = null, icon = null) => ({
  label,
  href,
  icon
});

/**
 * Common breadcrumb patterns for the application
 */
export const breadcrumbPatterns = {
  // Admin patterns
  adminUsers: (userId = null) => [
    createBreadcrumbItem('Admin', '/admin'),
    createBreadcrumbItem('Users', '/admin/users'),
    ...(userId ? [createBreadcrumbItem('User Details')] : [])
  ],

  adminClassrooms: (classroomId = null) => [
    createBreadcrumbItem('Admin', '/admin'),
    createBreadcrumbItem('Classrooms', '/admin/classrooms'),
    ...(classroomId ? [createBreadcrumbItem('Classroom Details')] : [])
  ],

  // Teacher patterns
  teacherClassrooms: (classroomId = null, classroomName = null) => [
    createBreadcrumbItem('Teacher', '/teacher'),
    createBreadcrumbItem('Classrooms', '/teacher/classrooms'),
    ...(classroomId ? [createBreadcrumbItem(classroomName || 'Classroom', `/teacher/classrooms/${classroomId}`)] : [])
  ],

  teacherExams: (examId = null, examTitle = null) => [
    createBreadcrumbItem('Teacher', '/teacher'),
    createBreadcrumbItem('Exams', '/teacher/exams'),
    ...(examId ? [createBreadcrumbItem(examTitle || 'Exam', `/teacher/exams/${examId}`)] : [])
  ],

  // Student patterns
  studentClassrooms: (classroomId = null, classroomName = null) => [
    createBreadcrumbItem('Student', '/student'),
    createBreadcrumbItem('Classrooms', '/student/classrooms'),
    ...(classroomId ? [createBreadcrumbItem(classroomName || 'Classroom', `/student/classrooms/${classroomId}`)] : [])
  ],

  studentExams: (examId = null, examTitle = null) => [
    createBreadcrumbItem('Student', '/student'),
    createBreadcrumbItem('Exams', '/student/exams'),
    ...(examId ? [createBreadcrumbItem(examTitle || 'Exam', `/student/exams/${examId}`)] : [])
  ]
};

/**
 * Hook for generating breadcrumbs based on current route
 */
export const useBreadcrumbs = (pattern, ...args) => {
  return React.useMemo(() => {
    if (typeof pattern === 'function') {
      return pattern(...args);
    }
    if (typeof pattern === 'string' && breadcrumbPatterns[pattern]) {
      return breadcrumbPatterns[pattern](...args);
    }
    return pattern || [];
  }, [pattern, ...args]);
};

/**
 * Compact Breadcrumbs variant for mobile or limited space
 */
export const CompactBreadcrumbs = ({
  items = [],
  showOnlyLast = 2,
  ...props
}) => {
  const compactItems = React.useMemo(() => {
    if (items.length <= showOnlyLast) {
      return items;
    }
    return items.slice(-showOnlyLast);
  }, [items, showOnlyLast]);

  return (
    <Breadcrumbs
      items={compactItems}
      showHome={false}
      maxItems={showOnlyLast}
      {...props}
    />
  );
};

export default Breadcrumbs;
