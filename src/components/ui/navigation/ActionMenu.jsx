import React, { useState, useRef, useEffect } from 'react';
import { 
  FiMoreVertical, 
  FiMoreHorizontal, 
  FiChevronDown,
  FiCheck,
  FiX,
  FiEye,
  FiEdit,
  FiTrash2,
  FiUser,
  FiDownload,
  FiShare2
} from 'react-icons/fi';

/**
 * Reusable ActionMenu component for dropdown menus
 * Provides consistent dropdown behavior with various trigger styles
 */
const ActionMenu = ({
  trigger = 'dots', // 'dots', 'button', 'custom'
  triggerLabel = 'Actions',
  triggerIcon: TriggerIcon,
  items = [],
  onItemClick,
  position = 'bottom-right', // 'bottom-left', 'bottom-right', 'top-left', 'top-right'
  size = 'default', // 'sm', 'default', 'lg'
  disabled = false,
  className = '',
  triggerClassName = '',
  menuClassName = '',
  children, // Custom trigger content
  ...props
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const menuRef = useRef(null);
  const triggerRef = useRef(null);

  // Position configurations
  const positionClasses = {
    'bottom-left': 'top-full left-0 mt-1',
    'bottom-right': 'top-full right-0 mt-1',
    'top-left': 'bottom-full left-0 mb-1',
    'top-right': 'bottom-full right-0 mb-1'
  };

  // Size configurations
  const sizeClasses = {
    sm: {
      trigger: 'p-1',
      menu: 'py-1',
      item: 'px-3 py-1.5 text-xs'
    },
    default: {
      trigger: 'p-2',
      menu: 'py-2',
      item: 'px-4 py-2 text-sm'
    },
    lg: {
      trigger: 'p-3',
      menu: 'py-3',
      item: 'px-6 py-3 text-base'
    }
  };

  const sizeConfig = sizeClasses[size] || sizeClasses.default;

  // Close menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (
        menuRef.current && 
        !menuRef.current.contains(event.target) &&
        triggerRef.current &&
        !triggerRef.current.contains(event.target)
      ) {
        setIsOpen(false);
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
      return () => document.removeEventListener('mousedown', handleClickOutside);
    }
  }, [isOpen]);

  // Close menu on escape key
  useEffect(() => {
    const handleEscape = (event) => {
      if (event.key === 'Escape') {
        setIsOpen(false);
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleEscape);
      return () => document.removeEventListener('keydown', handleEscape);
    }
  }, [isOpen]);

  const handleTriggerClick = () => {
    if (!disabled) {
      setIsOpen(!isOpen);
    }
  };

  const handleItemClick = (item, event) => {
    event.stopPropagation();
    
    if (item.disabled) return;
    
    if (item.onClick) {
      item.onClick(item, event);
    }
    
    if (onItemClick) {
      onItemClick(item, event);
    }
    
    // Close menu unless item specifies to keep open
    if (!item.keepOpen) {
      setIsOpen(false);
    }
  };

  // Render trigger based on type
  const renderTrigger = () => {
    const baseClasses = `
      ${sizeConfig.trigger} rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-violet-500
      ${disabled 
        ? 'opacity-50 cursor-not-allowed' 
        : 'cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-700'
      }
      ${triggerClassName}
    `;

    if (children) {
      return (
        <div
          ref={triggerRef}
          onClick={handleTriggerClick}
          className={baseClasses}
        >
          {children}
        </div>
      );
    }

    switch (trigger) {
      case 'dots':
        return (
          <button
            ref={triggerRef}
            onClick={handleTriggerClick}
            disabled={disabled}
            className={`${baseClasses} text-gray-400 hover:text-gray-600 dark:hover:text-gray-300`}
            aria-label="More actions"
          >
            <FiMoreVertical className="w-4 h-4" />
          </button>
        );

      case 'button':
        const IconComponent = TriggerIcon || FiChevronDown;
        return (
          <button
            ref={triggerRef}
            onClick={handleTriggerClick}
            disabled={disabled}
            className={`
              ${baseClasses} flex items-center space-x-2 text-gray-700 dark:text-gray-300
              border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800
            `}
          >
            <span>{triggerLabel}</span>
            <IconComponent className="w-4 h-4" />
          </button>
        );

      default:
        return (
          <button
            ref={triggerRef}
            onClick={handleTriggerClick}
            disabled={disabled}
            className={baseClasses}
          >
            {TriggerIcon ? <TriggerIcon className="w-4 h-4" /> : <FiMoreVertical className="w-4 h-4" />}
          </button>
        );
    }
  };

  // Render menu item
  const renderMenuItem = (item, index) => {
    const IconComponent = item.icon;
    const isDestructive = item.variant === 'destructive' || item.destructive;
    const isDivider = item.type === 'divider';
    const isHeader = item.type === 'header';

    if (isDivider) {
      return (
        <div
          key={index}
          className="my-1 border-t border-gray-200 dark:border-gray-700"
        />
      );
    }

    if (isHeader) {
      return (
        <div
          key={index}
          className="px-4 py-2 text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wide"
        >
          {item.label}
        </div>
      );
    }

    return (
      <button
        key={index}
        onClick={(e) => handleItemClick(item, e)}
        disabled={item.disabled}
        className={`
          ${sizeConfig.item} w-full text-left flex items-center space-x-3 transition-colors
          ${item.disabled 
            ? 'opacity-50 cursor-not-allowed' 
            : 'cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-700'
          }
          ${isDestructive 
            ? 'text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20' 
            : 'text-gray-700 dark:text-gray-300'
          }
        `}
      >
        {IconComponent && (
          <IconComponent className="w-4 h-4 flex-shrink-0" />
        )}
        <span className="flex-1">{item.label}</span>
        {item.shortcut && (
          <span className="text-xs text-gray-400 dark:text-gray-500">
            {item.shortcut}
          </span>
        )}
        {item.checked && (
          <FiCheck className="w-4 h-4 text-green-500" />
        )}
      </button>
    );
  };

  return (
    <div className={`relative inline-block ${className}`} {...props}>
      {renderTrigger()}

      {/* Menu */}
      {isOpen && (
        <div
          ref={menuRef}
          className={`
            absolute z-50 ${positionClasses[position]} ${sizeConfig.menu}
            bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700
            rounded-lg shadow-lg min-w-48 max-w-xs
            ${menuClassName}
          `}
        >
          {items.map(renderMenuItem)}
        </div>
      )}
    </div>
  );
};

/**
 * Predefined action menu patterns
 */
export const createActionItems = {
  // Standard CRUD actions
  crud: (onView, onEdit, onDelete) => [
    { label: 'View', icon: FiEye, onClick: onView },
    { label: 'Edit', icon: FiEdit, onClick: onEdit },
    { type: 'divider' },
    { label: 'Delete', icon: FiTrash2, onClick: onDelete, variant: 'destructive' }
  ],

  // User management actions
  userActions: (onView, onEdit, onDeactivate, onDelete) => [
    { label: 'View Profile', icon: FiUser, onClick: onView },
    { label: 'Edit User', icon: FiEdit, onClick: onEdit },
    { type: 'divider' },
    { label: 'Deactivate', icon: FiUser, onClick: onDeactivate },
    { label: 'Delete', icon: FiTrash2, onClick: onDelete, variant: 'destructive' }
  ],

  // Content actions
  contentActions: (onView, onEdit, onDownload, onShare, onDelete) => [
    { label: 'View', icon: FiEye, onClick: onView },
    { label: 'Edit', icon: FiEdit, onClick: onEdit },
    { type: 'divider' },
    { label: 'Download', icon: FiDownload, onClick: onDownload },
    { label: 'Share', icon: FiShare2, onClick: onShare },
    { type: 'divider' },
    { label: 'Delete', icon: FiTrash2, onClick: onDelete, variant: 'destructive' }
  ]
};

export default ActionMenu;
