# Navigation Components

This directory contains reusable navigation components for the EduFair application. These components provide consistent navigation patterns and user interface elements.

## Components

### Breadcrumbs
Navigation breadcrumbs for showing page hierarchy and enabling quick navigation.

**Props:**
- `items` (array): Array of breadcrumb items
- `separator` (string): Separator style - 'chevron', 'slash', 'arrow'
- `showHome` (boolean): Show home breadcrumb (default: true)
- `homeUrl` (string): Home URL (default: '/')
- `homeLabel` (string): Home label (default: 'Home')
- `maxItems` (number): Maximum items to show before ellipsis

**Breadcrumb Item Structure:**
```javascript
{
  label: 'Page Name',
  href: '/page-url', // Optional for last item
  icon: IconComponent // Optional
}
```

**Usage:**
```jsx
import { Breadcrumbs, createBreadcrumbItem, breadcrumbPatterns } from '../components/ui/navigation';

// Manual breadcrumbs
const breadcrumbItems = [
  createBreadcrumbItem('Admin', '/admin'),
  createBreadcrumbItem('Users', '/admin/users'),
  createBreadcrumbItem('User Details')
];

<Breadcrumbs items={breadcrumbItems} />

// Using predefined patterns
<Breadcrumbs items={breadcrumbPatterns.adminUsers('123')} />

// With custom separator
<Breadcrumbs 
  items={breadcrumbItems} 
  separator="slash"
  maxItems={3}
/>
```

**Predefined Patterns:**
- `adminUsers(userId)`: Admin → Users → [User Details]
- `adminClassrooms(classroomId)`: Admin → Classrooms → [Classroom Details]
- `teacherClassrooms(classroomId, name)`: Teacher → Classrooms → [Classroom]
- `teacherExams(examId, title)`: Teacher → Exams → [Exam]
- `studentClassrooms(classroomId, name)`: Student → Classrooms → [Classroom]
- `studentExams(examId, title)`: Student → Exams → [Exam]

**Hook Usage:**
```jsx
import { useBreadcrumbs } from '../components/ui/navigation';

const breadcrumbs = useBreadcrumbs('teacherClassrooms', classroomId, classroom.name);
```

### Tabs
Tab navigation for organizing content into sections.

**Props:**
- `tabs` (array): Array of tab objects
- `activeTab` (string|number): Currently active tab ID or index
- `onTabChange` (function): Tab change callback
- `variant` (string): Tab style - 'default', 'pills', 'underline', 'cards'
- `size` (string): Tab size - 'sm', 'default', 'lg'
- `orientation` (string): Layout - 'horizontal', 'vertical'
- `fullWidth` (boolean): Full width tabs
- `scrollable` (boolean): Enable horizontal scrolling

**Tab Object Structure:**
```javascript
{
  id: 'tab-id',
  label: 'Tab Label',
  icon: IconComponent, // Optional
  content: <TabContent />, // Optional
  badge: '5', // Optional
  notification: true, // Optional
  disabled: false // Optional
}
```

**Usage:**
```jsx
import { Tabs, useTabs, PillTabs } from '../components/ui/navigation';

const tabs = [
  {
    id: 'overview',
    label: 'Overview',
    icon: FiHome,
    content: <OverviewContent />
  },
  {
    id: 'settings',
    label: 'Settings',
    icon: FiSettings,
    content: <SettingsContent />,
    badge: '2'
  }
];

const { activeTab, handleTabChange } = useTabs('overview');

<Tabs
  tabs={tabs}
  activeTab={activeTab}
  onTabChange={handleTabChange}
  variant="pills"
/>

// Vertical tabs
<Tabs
  tabs={tabs}
  activeTab={activeTab}
  onTabChange={handleTabChange}
  orientation="vertical"
/>
```

**Variants:**
- `SimpleTabs`: Tabs without content management
- `VerticalTabs`: Vertical orientation
- `PillTabs`: Pill-style tabs

### Pagination
Pagination controls for navigating through data sets.

**Props:**
- `currentPage` (number): Current page number
- `totalPages` (number): Total number of pages
- `totalItems` (number): Total number of items
- `itemsPerPage` (number): Items per page
- `onPageChange` (function): Page change callback
- `onItemsPerPageChange` (function): Items per page change callback
- `showItemsPerPage` (boolean): Show items per page selector
- `showPageInfo` (boolean): Show page information
- `showFirstLast` (boolean): Show first/last buttons
- `maxVisiblePages` (number): Maximum visible page numbers
- `variant` (string): Style - 'default', 'simple', 'compact'

**Usage:**
```jsx
import { Pagination, usePagination } from '../components/ui/navigation';

const {
  currentPage,
  itemsPerPage,
  totalPages,
  handlePageChange,
  handleItemsPerPageChange
} = usePagination({
  initialPage: 1,
  initialItemsPerPage: 10,
  totalItems: 250
});

<Pagination
  currentPage={currentPage}
  totalPages={totalPages}
  totalItems={250}
  itemsPerPage={itemsPerPage}
  onPageChange={handlePageChange}
  onItemsPerPageChange={handleItemsPerPageChange}
  showItemsPerPage={true}
  maxVisiblePages={5}
/>

// Simple pagination
<SimplePagination
  currentPage={currentPage}
  totalPages={totalPages}
  onPageChange={handlePageChange}
/>
```

**Variants:**
- `SimplePagination`: Just previous/next buttons
- `CompactPagination`: Minimal controls

### ActionMenu
Dropdown menu for actions with various trigger styles.

**Props:**
- `trigger` (string): Trigger style - 'dots', 'button', 'custom'
- `triggerLabel` (string): Button trigger label
- `triggerIcon` (component): Custom trigger icon
- `items` (array): Menu items array
- `onItemClick` (function): Item click callback
- `position` (string): Menu position - 'bottom-left', 'bottom-right', 'top-left', 'top-right'
- `size` (string): Menu size - 'sm', 'default', 'lg'
- `disabled` (boolean): Disable menu

**Menu Item Structure:**
```javascript
{
  label: 'Action Label',
  icon: IconComponent, // Optional
  onClick: (item, event) => {}, // Optional
  disabled: false, // Optional
  variant: 'destructive', // Optional for red styling
  shortcut: 'Ctrl+D', // Optional
  checked: true, // Optional
  type: 'divider' | 'header' // Optional
}
```

**Usage:**
```jsx
import { ActionMenu, createActionItems } from '../components/ui/navigation';

// Manual items
const menuItems = [
  { label: 'View', icon: FiEye, onClick: handleView },
  { label: 'Edit', icon: FiEdit, onClick: handleEdit },
  { type: 'divider' },
  { label: 'Delete', icon: FiTrash2, onClick: handleDelete, variant: 'destructive' }
];

<ActionMenu
  items={menuItems}
  trigger="dots"
  position="bottom-right"
/>

// Using predefined patterns
<ActionMenu
  items={createActionItems.crud(handleView, handleEdit, handleDelete)}
  trigger="button"
  triggerLabel="Actions"
/>

// Custom trigger
<ActionMenu items={menuItems}>
  <CustomTriggerComponent />
</ActionMenu>
```

**Predefined Item Patterns:**
- `createActionItems.crud(onView, onEdit, onDelete)`: Standard CRUD actions
- `createActionItems.userActions(onView, onEdit, onDeactivate, onDelete)`: User management
- `createActionItems.contentActions(onView, onEdit, onDownload, onShare, onDelete)`: Content actions

## Common Patterns

### Page Navigation
```jsx
import { Breadcrumbs, Tabs } from '../components/ui/navigation';

// Page with breadcrumbs and tabs
<div>
  <Breadcrumbs items={breadcrumbPatterns.teacherClassrooms(id, name)} />
  
  <Tabs
    tabs={classroomTabs}
    activeTab={activeTab}
    onTabChange={setActiveTab}
    variant="underline"
  />
</div>
```

### Data Table Navigation
```jsx
import { Pagination, ActionMenu } from '../components/ui/navigation';

// Table with pagination and row actions
<div>
  <DataTable
    data={data}
    columns={columns}
    actions={[
      {
        render: (item) => (
          <ActionMenu
            items={createActionItems.crud(
              () => handleView(item),
              () => handleEdit(item),
              () => handleDelete(item)
            )}
          />
        )
      }
    ]}
  />
  
  <Pagination
    currentPage={currentPage}
    totalPages={totalPages}
    onPageChange={handlePageChange}
  />
</div>
```

## Accessibility

- **Keyboard Navigation**: All components support keyboard navigation
- **ARIA Labels**: Proper ARIA labels and roles for screen readers
- **Focus Management**: Visible focus indicators and logical tab order
- **Screen Reader Support**: Semantic HTML and descriptive text

## Best Practices

1. **Use consistent navigation patterns** across similar pages
2. **Provide clear breadcrumbs** for deep navigation hierarchies
3. **Group related content** with tabs for better organization
4. **Implement proper pagination** for large data sets
5. **Use action menus** for secondary actions to reduce UI clutter
6. **Consider mobile experience** with responsive navigation
7. **Provide keyboard shortcuts** for power users

## Migration Guide

**Before:**
```jsx
// Custom breadcrumb implementation
<div className="flex items-center space-x-2">
  <Link to="/admin">Admin</Link>
  <span>/</span>
  <Link to="/admin/users">Users</Link>
  <span>/</span>
  <span>User Details</span>
</div>

// Custom tab implementation
<div className="border-b">
  <button 
    className={activeTab === 'overview' ? 'active' : ''}
    onClick={() => setActiveTab('overview')}
  >
    Overview
  </button>
</div>
```

**After:**
```jsx
// Using navigation components
<Breadcrumbs items={breadcrumbPatterns.adminUsers(userId)} />

<Tabs
  tabs={tabs}
  activeTab={activeTab}
  onTabChange={setActiveTab}
/>
```
