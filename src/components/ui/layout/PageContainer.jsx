import React from 'react';

/**
 * Reusable PageContainer component for consistent page layout
 * Provides consistent spacing, max-width, and responsive behavior
 */
const PageContainer = ({
  children,
  maxWidth = 'full', // 'sm', 'md', 'lg', 'xl', '2xl', 'full'
  padding = 'default', // 'none', 'sm', 'default', 'lg'
  className = '',
  centered = true,
  fluid = false,
  ...props
}) => {
  // Max width configurations
  const maxWidthClasses = {
    sm: 'max-w-sm',
    md: 'max-w-md',
    lg: 'max-w-4xl',
    xl: 'max-w-6xl',
    '2xl': 'max-w-7xl',
    full: 'max-w-full'
  };

  // Padding configurations
  const paddingClasses = {
    none: '',
    sm: 'px-4 py-4',
    default: 'px-4 sm:px-6 lg:px-8 py-6',
    lg: 'px-4 sm:px-6 lg:px-8 py-8'
  };

  const containerClasses = `
    ${fluid ? 'w-full' : maxWidthClasses[maxWidth] || maxWidthClasses.full}
    ${centered && !fluid ? 'mx-auto' : ''}
    ${paddingClasses[padding] || paddingClasses.default}
    ${className}
  `;

  return (
    <div className={containerClasses} {...props}>
      {children}
    </div>
  );
};

/**
 * Specialized PageContainer variants
 */

/**
 * Narrow container for forms and focused content
 */
export const NarrowPageContainer = ({ children, ...props }) => {
  return (
    <PageContainer maxWidth="lg" {...props}>
      {children}
    </PageContainer>
  );
};

/**
 * Wide container for dashboards and data tables
 */
export const WidePageContainer = ({ children, ...props }) => {
  return (
    <PageContainer maxWidth="full" {...props}>
      {children}
    </PageContainer>
  );
};

/**
 * Compact container with minimal padding
 */
export const CompactPageContainer = ({ children, ...props }) => {
  return (
    <PageContainer padding="sm" {...props}>
      {children}
    </PageContainer>
  );
};

/**
 * Full-width fluid container
 */
export const FluidPageContainer = ({ children, ...props }) => {
  return (
    <PageContainer fluid centered={false} {...props}>
      {children}
    </PageContainer>
  );
};

export default PageContainer;
