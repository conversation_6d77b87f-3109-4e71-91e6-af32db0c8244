# Layout Components

This directory contains reusable layout components for the EduFair application. These components provide consistent page structure, spacing, and content organization.

## Components

### PageHeader
Consistent page header with title, breadcrumbs, and actions.

**Props:**
- `title` (string): Page title
- `subtitle` (string): Page subtitle
- `description` (string): Page description
- `breadcrumbs` (array): Breadcrumb items
- `actions` (array): Action buttons
- `backButton` (boolean): Show back button
- `onBack` (function): Back button callback
- `size` (string): Header size - 'compact', 'default', 'large'
- `variant` (string): Header style - 'default', 'centered', 'minimal'

**Usage:**
```jsx
import { PageHeader, breadcrumbPatterns } from '../components/ui/layout';

<PageHeader
  title="User Management"
  subtitle="Manage system users and permissions"
  description="View, edit, and manage all users in the system."
  breadcrumbs={breadcrumbPatterns.adminUsers()}
  actions={[
    {
      label: 'Add User',
      variant: 'primary',
      icon: FiPlus,
      onClick: handleAddUser
    }
  ]}
/>
```

**Specialized Variants:**
- `SimplePageHeader`: Minimal header with title and actions
- `DashboardPageHeader`: Header with stats/metrics
- `FormPageHeader`: Header with save/cancel actions

```jsx
// Dashboard header with stats
<DashboardPageHeader
  title="Dashboard"
  subtitle="Overview of your classroom activity"
  stats={[
    { label: 'Total Students', value: '1,234', icon: FiUsers, change: 12 },
    { label: 'Active Classrooms', value: '56', icon: FiBookOpen, change: -2 }
  ]}
  actions={dashboardActions}
/>

// Form header
<FormPageHeader
  title="Edit User"
  subtitle="Update user information"
  onSave={handleSave}
  onCancel={handleCancel}
  isSaving={isLoading}
  saveDisabled={!isValid}
/>
```

### PageContainer
Consistent page container with responsive max-width and padding.

**Props:**
- `maxWidth` (string): Container width - 'sm', 'md', 'lg', 'xl', '2xl', 'full'
- `padding` (string): Container padding - 'none', 'sm', 'default', 'lg'
- `centered` (boolean): Center container (default: true)
- `fluid` (boolean): Full width container

**Usage:**
```jsx
import { PageContainer, NarrowPageContainer, WidePageContainer } from '../components/ui/layout';

// Standard page container
<PageContainer maxWidth="xl" padding="default">
  <PageContent />
</PageContainer>

// Narrow container for forms
<NarrowPageContainer>
  <UserForm />
</NarrowPageContainer>

// Wide container for data tables
<WidePageContainer>
  <DataTable />
</WidePageContainer>
```

**Specialized Variants:**
- `NarrowPageContainer`: For forms and focused content
- `WidePageContainer`: For dashboards and data tables
- `CompactPageContainer`: Minimal padding
- `FluidPageContainer`: Full-width fluid layout

### SectionDivider
Visual separation between content sections.

**Props:**
- `variant` (string): Divider style - 'line', 'space', 'gradient', 'dashed'
- `size` (string): Divider size - 'sm', 'default', 'lg'
- `orientation` (string): Direction - 'horizontal', 'vertical'
- `label` (string): Optional label text
- `labelPosition` (string): Label position - 'left', 'center', 'right'

**Usage:**
```jsx
import { 
  SectionDivider, 
  LineDivider, 
  GradientDivider, 
  LabeledDivider 
} from '../components/ui/layout';

// Basic line divider
<LineDivider />

// Gradient divider for subtle separation
<GradientDivider size="lg" />

// Labeled divider
<LabeledDivider label="User Information" />

// Vertical divider
<SectionDivider orientation="vertical" />
```

**Specialized Variants:**
- `LineDivider`: Simple line separator
- `SpaceDivider`: Invisible spacing
- `GradientDivider`: Subtle gradient separator
- `DashedDivider`: Dashed line separator
- `LabeledDivider`: Divider with text label
- `VerticalDivider`: Vertical separator

### ContentWrapper
Flexible wrapper for content sections with background, borders, and spacing.

**Props:**
- `variant` (string): Wrapper style - 'card', 'section', 'panel', 'minimal'
- `padding` (string): Internal padding - 'none', 'sm', 'default', 'lg'
- `shadow` (string): Drop shadow - 'none', 'sm', 'default', 'lg'
- `border` (boolean): Show border (default: true)
- `rounded` (string): Border radius - 'none', 'sm', 'default', 'lg', 'xl'
- `background` (string): Background style - 'default', 'muted', 'transparent'

**Usage:**
```jsx
import { 
  ContentWrapper, 
  Card, 
  Panel, 
  Section,
  GridWrapper,
  Stack
} from '../components/ui/layout';

// Card wrapper
<Card>
  <h3>Card Title</h3>
  <p>Card content</p>
</Card>

// Panel wrapper
<Panel shadow="lg" rounded="xl">
  <SidebarContent />
</Panel>

// Grid layout
<GridWrapper columns={3} gap="lg">
  <Card>Item 1</Card>
  <Card>Item 2</Card>
  <Card>Item 3</Card>
</GridWrapper>

// Vertical stack
<Stack gap="lg">
  <Section>Section 1</Section>
  <Section>Section 2</Section>
</Stack>
```

**Specialized Variants:**
- `Card`: Content cards with shadow and border
- `Panel`: Sidebar panels with muted background
- `Section`: Page sections with minimal styling
- `MinimalWrapper`: No styling wrapper
- `GridWrapper`: CSS Grid layout
- `FlexWrapper`: Flexbox layout
- `Stack`: Vertical stacking
- `Inline`: Horizontal inline layout

## Layout Patterns

### Standard Page Layout
```jsx
import { 
  PageContainer, 
  PageHeader, 
  ContentWrapper, 
  SectionDivider 
} from '../components/ui/layout';

<PageContainer>
  <PageHeader
    title="Page Title"
    breadcrumbs={breadcrumbs}
    actions={actions}
  />
  
  <SectionDivider />
  
  <ContentWrapper variant="card">
    <PageContent />
  </ContentWrapper>
</PageContainer>
```

### Dashboard Layout
```jsx
<WidePageContainer>
  <DashboardPageHeader
    title="Dashboard"
    stats={dashboardStats}
    actions={dashboardActions}
  />
  
  <GridWrapper columns={2} gap="lg">
    <Card>
      <ChartComponent />
    </Card>
    <Card>
      <MetricsComponent />
    </Card>
  </GridWrapper>
</WidePageContainer>
```

### Form Layout
```jsx
<NarrowPageContainer>
  <FormPageHeader
    title="Create User"
    onSave={handleSave}
    onCancel={handleCancel}
    isSaving={isLoading}
  />
  
  <Card>
    <Stack gap="lg">
      <Section>
        <h3>Basic Information</h3>
        <FormFields />
      </Section>
      
      <LabeledDivider label="Additional Details" />
      
      <Section>
        <AdditionalFields />
      </Section>
    </Stack>
  </Card>
</NarrowPageContainer>
```

### Data Table Layout
```jsx
<WidePageContainer>
  <PageHeader
    title="Users"
    actions={[
      { label: 'Add User', onClick: handleAdd }
    ]}
  />
  
  <Card padding="none">
    <DataTable data={users} />
  </Card>
  
  <Pagination {...paginationProps} />
</WidePageContainer>
```

## Responsive Design

All layout components are responsive by default:
- **Mobile**: Single column, reduced padding
- **Tablet**: Adaptive columns, medium padding
- **Desktop**: Full layout, standard padding

## Best Practices

1. **Use consistent containers** for similar page types
2. **Combine layout components** for complex layouts
3. **Maintain visual hierarchy** with proper spacing
4. **Use semantic variants** (Card for content, Panel for sidebars)
5. **Consider mobile experience** with responsive design
6. **Group related content** with appropriate wrappers
7. **Provide clear separation** between sections

## Accessibility

- **Semantic HTML**: Proper heading hierarchy and landmarks
- **Focus Management**: Logical tab order and focus indicators
- **Screen Reader Support**: Descriptive labels and structure
- **Keyboard Navigation**: All interactive elements accessible

## Migration Guide

**Before:**
```jsx
<div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
  <div className="mb-6">
    <h1 className="text-2xl font-bold">Page Title</h1>
  </div>
  
  <div className="bg-white rounded-lg shadow p-6">
    <Content />
  </div>
</div>
```

**After:**
```jsx
<PageContainer>
  <PageHeader title="Page Title" />
  <Card>
    <Content />
  </Card>
</PageContainer>
```
