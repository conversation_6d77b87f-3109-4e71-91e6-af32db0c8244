import React from 'react';

/**
 * Reusable SectionDivider component for visual separation
 * Provides consistent spacing and styling for content sections
 */
const SectionDivider = ({
  variant = 'line', // 'line', 'space', 'gradient', 'dashed'
  size = 'default', // 'sm', 'default', 'lg'
  orientation = 'horizontal', // 'horizontal', 'vertical'
  label,
  labelPosition = 'center', // 'left', 'center', 'right'
  className = '',
  ...props
}) => {
  // Size configurations
  const sizeClasses = {
    horizontal: {
      sm: 'my-4',
      default: 'my-6',
      lg: 'my-8'
    },
    vertical: {
      sm: 'mx-2 h-8',
      default: 'mx-4 h-12',
      lg: 'mx-6 h-16'
    }
  };

  // Variant configurations
  const variantClasses = {
    line: 'border-gray-200 dark:border-gray-700',
    space: 'border-transparent',
    gradient: 'border-transparent bg-gradient-to-r from-transparent via-gray-300 dark:via-gray-600 to-transparent',
    dashed: 'border-gray-200 dark:border-gray-700 border-dashed'
  };

  const spacing = sizeClasses[orientation][size] || sizeClasses[orientation].default;

  // Horizontal divider
  if (orientation === 'horizontal') {
    if (label) {
      return (
        <div className={`relative ${spacing} ${className}`} {...props}>
          <div className="absolute inset-0 flex items-center">
            <div className={`
              w-full border-t ${variantClasses[variant]}
              ${variant === 'gradient' ? 'h-px' : ''}
            `} />
          </div>
          <div className={`
            relative flex ${
              labelPosition === 'left' ? 'justify-start' :
              labelPosition === 'right' ? 'justify-end' :
              'justify-center'
            }
          `}>
            <span className="bg-white dark:bg-gray-900 px-4 text-sm text-gray-500 dark:text-gray-400 font-medium">
              {label}
            </span>
          </div>
        </div>
      );
    }

    if (variant === 'space') {
      return <div className={spacing} {...props} />;
    }

    if (variant === 'gradient') {
      return (
        <div className={`${spacing} ${className}`} {...props}>
          <div className="h-px bg-gradient-to-r from-transparent via-gray-300 dark:via-gray-600 to-transparent" />
        </div>
      );
    }

    return (
      <hr 
        className={`
          border-t ${variantClasses[variant]} ${spacing} ${className}
        `} 
        {...props} 
      />
    );
  }

  // Vertical divider
  if (variant === 'space') {
    return <div className={spacing} {...props} />;
  }

  if (variant === 'gradient') {
    return (
      <div className={`${spacing} ${className}`} {...props}>
        <div className="w-px h-full bg-gradient-to-b from-transparent via-gray-300 dark:via-gray-600 to-transparent" />
      </div>
    );
  }

  return (
    <div 
      className={`
        border-l ${variantClasses[variant]} ${spacing} ${className}
      `} 
      {...props} 
    />
  );
};

/**
 * Specialized SectionDivider variants
 */

/**
 * Simple line divider
 */
export const LineDivider = (props) => {
  return <SectionDivider variant="line" {...props} />;
};

/**
 * Space divider (invisible spacing)
 */
export const SpaceDivider = (props) => {
  return <SectionDivider variant="space" {...props} />;
};

/**
 * Gradient divider for subtle separation
 */
export const GradientDivider = (props) => {
  return <SectionDivider variant="gradient" {...props} />;
};

/**
 * Dashed divider for less prominent separation
 */
export const DashedDivider = (props) => {
  return <SectionDivider variant="dashed" {...props} />;
};

/**
 * Labeled divider with text
 */
export const LabeledDivider = ({ label, ...props }) => {
  return <SectionDivider label={label} {...props} />;
};

/**
 * Vertical divider for side-by-side content
 */
export const VerticalDivider = (props) => {
  return <SectionDivider orientation="vertical" {...props} />;
};

export default SectionDivider;
