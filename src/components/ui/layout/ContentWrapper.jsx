import React from 'react';

/**
 * Reusable ContentWrapper component for consistent content layout
 * Provides background, borders, shadows, and spacing for content sections
 */
const ContentWrapper = ({
  children,
  variant = 'card', // 'card', 'section', 'panel', 'minimal'
  padding = 'default', // 'none', 'sm', 'default', 'lg'
  shadow = 'default', // 'none', 'sm', 'default', 'lg'
  border = true,
  rounded = 'default', // 'none', 'sm', 'default', 'lg', 'xl'
  background = 'default', // 'default', 'muted', 'transparent'
  className = '',
  ...props
}) => {
  // Padding configurations
  const paddingClasses = {
    none: '',
    sm: 'p-4',
    default: 'p-6',
    lg: 'p-8'
  };

  // Shadow configurations
  const shadowClasses = {
    none: '',
    sm: 'shadow-sm',
    default: 'shadow',
    lg: 'shadow-lg'
  };

  // Rounded configurations
  const roundedClasses = {
    none: '',
    sm: 'rounded-sm',
    default: 'rounded-lg',
    lg: 'rounded-xl',
    xl: 'rounded-2xl'
  };

  // Background configurations
  const backgroundClasses = {
    default: 'bg-white dark:bg-gray-800',
    muted: 'bg-gray-50 dark:bg-gray-900',
    transparent: 'bg-transparent'
  };

  // Border configurations
  const borderClasses = border 
    ? 'border border-gray-200 dark:border-gray-700' 
    : '';

  // Variant-specific configurations
  const variantConfigs = {
    card: {
      padding: paddingClasses[padding] || paddingClasses.default,
      shadow: shadowClasses[shadow] || shadowClasses.default,
      rounded: roundedClasses[rounded] || roundedClasses.default,
      background: backgroundClasses[background] || backgroundClasses.default,
      border: borderClasses
    },
    section: {
      padding: paddingClasses[padding] || paddingClasses.lg,
      shadow: shadowClasses.none,
      rounded: roundedClasses.none,
      background: backgroundClasses[background] || backgroundClasses.default,
      border: borderClasses
    },
    panel: {
      padding: paddingClasses[padding] || paddingClasses.default,
      shadow: shadowClasses[shadow] || shadowClasses.sm,
      rounded: roundedClasses[rounded] || roundedClasses.default,
      background: backgroundClasses[background] || backgroundClasses.muted,
      border: borderClasses
    },
    minimal: {
      padding: paddingClasses[padding] || paddingClasses.sm,
      shadow: shadowClasses.none,
      rounded: roundedClasses.none,
      background: backgroundClasses.transparent,
      border: ''
    }
  };

  const config = variantConfigs[variant] || variantConfigs.card;

  const wrapperClasses = `
    ${config.background}
    ${config.padding}
    ${config.shadow}
    ${config.rounded}
    ${config.border}
    ${className}
  `;

  return (
    <div className={wrapperClasses} {...props}>
      {children}
    </div>
  );
};

/**
 * Specialized ContentWrapper variants
 */

/**
 * Card wrapper for content cards
 */
export const Card = ({ children, ...props }) => {
  return (
    <ContentWrapper variant="card" {...props}>
      {children}
    </ContentWrapper>
  );
};

/**
 * Panel wrapper for sidebar panels
 */
export const Panel = ({ children, ...props }) => {
  return (
    <ContentWrapper variant="panel" {...props}>
      {children}
    </ContentWrapper>
  );
};

/**
 * Section wrapper for page sections
 */
export const Section = ({ children, ...props }) => {
  return (
    <ContentWrapper variant="section" {...props}>
      {children}
    </ContentWrapper>
  );
};

/**
 * Minimal wrapper with no styling
 */
export const MinimalWrapper = ({ children, ...props }) => {
  return (
    <ContentWrapper variant="minimal" {...props}>
      {children}
    </ContentWrapper>
  );
};

/**
 * Grid wrapper for grid layouts
 */
export const GridWrapper = ({ 
  children, 
  columns = 1, 
  gap = 'default', // 'sm', 'default', 'lg'
  className = '',
  ...props 
}) => {
  const gapClasses = {
    sm: 'gap-4',
    default: 'gap-6',
    lg: 'gap-8'
  };

  const gridClasses = `
    grid grid-cols-1 
    ${columns > 1 ? `sm:grid-cols-${Math.min(columns, 2)} lg:grid-cols-${columns}` : ''}
    ${gapClasses[gap] || gapClasses.default}
    ${className}
  `;

  return (
    <div className={gridClasses} {...props}>
      {children}
    </div>
  );
};

/**
 * Flex wrapper for flex layouts
 */
export const FlexWrapper = ({ 
  children, 
  direction = 'row', // 'row', 'col'
  align = 'start', // 'start', 'center', 'end', 'stretch'
  justify = 'start', // 'start', 'center', 'end', 'between', 'around'
  gap = 'default', // 'sm', 'default', 'lg'
  wrap = false,
  className = '',
  ...props 
}) => {
  const directionClasses = {
    row: 'flex-row',
    col: 'flex-col'
  };

  const alignClasses = {
    start: 'items-start',
    center: 'items-center',
    end: 'items-end',
    stretch: 'items-stretch'
  };

  const justifyClasses = {
    start: 'justify-start',
    center: 'justify-center',
    end: 'justify-end',
    between: 'justify-between',
    around: 'justify-around'
  };

  const gapClasses = {
    sm: direction === 'row' ? 'space-x-4' : 'space-y-4',
    default: direction === 'row' ? 'space-x-6' : 'space-y-6',
    lg: direction === 'row' ? 'space-x-8' : 'space-y-8'
  };

  const flexClasses = `
    flex ${directionClasses[direction]}
    ${alignClasses[align]}
    ${justifyClasses[justify]}
    ${gapClasses[gap] || gapClasses.default}
    ${wrap ? 'flex-wrap' : ''}
    ${className}
  `;

  return (
    <div className={flexClasses} {...props}>
      {children}
    </div>
  );
};

/**
 * Stack wrapper for vertical stacking
 */
export const Stack = ({ children, gap = 'default', className = '', ...props }) => {
  return (
    <FlexWrapper 
      direction="col" 
      gap={gap} 
      className={className} 
      {...props}
    >
      {children}
    </FlexWrapper>
  );
};

/**
 * Inline wrapper for horizontal inline layout
 */
export const Inline = ({ children, gap = 'default', className = '', ...props }) => {
  return (
    <FlexWrapper 
      direction="row" 
      align="center" 
      gap={gap} 
      wrap={true}
      className={className} 
      {...props}
    >
      {children}
    </FlexWrapper>
  );
};

export default ContentWrapper;
