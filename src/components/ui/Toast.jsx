import React, { createContext, useContext, useState, useCallback, useEffect } from 'react';
import { createPortal } from 'react-dom';
import {
  FiCheckCircle,
  FiAlertCircle,
  FiAlertTriangle,
  FiInfo,
  FiX
} from 'react-icons/fi';

// Toast Context
const ToastContext = createContext();

// Toast types
const TOAST_TYPES = {
  success: {
    icon: FiCheckCircle,
    className: 'bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800 text-green-800 dark:text-green-200'
  },
  error: {
    icon: FiAlertCircle,
    className: 'bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800 text-red-800 dark:text-red-200'
  },
  warning: {
    icon: FiAlertTriangle,
    className: 'bg-yellow-50 dark:bg-yellow-900/20 border-yellow-200 dark:border-yellow-800 text-yellow-800 dark:text-yellow-200'
  },
  info: {
    icon: FiInfo,
    className: 'bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800 text-blue-800 dark:text-blue-200'
  }
};

// Individual Toast Component
const Toast = ({ 
  id, 
  type = 'info', 
  title, 
  message, 
  duration = 5000, 
  action,
  onClose,
  persistent = false
}) => {
  const [isVisible, setIsVisible] = useState(false);
  const [isLeaving, setIsLeaving] = useState(false);

  const toastConfig = TOAST_TYPES[type];
  const IconComponent = toastConfig.icon;

  useEffect(() => {
    // Trigger entrance animation
    const timer = setTimeout(() => setIsVisible(true), 10);
    return () => clearTimeout(timer);
  }, []);

  useEffect(() => {
    if (!persistent && duration > 0) {
      const timer = setTimeout(() => {
        handleClose();
      }, duration);
      return () => clearTimeout(timer);
    }
  }, [duration, persistent]);

  const handleClose = useCallback(() => {
    setIsLeaving(true);
    setTimeout(() => {
      onClose(id);
    }, 300); // Match animation duration
  }, [id, onClose]);

  return (
    <div
      className={`
        transform transition-all duration-300 ease-in-out mb-4
        ${isVisible && !isLeaving ? 'translate-x-0 opacity-100' : 'translate-x-full opacity-0'}
      `}
    >
      <div className={`
        max-w-sm w-full rounded-lg border shadow-lg p-4
        ${toastConfig.className}
      `}>
        <div className="flex items-start">
          <div className="flex-shrink-0">
            <IconComponent className="w-5 h-5" />
          </div>
          
          <div className="ml-3 flex-1">
            {title && (
              <p className="text-sm font-semibold">
                {title}
              </p>
            )}
            {message && (
              <p className={`text-sm ${title ? 'mt-1' : ''}`}>
                {message}
              </p>
            )}
            {action && (
              <div className="mt-3">
                {action}
              </div>
            )}
          </div>
          
          <div className="ml-4 flex-shrink-0">
            <button
              onClick={handleClose}
              className="inline-flex rounded-md p-1.5 hover:bg-black/10 dark:hover:bg-white/10 transition-colors"
            >
              <FiX className="w-4 h-4" />
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

// Toast Container
const ToastContainer = ({ toasts, onClose }) => {
  if (toasts.length === 0) return null;

  return createPortal(
    <div className="fixed top-4 right-4 z-50 space-y-4">
      {toasts.map((toast) => (
        <Toast
          key={toast.id}
          {...toast}
          onClose={onClose}
        />
      ))}
    </div>,
    document.body
  );
};

// Toast Provider
export const ToastProvider = ({ children }) => {
  const [toasts, setToasts] = useState([]);

  const addToast = useCallback((toast) => {
    const id = Date.now() + Math.random();
    const newToast = { id, ...toast };
    
    setToasts(prev => [...prev, newToast]);
    
    return id;
  }, []);

  const removeToast = useCallback((id) => {
    setToasts(prev => prev.filter(toast => toast.id !== id));
  }, []);

  const clearAllToasts = useCallback(() => {
    setToasts([]);
  }, []);

  // Convenience methods
  const toast = {
    success: (title, message, options = {}) => 
      addToast({ type: 'success', title, message, ...options }),
    
    error: (title, message, options = {}) => 
      addToast({ type: 'error', title, message, ...options }),
    
    warning: (title, message, options = {}) => 
      addToast({ type: 'warning', title, message, ...options }),
    
    info: (title, message, options = {}) => 
      addToast({ type: 'info', title, message, ...options }),
    
    custom: (options) => addToast(options),
    
    remove: removeToast,
    clear: clearAllToasts
  };

  return (
    <ToastContext.Provider value={toast}>
      {children}
      <ToastContainer toasts={toasts} onClose={removeToast} />
    </ToastContext.Provider>
  );
};

// Hook to use toast
export const useToast = () => {
  const context = useContext(ToastContext);
  if (!context) {
    throw new Error('useToast must be used within a ToastProvider');
  }
  return context;
};

// Higher-order component for automatic error/success toasts
export const withToastNotifications = (Component) => {
  return (props) => {
    const toast = useToast();
    
    return (
      <Component 
        {...props} 
        toast={toast}
        onSuccess={(message, title = 'Success') => toast.success(title, message)}
        onError={(message, title = 'Error') => toast.error(title, message)}
      />
    );
  };
};

// Toast hook for async operations
export const useAsyncToast = () => {
  const toast = useToast();
  
  const executeWithToast = useCallback(async (
    asyncFn, 
    {
      loadingMessage = 'Processing...',
      successMessage = 'Operation completed successfully',
      errorMessage = 'Operation failed',
      successTitle = 'Success',
      errorTitle = 'Error'
    } = {}
  ) => {
    const loadingToastId = toast.info('Loading', loadingMessage, { 
      persistent: true,
      duration: 0 
    });
    
    try {
      const result = await asyncFn();
      toast.remove(loadingToastId);
      toast.success(successTitle, successMessage);
      return result;
    } catch (error) {
      toast.remove(loadingToastId);
      toast.error(errorTitle, error.message || errorMessage);
      throw error;
    }
  }, [toast]);
  
  return { executeWithToast, toast };
};

// Predefined toast configurations
export const toastPresets = {
  saved: () => ({ 
    type: 'success', 
    title: 'Saved', 
    message: 'Your changes have been saved successfully' 
  }),
  
  deleted: () => ({ 
    type: 'success', 
    title: 'Deleted', 
    message: 'Item has been deleted successfully' 
  }),
  
  networkError: () => ({ 
    type: 'error', 
    title: 'Network Error', 
    message: 'Please check your internet connection and try again',
    persistent: true
  }),
  
  validationError: (field) => ({ 
    type: 'warning', 
    title: 'Validation Error', 
    message: `Please check the ${field} field and try again` 
  }),
  
  unauthorized: () => ({ 
    type: 'error', 
    title: 'Unauthorized', 
    message: 'You do not have permission to perform this action',
    persistent: true
  })
};

export default Toast;
