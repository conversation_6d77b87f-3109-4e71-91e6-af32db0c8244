import React from 'react';
import { 
  Fi<PERSON>sers, 
  FiBookOpen, 
  FiCalendar, 
  FiCheckCircle, 
  FiClock,
  FiUser,
  FiFileText
} from 'react-icons/fi';
import DataTable from '../DataTable';
import { ViewButton, EditButton, DeleteButton, QuickActionBar } from '../buttons';

/**
 * Specialized ClassroomTable component for displaying classroom data
 * Provides consistent classroom data presentation with built-in actions
 */
const ClassroomTable = ({
  classrooms = [],
  onView,
  onEdit,
  onDelete,
  onBulkAction,
  showActions = true,
  showTeacher = false,
  showStats = true,
  userRole = 'teacher', // 'teacher', 'student', 'admin'
  selectable = false,
  ...props
}) => {
  // Define classroom-specific columns
  const columns = [
    {
      key: 'icon',
      label: '',
      sortable: false,
      hideOnMobile: false,
      width: '60px',
      render: (value, classroom) => (
        <div className="p-2 bg-blue-100 dark:bg-blue-900 rounded-lg flex-shrink-0">
          <FiBookOpen className="text-blue-600 dark:text-blue-400" size={20} />
        </div>
      )
    },
    {
      key: 'name',
      label: 'Classroom',
      sortable: true,
      render: (value, classroom) => (
        <div className="min-w-0">
          <div className="font-medium text-gray-900 dark:text-gray-100 truncate">
            {classroom.name || 'Untitled Classroom'}
          </div>
          {classroom.description && (
            <div className="text-sm text-gray-500 dark:text-gray-400 truncate">
              {classroom.description}
            </div>
          )}
          {classroom.subject && (
            <div className="mt-1">
              <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-violet-100 text-violet-800 dark:bg-violet-900 dark:text-violet-200">
                {classroom.subject.name || classroom.subject}
              </span>
            </div>
          )}
        </div>
      )
    }
  ];

  // Add teacher column if enabled (for student/admin view)
  if (showTeacher) {
    columns.push({
      key: 'teacher',
      label: 'Teacher',
      sortable: true,
      hideOnMobile: true,
      render: (value, classroom) => {
        const teacher = classroom.teacher || classroom.teacher_info;
        if (!teacher) return 'N/A';
        
        return (
          <div className="flex items-center space-x-2">
            <FiUser className="w-4 h-4 text-gray-400" />
            <span className="text-sm text-gray-900 dark:text-gray-100">
              {teacher.username || teacher.name || 'Unknown Teacher'}
            </span>
          </div>
        );
      }
    });
  }

  // Add stats columns if enabled
  if (showStats) {
    columns.push(
      {
        key: 'student_count',
        label: 'Students',
        sortable: true,
        hideOnMobile: true,
        render: (value, classroom) => (
          <div className="flex items-center space-x-1 text-sm text-gray-600 dark:text-gray-400">
            <FiUsers className="w-4 h-4" />
            <span>{classroom.student_count || classroom.students?.length || 0}</span>
          </div>
        )
      },
      {
        key: 'content_count',
        label: 'Content',
        sortable: false,
        hideOnMobile: true,
        render: (value, classroom) => (
          <div className="space-y-1">
            {classroom.exam_count !== undefined && (
              <div className="flex items-center space-x-1 text-xs text-gray-500 dark:text-gray-400">
                <FiFileText className="w-3 h-3" />
                <span>{classroom.exam_count} Exams</span>
              </div>
            )}
            {classroom.assignment_count !== undefined && (
              <div className="flex items-center space-x-1 text-xs text-gray-500 dark:text-gray-400">
                <FiFileText className="w-3 h-3" />
                <span>{classroom.assignment_count} Assignments</span>
              </div>
            )}
          </div>
        )
      }
    );
  }

  // Add status column
  columns.push({
    key: 'status',
    label: 'Status',
    sortable: true,
    hideOnMobile: true,
    render: (value, classroom) => {
      const status = classroom.status || 'active';
      const getStatusConfig = (status) => {
        const configs = {
          active: {
            icon: FiCheckCircle,
            color: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
          },
          inactive: {
            icon: FiClock,
            color: 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200'
          },
          archived: {
            icon: FiClock,
            color: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
          },
          draft: {
            icon: FiClock,
            color: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
          }
        };
        return configs[status] || configs.active;
      };

      const config = getStatusConfig(status);
      const IconComponent = config.icon;

      return (
        <span className={`
          inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
          ${config.color}
        `}>
          <IconComponent className="w-3 h-3 mr-1" />
          {status.charAt(0).toUpperCase() + status.slice(1)}
        </span>
      );
    }
  });

  // Add created date column
  columns.push({
    key: 'created_at',
    label: 'Created',
    sortable: true,
    hideOnMobile: true,
    render: (value) => {
      if (!value) return 'N/A';
      return (
        <div className="text-sm text-gray-500 dark:text-gray-400 flex items-center">
          <FiCalendar className="w-3 h-3 mr-1" />
          {new Date(value).toLocaleDateString()}
        </div>
      );
    }
  });

  // Add actions column if enabled
  if (showActions && (onView || onEdit || onDelete)) {
    columns.push({
      key: 'actions',
      label: 'Actions',
      sortable: false,
      width: '120px',
      render: (value, classroom) => (
        <QuickActionBar
          onView={onView ? () => onView(classroom) : undefined}
          onEdit={onEdit && userRole === 'teacher' ? () => onEdit(classroom) : undefined}
          onDelete={onDelete && userRole === 'teacher' ? () => onDelete(classroom) : undefined}
          showView={!!onView}
          showEdit={!!onEdit && userRole === 'teacher'}
          showDelete={!!onDelete && userRole === 'teacher'}
        />
      )
    });
  }

  // Bulk actions for classroom management
  const bulkActions = [];
  if (onBulkAction && userRole === 'teacher') {
    bulkActions.push(
      {
        label: 'Archive Selected',
        action: 'archive',
        onClick: (selectedClassrooms) => onBulkAction('archive', selectedClassrooms)
      },
      {
        label: 'Activate Selected',
        action: 'activate',
        onClick: (selectedClassrooms) => onBulkAction('activate', selectedClassrooms)
      },
      {
        label: 'Delete Selected',
        action: 'delete',
        onClick: (selectedClassrooms) => onBulkAction('delete', selectedClassrooms),
        variant: 'danger'
      }
    );
  }

  // Custom empty state for classrooms
  const emptyState = (
    <div className="text-center py-12">
      <div className="w-16 h-16 mx-auto mb-4 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center">
        <FiBookOpen className="w-8 h-8 text-gray-400" />
      </div>
      <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
        No classrooms found
      </h3>
      <p className="text-gray-500 dark:text-gray-400">
        {userRole === 'teacher' 
          ? "You haven't created any classrooms yet. Create your first classroom to get started."
          : "No classrooms match your current filters. Try adjusting your search criteria."
        }
      </p>
    </div>
  );

  return (
    <DataTable
      data={classrooms}
      columns={columns}
      selectable={selectable}
      bulkActions={bulkActions}
      emptyState={emptyState}
      {...props}
    />
  );
};

/**
 * Compact ClassroomTable variant for smaller spaces
 */
export const CompactClassroomTable = ({
  classrooms = [],
  onRowClick,
  showTeacher = false,
  ...props
}) => {
  const columns = [
    {
      key: 'classroom_info',
      label: 'Classroom',
      sortable: true,
      render: (value, classroom) => (
        <div className="flex items-center space-x-3">
          <div className="p-1.5 bg-blue-100 dark:bg-blue-900 rounded-lg flex-shrink-0">
            <FiBookOpen className="text-blue-600 dark:text-blue-400" size={16} />
          </div>
          <div className="min-w-0">
            <div className="font-medium text-gray-900 dark:text-gray-100 truncate">
              {classroom.name || 'Untitled Classroom'}
            </div>
            <div className="text-xs text-gray-500 dark:text-gray-400 flex items-center space-x-2">
              <span className="flex items-center">
                <FiUsers className="w-3 h-3 mr-1" />
                {classroom.student_count || 0}
              </span>
              {showTeacher && classroom.teacher && (
                <span className="flex items-center">
                  <FiUser className="w-3 h-3 mr-1" />
                  {classroom.teacher.username || 'Unknown'}
                </span>
              )}
            </div>
          </div>
        </div>
      )
    },
    {
      key: 'status',
      label: 'Status',
      sortable: true,
      render: (value, classroom) => {
        const status = classroom.status || 'active';
        return (
          <span className="text-sm text-gray-600 dark:text-gray-400 capitalize">
            {status}
          </span>
        );
      }
    }
  ];

  return (
    <DataTable
      data={classrooms}
      columns={columns}
      onRowClick={onRowClick}
      pagination={false}
      className="compact-table"
      {...props}
    />
  );
};

export default ClassroomTable;
