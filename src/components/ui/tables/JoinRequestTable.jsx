import React from 'react';
import { 
  FiUser, 
  FiMail, 
  FiPhone, 
  FiCalendar, 
  FiCheck, 
  FiX,
  FiClock,
  FiBookOpen,
  FiEye
} from 'react-icons/fi';
import DataTable from '../DataTable';
import { ViewButton, ActionButtonGroup } from '../buttons';

/**
 * Specialized JoinRequestTable component for displaying classroom join requests
 * Handles the specific API response structure for join requests
 */
const JoinRequestTable = ({
  requests = [],
  onApprove,
  onReject,
  onView,
  onBulkAction,
  showActions = true,
  showClassroomInfo = true,
  userRole = 'teacher', // 'teacher', 'admin'
  selectable = false,
  ...props
}) => {
  // Define join request specific columns
  const columns = [
    {
      key: 'student_avatar',
      label: '',
      sortable: false,
      hideOnMobile: false,
      width: '60px',
      render: (value, request) => {
        const student = request.student_user || request.student;
        return (
          <div className="w-10 h-10 bg-blue-100 dark:bg-blue-900/30 rounded-full flex items-center justify-center flex-shrink-0">
            {student?.profile_picture ? (
              <img 
                src={student.profile_picture} 
                alt={student.username} 
                className="w-full h-full rounded-full object-cover"
              />
            ) : (
              <span className="text-sm font-semibold text-blue-600 dark:text-blue-400">
                {student?.username?.charAt(0).toUpperCase() || 'S'}
              </span>
            )}
          </div>
        );
      }
    },
    {
      key: 'student_info',
      label: 'Student',
      sortable: true,
      render: (value, request) => {
        const student = request.student_user || request.student;
        if (!student) return 'Unknown Student';
        
        return (
          <div className="min-w-0">
            <div className="font-medium text-gray-900 dark:text-gray-100 truncate">
              {student.username || 'Unknown Student'}
            </div>
            <div className="text-sm text-gray-500 dark:text-gray-400 flex items-center truncate">
              <FiMail className="w-3 h-3 mr-1 flex-shrink-0" />
              {student.email || 'N/A'}
            </div>
            {student.mobile && (
              <div className="text-sm text-gray-500 dark:text-gray-400 flex items-center truncate">
                <FiPhone className="w-3 h-3 mr-1 flex-shrink-0" />
                {student.mobile}
              </div>
            )}
          </div>
        );
      }
    }
  ];

  // Add classroom column if enabled
  if (showClassroomInfo) {
    columns.push({
      key: 'classroom_info',
      label: 'Classroom',
      sortable: true,
      hideOnMobile: true,
      render: (value, request) => {
        const classroomId = request.classroom_id || request.classroom;
        const classroomName = typeof request.classroom === 'object' 
          ? request.classroom.name 
          : `Classroom ${classroomId?.slice(-8)}`;
        
        return (
          <div className="flex items-center space-x-2">
            <FiBookOpen className="w-4 h-4 text-gray-400 flex-shrink-0" />
            <span className="text-sm text-gray-900 dark:text-gray-100 truncate">
              {classroomName}
            </span>
          </div>
        );
      }
    });
  }

  // Add student details column
  columns.push({
    key: 'student_details',
    label: 'Details',
    sortable: false,
    hideOnMobile: true,
    render: (value, request) => {
      const student = request.student_user || request.student;
      if (!student) return 'N/A';
      
      return (
        <div className="space-y-1">
          <div className="text-xs text-gray-500 dark:text-gray-400">
            Type: {student.user_type?.charAt(0).toUpperCase() + student.user_type?.slice(1) || 'Student'}
          </div>
          {student.country && (
            <div className="text-xs text-gray-500 dark:text-gray-400">
              Country: {student.country}
            </div>
          )}
          <div className="flex items-center space-x-2">
            {student.is_email_verified && (
              <span className="inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                Email ✓
              </span>
            )}
            {student.is_mobile_verified && (
              <span className="inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                Mobile ✓
              </span>
            )}
          </div>
        </div>
      );
    }
  });

  // Add status column
  columns.push({
    key: 'status',
    label: 'Status',
    sortable: true,
    render: (value, request) => {
      const status = request.status || 'pending';
      const getStatusConfig = (status) => {
        const configs = {
          pending: {
            icon: FiClock,
            color: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
          },
          approved: {
            icon: FiCheck,
            color: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
          },
          rejected: {
            icon: FiX,
            color: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
          }
        };
        return configs[status] || configs.pending;
      };

      const config = getStatusConfig(status);
      const IconComponent = config.icon;

      return (
        <span className={`
          inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
          ${config.color}
        `}>
          <IconComponent className="w-3 h-3 mr-1" />
          {status.charAt(0).toUpperCase() + status.slice(1)}
        </span>
      );
    }
  });

  // Add request date column
  columns.push({
    key: 'created_at',
    label: 'Requested',
    sortable: true,
    hideOnMobile: true,
    render: (value) => {
      if (!value) return 'N/A';
      return (
        <div className="text-sm text-gray-500 dark:text-gray-400 flex items-center">
          <FiCalendar className="w-3 h-3 mr-1" />
          {new Date(value).toLocaleDateString()}
        </div>
      );
    }
  });

  // Add actions column if enabled
  if (showActions) {
    columns.push({
      key: 'actions',
      label: 'Actions',
      sortable: false,
      width: '150px',
      render: (value, request) => {
        const isPending = request.status === 'pending' || !request.status;
        
        return (
          <ActionButtonGroup spacing="tight">
            {onView && (
              <ViewButton 
                onClick={() => onView(request)}
                tooltip="View Details"
              />
            )}
            
            {isPending && onApprove && (
              <button
                onClick={() => onApprove(request)}
                className="p-2 text-green-600 hover:text-green-700 dark:text-green-400 dark:hover:text-green-300 transition-colors rounded-lg hover:bg-green-50 dark:hover:bg-green-900/20"
                title="Approve Request"
              >
                <FiCheck className="w-4 h-4" />
              </button>
            )}
            
            {isPending && onReject && (
              <button
                onClick={() => onReject(request)}
                className="p-2 text-red-600 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300 transition-colors rounded-lg hover:bg-red-50 dark:hover:bg-red-900/20"
                title="Reject Request"
              >
                <FiX className="w-4 h-4" />
              </button>
            )}
          </ActionButtonGroup>
        );
      }
    });
  }

  // Bulk actions for request management
  const bulkActions = [];
  if (onBulkAction) {
    bulkActions.push(
      {
        label: 'Approve Selected',
        action: 'approve',
        onClick: (selectedRequests) => onBulkAction('approve', selectedRequests)
      },
      {
        label: 'Reject Selected',
        action: 'reject',
        onClick: (selectedRequests) => onBulkAction('reject', selectedRequests),
        variant: 'danger'
      }
    );
  }

  // Custom empty state for join requests
  const emptyState = (
    <div className="text-center py-12">
      <div className="w-16 h-16 mx-auto mb-4 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center">
        <FiUser className="w-8 h-8 text-gray-400" />
      </div>
      <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
        No join requests
      </h3>
      <p className="text-gray-500 dark:text-gray-400">
        No students have requested to join your classrooms yet.
      </p>
    </div>
  );

  return (
    <DataTable
      data={requests}
      columns={columns}
      selectable={selectable}
      bulkActions={bulkActions}
      emptyState={emptyState}
      {...props}
    />
  );
};

/**
 * Compact JoinRequestTable variant for smaller spaces
 */
export const CompactJoinRequestTable = ({
  requests = [],
  onApprove,
  onReject,
  onRowClick,
  ...props
}) => {
  const columns = [
    {
      key: 'student_info',
      label: 'Student',
      sortable: true,
      render: (value, request) => {
        const student = request.student_user || request.student;
        return (
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-blue-100 dark:bg-blue-900/30 rounded-full flex items-center justify-center flex-shrink-0">
              <span className="text-xs font-semibold text-blue-600 dark:text-blue-400">
                {student?.username?.charAt(0).toUpperCase() || 'S'}
              </span>
            </div>
            <div className="min-w-0">
              <div className="font-medium text-gray-900 dark:text-gray-100 truncate">
                {student?.username || 'Unknown Student'}
              </div>
              <div className="text-xs text-gray-500 dark:text-gray-400 truncate">
                {student?.email || 'N/A'}
              </div>
            </div>
          </div>
        );
      }
    },
    {
      key: 'status',
      label: 'Status',
      sortable: true,
      render: (value, request) => {
        const status = request.status || 'pending';
        return (
          <span className="text-sm text-gray-600 dark:text-gray-400 capitalize">
            {status}
          </span>
        );
      }
    },
    {
      key: 'actions',
      label: '',
      sortable: false,
      width: '80px',
      render: (value, request) => {
        const isPending = request.status === 'pending' || !request.status;
        if (!isPending) return null;
        
        return (
          <div className="flex items-center space-x-1">
            {onApprove && (
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  onApprove(request);
                }}
                className="p-1 text-green-600 hover:text-green-700 dark:text-green-400 dark:hover:text-green-300"
                title="Approve"
              >
                <FiCheck className="w-4 h-4" />
              </button>
            )}
            {onReject && (
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  onReject(request);
                }}
                className="p-1 text-red-600 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300"
                title="Reject"
              >
                <FiX className="w-4 h-4" />
              </button>
            )}
          </div>
        );
      }
    }
  ];

  return (
    <DataTable
      data={requests}
      columns={columns}
      onRowClick={onRowClick}
      pagination={false}
      className="compact-table"
      {...props}
    />
  );
};

export default JoinRequestTable;
