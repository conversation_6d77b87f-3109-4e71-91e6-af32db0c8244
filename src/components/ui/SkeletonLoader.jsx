import React from 'react';

// Base skeleton component
const SkeletonBase = ({ className = '', animated = true, ...props }) => (
  <div
    className={`bg-gray-200 dark:bg-gray-700 rounded ${
      animated ? 'animate-pulse' : ''
    } ${className}`}
    {...props}
  />
);

// Text skeleton
export const SkeletonText = ({ 
  lines = 1, 
  className = '', 
  animated = true,
  widths = ['100%'] 
}) => (
  <div className={`space-y-2 ${className}`}>
    {Array.from({ length: lines }).map((_, index) => (
      <SkeletonBase
        key={index}
        className="h-4"
        style={{ width: widths[index % widths.length] }}
        animated={animated}
      />
    ))}
  </div>
);

// Avatar skeleton
export const SkeletonAvatar = ({ 
  size = 'md', 
  className = '', 
  animated = true 
}) => {
  const sizeClasses = {
    sm: 'w-8 h-8',
    md: 'w-12 h-12',
    lg: 'w-16 h-16',
    xl: 'w-20 h-20'
  };

  return (
    <SkeletonBase
      className={`${sizeClasses[size]} rounded-full ${className}`}
      animated={animated}
    />
  );
};

// Button skeleton
export const SkeletonButton = ({ 
  size = 'md', 
  className = '', 
  animated = true 
}) => {
  const sizeClasses = {
    sm: 'h-8 w-20',
    md: 'h-10 w-24',
    lg: 'h-12 w-32'
  };

  return (
    <SkeletonBase
      className={`${sizeClasses[size]} rounded-lg ${className}`}
      animated={animated}
    />
  );
};

// Card skeleton
export const SkeletonCard = ({ 
  className = '', 
  animated = true,
  showHeader = true,
  showFooter = true,
  contentLines = 3
}) => (
  <div className={`bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-6 ${className}`}>
    {showHeader && (
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-3">
          <SkeletonAvatar size="sm" animated={animated} />
          <div className="space-y-2">
            <SkeletonBase className="h-4 w-32" animated={animated} />
            <SkeletonBase className="h-3 w-24" animated={animated} />
          </div>
        </div>
        <SkeletonBase className="h-6 w-16 rounded-full" animated={animated} />
      </div>
    )}
    
    <SkeletonText 
      lines={contentLines} 
      widths={['100%', '85%', '70%']} 
      animated={animated} 
    />
    
    {showFooter && (
      <div className="flex items-center justify-between mt-6">
        <div className="flex space-x-2">
          <SkeletonButton size="sm" animated={animated} />
          <SkeletonButton size="sm" animated={animated} />
        </div>
        <SkeletonBase className="h-8 w-8 rounded" animated={animated} />
      </div>
    )}
  </div>
);

// Table skeleton
export const SkeletonTable = ({ 
  rows = 5, 
  columns = 4, 
  className = '', 
  animated = true 
}) => (
  <div className={`bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 overflow-hidden ${className}`}>
    {/* Table Header */}
    <div className="border-b border-gray-200 dark:border-gray-700 p-4">
      <div className="grid gap-4" style={{ gridTemplateColumns: `repeat(${columns}, 1fr)` }}>
        {Array.from({ length: columns }).map((_, index) => (
          <SkeletonBase key={index} className="h-4 w-20" animated={animated} />
        ))}
      </div>
    </div>
    
    {/* Table Rows */}
    <div className="divide-y divide-gray-200 dark:divide-gray-700">
      {Array.from({ length: rows }).map((_, rowIndex) => (
        <div key={rowIndex} className="p-4">
          <div className="grid gap-4" style={{ gridTemplateColumns: `repeat(${columns}, 1fr)` }}>
            {Array.from({ length: columns }).map((_, colIndex) => (
              <SkeletonBase 
                key={colIndex} 
                className="h-4" 
                style={{ width: `${Math.random() * 40 + 60}%` }}
                animated={animated} 
              />
            ))}
          </div>
        </div>
      ))}
    </div>
  </div>
);

// Stats card skeleton
export const SkeletonStatsCard = ({ 
  className = '', 
  animated = true 
}) => (
  <div className={`bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-6 ${className}`}>
    <div className="flex items-center justify-between">
      <div className="space-y-3">
        <SkeletonBase className="h-4 w-24" animated={animated} />
        <SkeletonBase className="h-8 w-16" animated={animated} />
      </div>
      <SkeletonBase className="w-12 h-12 rounded-xl" animated={animated} />
    </div>
  </div>
);

// Form skeleton
export const SkeletonForm = ({ 
  fields = 4, 
  className = '', 
  animated = true 
}) => (
  <div className={`space-y-6 ${className}`}>
    {Array.from({ length: fields }).map((_, index) => (
      <div key={index} className="space-y-2">
        <SkeletonBase className="h-4 w-20" animated={animated} />
        <SkeletonBase className="h-10 w-full rounded-lg" animated={animated} />
      </div>
    ))}
    <div className="flex space-x-3 pt-4">
      <SkeletonButton size="md" animated={animated} />
      <SkeletonButton size="md" animated={animated} />
    </div>
  </div>
);

// Page skeleton
export const SkeletonPage = ({ 
  className = '', 
  animated = true,
  showStats = true,
  showTable = true,
  showCards = false
}) => (
  <div className={`space-y-6 ${className}`}>
    {/* Header */}
    <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
      <div className="space-y-2">
        <SkeletonBase className="h-8 w-64" animated={animated} />
        <SkeletonBase className="h-4 w-96" animated={animated} />
      </div>
      <div className="flex space-x-3 mt-4 sm:mt-0">
        <SkeletonButton size="md" animated={animated} />
        <SkeletonButton size="md" animated={animated} />
      </div>
    </div>

    {/* Stats Cards */}
    {showStats && (
      <div className="grid grid-cols-2 lg:grid-cols-4 gap-6">
        {Array.from({ length: 4 }).map((_, index) => (
          <SkeletonStatsCard key={index} animated={animated} />
        ))}
      </div>
    )}

    {/* Search and Filters */}
    <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
      <SkeletonBase className="h-10 w-full sm:w-80 rounded-lg" animated={animated} />
      <div className="flex space-x-3">
        <SkeletonBase className="h-10 w-32 rounded-lg" animated={animated} />
        <SkeletonBase className="h-10 w-32 rounded-lg" animated={animated} />
      </div>
    </div>

    {/* Content */}
    {showTable && (
      <SkeletonTable rows={5} columns={5} animated={animated} />
    )}

    {showCards && (
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
        {Array.from({ length: 6 }).map((_, index) => (
          <SkeletonCard key={index} animated={animated} />
        ))}
      </div>
    )}
  </div>
);

// Main SkeletonLoader component
const SkeletonLoader = ({ 
  type = 'text', 
  ...props 
}) => {
  const components = {
    text: SkeletonText,
    avatar: SkeletonAvatar,
    button: SkeletonButton,
    card: SkeletonCard,
    table: SkeletonTable,
    stats: SkeletonStatsCard,
    form: SkeletonForm,
    page: SkeletonPage
  };

  const Component = components[type] || SkeletonText;
  return <Component {...props} />;
};

export default SkeletonLoader;
