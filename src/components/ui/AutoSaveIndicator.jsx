import React, { useState, useEffect } from 'react';
import { FiCheck, FiClock, FiWifi, FiWifiOff, FiAlertCircle } from 'react-icons/fi';

const AutoSaveIndicator = ({ 
  isDirty = false,
  isSaving = false,
  lastSaved = null,
  error = null,
  className = '',
  showTimestamp = true,
  position = 'bottom-right' // 'top-left', 'top-right', 'bottom-left', 'bottom-right', 'inline'
}) => {
  const [isOnline, setIsOnline] = useState(navigator.onLine);

  useEffect(() => {
    const handleOnline = () => setIsOnline(true);
    const handleOffline = () => setIsOnline(false);

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  const formatTimestamp = (timestamp) => {
    if (!timestamp) return '';
    
    const now = new Date();
    const diff = now - timestamp;
    const seconds = Math.floor(diff / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);

    if (seconds < 60) {
      return 'just now';
    } else if (minutes < 60) {
      return `${minutes}m ago`;
    } else if (hours < 24) {
      return `${hours}h ago`;
    } else {
      return timestamp.toLocaleDateString();
    }
  };

  const getStatus = () => {
    if (!isOnline) {
      return {
        icon: FiWifiOff,
        text: 'Offline',
        color: 'text-gray-500',
        bgColor: 'bg-gray-100 dark:bg-gray-800'
      };
    }

    if (error) {
      return {
        icon: FiAlertCircle,
        text: 'Save failed',
        color: 'text-red-600 dark:text-red-400',
        bgColor: 'bg-red-50 dark:bg-red-900/20'
      };
    }

    if (isSaving) {
      return {
        icon: FiClock,
        text: 'Saving...',
        color: 'text-blue-600 dark:text-blue-400',
        bgColor: 'bg-blue-50 dark:bg-blue-900/20',
        animate: true
      };
    }

    if (isDirty) {
      return {
        icon: FiClock,
        text: 'Unsaved changes',
        color: 'text-yellow-600 dark:text-yellow-400',
        bgColor: 'bg-yellow-50 dark:bg-yellow-900/20'
      };
    }

    if (lastSaved) {
      return {
        icon: FiCheck,
        text: 'Saved',
        color: 'text-green-600 dark:text-green-400',
        bgColor: 'bg-green-50 dark:bg-green-900/20'
      };
    }

    return null;
  };

  const status = getStatus();
  if (!status) return null;

  const { icon: IconComponent, text, color, bgColor, animate } = status;

  const positionClasses = {
    'top-left': 'fixed top-4 left-4 z-50',
    'top-right': 'fixed top-4 right-4 z-50',
    'bottom-left': 'fixed bottom-4 left-4 z-50',
    'bottom-right': 'fixed bottom-4 right-4 z-50',
    'inline': 'relative'
  };

  const baseClasses = `
    flex items-center space-x-2 px-3 py-2 rounded-lg border text-sm
    ${bgColor} ${color} border-current/20
    transition-all duration-200 ease-in-out
    ${positionClasses[position]}
    ${className}
  `;

  return (
    <div className={baseClasses}>
      <IconComponent 
        className={`w-4 h-4 flex-shrink-0 ${animate ? 'animate-spin' : ''}`} 
      />
      
      <span className="font-medium whitespace-nowrap">
        {text}
      </span>
      
      {showTimestamp && lastSaved && !isSaving && !error && (
        <span className="text-xs opacity-75 whitespace-nowrap">
          {formatTimestamp(lastSaved)}
        </span>
      )}
    </div>
  );
};

// Hook for auto-save functionality
export const useAutoSave = (
  saveFunction,
  { 
    delay = 2000,
    enabled = true,
    onSaveStart,
    onSaveSuccess,
    onSaveError 
  } = {}
) => {
  const [isSaving, setIsSaving] = useState(false);
  const [lastSaved, setLastSaved] = useState(null);
  const [error, setError] = useState(null);
  const [timeoutId, setTimeoutId] = useState(null);

  const triggerSave = async (data) => {
    if (!enabled || isSaving) return;

    // Clear existing timeout
    if (timeoutId) {
      clearTimeout(timeoutId);
      setTimeoutId(null);
    }

    // Set new timeout
    const newTimeoutId = setTimeout(async () => {
      setIsSaving(true);
      setError(null);
      
      if (onSaveStart) onSaveStart();

      try {
        await saveFunction(data);
        setLastSaved(new Date());
        setError(null);
        
        if (onSaveSuccess) onSaveSuccess();
      } catch (err) {
        setError(err);
        
        if (onSaveError) onSaveError(err);
      } finally {
        setIsSaving(false);
      }
    }, delay);

    setTimeoutId(newTimeoutId);
  };

  const cancelSave = () => {
    if (timeoutId) {
      clearTimeout(timeoutId);
      setTimeoutId(null);
    }
  };

  const forceSave = async (data) => {
    cancelSave();
    
    setIsSaving(true);
    setError(null);
    
    if (onSaveStart) onSaveStart();

    try {
      await saveFunction(data);
      setLastSaved(new Date());
      setError(null);
      
      if (onSaveSuccess) onSaveSuccess();
    } catch (err) {
      setError(err);
      
      if (onSaveError) onSaveError(err);
    } finally {
      setIsSaving(false);
    }
  };

  // Cleanup on unmount
  React.useEffect(() => {
    return () => {
      if (timeoutId) {
        clearTimeout(timeoutId);
      }
    };
  }, [timeoutId]);

  return {
    isSaving,
    lastSaved,
    error,
    triggerSave,
    cancelSave,
    forceSave
  };
};

export default AutoSaveIndicator;
