import React from 'react';

const EmptyState = ({ 
  icon: Icon,
  title,
  description,
  action,
  actionText,
  onAction,
  currentTheme = 'light'
}) => {
  // Theme classes
  const bgSecondary = currentTheme === "dark" ? "bg-gray-800" : "bg-white";
  const textPrimary = currentTheme === "dark" ? "text-gray-100" : "text-gray-900";
  const textSecondary = currentTheme === "dark" ? "text-gray-400" : "text-gray-600";
  const borderColor = currentTheme === "dark" ? "border-gray-700" : "border-gray-200";

  return (
    <div className={`${bgSecondary} rounded-xl p-12 border ${borderColor} text-center`}>
      {Icon && (
        <div className="flex justify-center mb-4">
          <Icon className={`w-16 h-16 ${textSecondary}`} />
        </div>
      )}
      
      <h3 className={`text-lg font-medium ${textPrimary} mb-2`}>
        {title}
      </h3>
      
      {description && (
        <p className={`${textSecondary} mb-6 max-w-md mx-auto`}>
          {description}
        </p>
      )}
      
      {action && onAction && (
        <button
          onClick={onAction}
          className="inline-flex items-center px-4 py-2 bg-violet-600 text-white rounded-lg hover:bg-violet-700 transition-colors"
        >
          {actionText || 'Get Started'}
        </button>
      )}
      
      {action && !onAction && action}
    </div>
  );
};

export default EmptyState;
