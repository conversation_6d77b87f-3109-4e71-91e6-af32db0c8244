import React from 'react';
import { 
  FiUser, 
  FiMail, 
  FiPhone, 
  FiCalendar, 
  FiCheck, 
  FiX,
  FiClock,
  FiBookOpen
} from 'react-icons/fi';

/**
 * JoinRequestCard component for displaying student classroom join requests
 * Handles the specific API response structure for join requests
 */
const JoinRequestCard = ({
  request,
  onApprove,
  onReject,
  onView,
  showActions = true,
  showStudentDetails = true,
  showClassroomInfo = true,
  className = '',
  size = 'default' // 'compact', 'default', 'large'
}) => {
  if (!request) return null;

  const sizeClasses = {
    compact: 'p-4',
    default: 'p-6',
    large: 'p-8'
  };

  const student = request.student_user || request.student;
  const classroomId = request.classroom_id || request.classroom;
  const classroomName = typeof request.classroom === 'object' 
    ? request.classroom.name 
    : `Classroom ${classroomId?.slice(-8)}`;

  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString();
  };

  const getStatusColor = (status) => {
    const colors = {
      pending: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200',
      approved: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200',
      rejected: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
    };
    return colors[status] || colors.pending;
  };

  return (
    <div className={`
      bg-white dark:bg-gray-800 rounded-lg shadow hover:shadow-md 
      transition-all duration-200 border border-transparent 
      hover:border-blue-200 dark:hover:border-blue-700
      ${sizeClasses[size]} ${className}
    `}>
      {/* Header Section */}
      <div className="flex items-start justify-between">
        <div className="flex items-center space-x-3 flex-1 min-w-0">
          {/* Student Avatar */}
          <div className="w-12 h-12 bg-blue-100 dark:bg-blue-900/30 rounded-full flex items-center justify-center flex-shrink-0">
            {student?.profile_picture ? (
              <img 
                src={student.profile_picture} 
                alt={student.username} 
                className="w-full h-full rounded-full object-cover"
              />
            ) : (
              <span className="text-lg font-semibold text-blue-600 dark:text-blue-400">
                {student?.username?.charAt(0).toUpperCase() || 'S'}
              </span>
            )}
          </div>

          {/* Request Info */}
          <div className="flex-1 min-w-0">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 truncate">
              Join Request
            </h3>
            
            {showStudentDetails && student && (
              <div className="mt-1 space-y-1">
                <div className="flex items-center text-sm text-gray-600 dark:text-gray-400">
                  <FiUser className="w-4 h-4 mr-2 flex-shrink-0" />
                  <span className="truncate">{student.username || 'Unknown Student'}</span>
                </div>
                
                <div className="flex items-center text-sm text-gray-500 dark:text-gray-400">
                  <FiMail className="w-4 h-4 mr-2 flex-shrink-0" />
                  <span className="truncate">{student.email || 'N/A'}</span>
                </div>

                {student.mobile && (
                  <div className="flex items-center text-sm text-gray-500 dark:text-gray-400">
                    <FiPhone className="w-4 h-4 mr-2 flex-shrink-0" />
                    <span className="truncate">{student.mobile}</span>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>

        {/* Status Badge */}
        <div className="flex flex-col items-end space-y-2 flex-shrink-0">
          <span className={`
            inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
            ${getStatusColor(request.status)}
          `}>
            <FiClock className="w-3 h-3 mr-1" />
            {request.status?.charAt(0).toUpperCase() + request.status?.slice(1) || 'Pending'}
          </span>
        </div>
      </div>

      {/* Classroom Info Section */}
      {showClassroomInfo && (
        <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
          <div className="flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-400">
            <FiBookOpen className="w-4 h-4 flex-shrink-0" />
            <span>Requesting to join:</span>
            <span className="font-medium text-gray-900 dark:text-gray-100">
              {classroomName}
            </span>
          </div>
          
          {request.created_at && (
            <div className="flex items-center space-x-2 text-xs text-gray-500 dark:text-gray-400 mt-1">
              <FiCalendar className="w-3 h-3 flex-shrink-0" />
              <span>Requested on {formatDate(request.created_at)}</span>
            </div>
          )}
        </div>
      )}

      {/* Actions Section */}
      {showActions && (request.status === 'pending' || !request.status) && (
        <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-end space-x-3">
            {onView && (
              <button
                onClick={() => onView(request)}
                className="px-3 py-1.5 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors"
              >
                View Details
              </button>
            )}
            
            {onReject && (
              <button
                onClick={() => onReject(request)}
                className="px-3 py-1.5 text-sm font-medium text-red-700 dark:text-red-300 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg hover:bg-red-100 dark:hover:bg-red-900/30 focus:outline-none focus:ring-2 focus:ring-red-500 transition-colors flex items-center space-x-1"
              >
                <FiX className="w-4 h-4" />
                <span>Reject</span>
              </button>
            )}
            
            {onApprove && (
              <button
                onClick={() => onApprove(request)}
                className="px-3 py-1.5 text-sm font-medium text-white bg-green-600 border border-transparent rounded-lg hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 transition-colors flex items-center space-x-1"
              >
                <FiCheck className="w-4 h-4" />
                <span>Approve</span>
              </button>
            )}
          </div>
        </div>
      )}

      {/* Approved/Rejected State */}
      {(request.status === 'approved' || request.status === 'rejected') && (
        <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
          <div className={`
            text-sm font-medium flex items-center space-x-2
            ${request.status === 'approved' 
              ? 'text-green-600 dark:text-green-400' 
              : 'text-red-600 dark:text-red-400'
            }
          `}>
            {request.status === 'approved' ? (
              <>
                <FiCheck className="w-4 h-4" />
                <span>Request approved</span>
              </>
            ) : (
              <>
                <FiX className="w-4 h-4" />
                <span>Request rejected</span>
              </>
            )}
          </div>
          
          {request.processed_at && (
            <div className="text-xs text-gray-500 dark:text-gray-400 mt-1">
              on {formatDate(request.processed_at)}
            </div>
          )}
        </div>
      )}
    </div>
  );
};

/**
 * Compact JoinRequestCard variant for lists
 */
export const CompactJoinRequestCard = ({
  request,
  onApprove,
  onReject,
  ...props
}) => {
  const student = request.student_user || request.student;
  
  return (
    <JoinRequestCard
      request={request}
      onApprove={onApprove}
      onReject={onReject}
      size="compact"
      showClassroomInfo={false}
      {...props}
    >
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <div className="w-8 h-8 bg-blue-100 dark:bg-blue-900/30 rounded-full flex items-center justify-center">
            <span className="text-sm font-semibold text-blue-600 dark:text-blue-400">
              {student?.username?.charAt(0).toUpperCase() || 'S'}
            </span>
          </div>
          <div>
            <div className="font-medium text-gray-900 dark:text-gray-100">
              {student?.username || 'Unknown Student'}
            </div>
            <div className="text-sm text-gray-500 dark:text-gray-400">
              {student?.email || 'N/A'}
            </div>
          </div>
        </div>
        
        {(request.status === 'pending' || !request.status) && (
          <div className="flex items-center space-x-2">
            {onReject && (
              <button
                onClick={() => onReject(request)}
                className="p-1 text-red-600 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300"
              >
                <FiX className="w-4 h-4" />
              </button>
            )}
            {onApprove && (
              <button
                onClick={() => onApprove(request)}
                className="p-1 text-green-600 hover:text-green-700 dark:text-green-400 dark:hover:text-green-300"
              >
                <FiCheck className="w-4 h-4" />
              </button>
            )}
          </div>
        )}
      </div>
    </JoinRequestCard>
  );
};

export default JoinRequestCard;
