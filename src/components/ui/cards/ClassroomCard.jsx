import React from 'react';
import { 
  FiUsers, 
  FiBookOpen, 
  FiCalendar, 
  FiEye, 
  FiEdit, 
  FiMoreVertical,
  FiCheckCircle,
  FiClock,
  FiUser
} from 'react-icons/fi';

/**
 * Reusable ClassroomCard component for displaying classroom information
 * Used in Teacher and Student classroom listings
 */
const ClassroomCard = ({
  classroom,
  onView,
  onEdit,
  onMoreActions,
  showActions = true,
  showStats = true,
  showTeacher = false,
  userRole = 'teacher', // 'teacher', 'student', 'admin'
  className = '',
  size = 'default' // 'compact', 'default', 'large'
}) => {
  if (!classroom) return null;

  const sizeClasses = {
    compact: 'p-4',
    default: 'p-6',
    large: 'p-8'
  };

  const getStatusColor = (status) => {
    const colors = {
      active: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200',
      inactive: 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200',
      archived: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200',
      draft: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
    };
    return colors[status] || colors.active;
  };

  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString();
  };

  const getClassroomIcon = () => {
    return classroom.subject_icon || FiBookOpen;
  };

  return (
    <div className={`
      bg-white dark:bg-gray-800 rounded-lg shadow hover:shadow-md 
      transition-all duration-200 border border-transparent 
      hover:border-blue-200 dark:hover:border-blue-700 cursor-pointer
      ${sizeClasses[size]} ${className}
    `}>
      {/* Header Section */}
      <div className="flex items-start justify-between">
        <div className="flex items-center space-x-3 flex-1 min-w-0">
          {/* Classroom Icon */}
          <div className="p-2 bg-blue-100 dark:bg-blue-900 rounded-lg flex-shrink-0">
            <FiBookOpen className="text-blue-600 dark:text-blue-400" size={20} />
          </div>

          {/* Classroom Info */}
          <div className="flex-1 min-w-0">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 truncate">
              {classroom.name || 'Untitled Classroom'}
            </h3>
            
            {classroom.description && (
              <p className="text-sm text-gray-500 dark:text-gray-400 mt-1 line-clamp-2">
                {classroom.description}
              </p>
            )}

            {/* Teacher Info (for student view) */}
            {showTeacher && classroom.teacher && (
              <p className="text-sm text-gray-500 dark:text-gray-400 flex items-center mt-2">
                <FiUser className="w-4 h-4 mr-1" />
                {classroom.teacher.username || classroom.teacher.name || 'Unknown Teacher'}
              </p>
            )}
          </div>
        </div>

        {/* Status Badge and Actions */}
        <div className="flex flex-col items-end space-y-2 flex-shrink-0">
          {/* Status Badge */}
          <span className={`
            inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
            ${getStatusColor(classroom.status)}
          `}>
            {classroom.status === 'active' && <FiCheckCircle className="w-3 h-3 mr-1" />}
            {classroom.status === 'draft' && <FiClock className="w-3 h-3 mr-1" />}
            {classroom.status?.charAt(0).toUpperCase() + classroom.status?.slice(1) || 'Active'}
          </span>

          {/* Action Buttons */}
          {showActions && (
            <div className="flex items-center space-x-1">
              {onView && (
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    onView(classroom);
                  }}
                  className="p-2 text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors"
                  title="View Classroom"
                >
                  <FiEye className="w-4 h-4" />
                </button>
              )}
              {onEdit && userRole === 'teacher' && (
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    onEdit(classroom);
                  }}
                  className="p-2 text-gray-400 hover:text-green-600 dark:hover:text-green-400 transition-colors"
                  title="Edit Classroom"
                >
                  <FiEdit className="w-4 h-4" />
                </button>
              )}
              {onMoreActions && (
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    onMoreActions(classroom);
                  }}
                  className="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
                  title="More Actions"
                >
                  <FiMoreVertical className="w-4 h-4" />
                </button>
              )}
            </div>
          )}
        </div>
      </div>

      {/* Stats Section */}
      {showStats && (
        <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
          <div className="grid grid-cols-2 gap-4 text-sm">
            {/* Student Count */}
            <div className="flex items-center text-gray-500 dark:text-gray-400">
              <FiUsers className="w-4 h-4 mr-2" />
              <span>
                {classroom.student_count || classroom.students?.length || 0} Students
              </span>
            </div>

            {/* Created Date */}
            <div className="flex items-center text-gray-500 dark:text-gray-400">
              <FiCalendar className="w-4 h-4 mr-2" />
              <span>
                {formatDate(classroom.created_at)}
              </span>
            </div>
          </div>

          {/* Additional Stats (if available) */}
          {(classroom.exam_count || classroom.assignment_count) && (
            <div className="grid grid-cols-2 gap-4 text-sm mt-2">
              {classroom.exam_count !== undefined && (
                <div className="text-gray-500 dark:text-gray-400">
                  <span className="font-medium">{classroom.exam_count}</span> Exams
                </div>
              )}
              {classroom.assignment_count !== undefined && (
                <div className="text-gray-500 dark:text-gray-400">
                  <span className="font-medium">{classroom.assignment_count}</span> Assignments
                </div>
              )}
            </div>
          )}
        </div>
      )}

      {/* Subject/Category Tag (if available) */}
      {classroom.subject && (
        <div className="mt-3">
          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-violet-100 text-violet-800 dark:bg-violet-900 dark:text-violet-200">
            {classroom.subject.name || classroom.subject}
          </span>
        </div>
      )}
    </div>
  );
};

export default ClassroomCard;
