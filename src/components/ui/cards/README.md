# Card Components

This directory contains reusable card components for the EduFair application. These components provide consistent styling and behavior across different parts of the application.

## Components

### UserCard
Displays user information with avatar, contact details, role, and verification status.

**Props:**
- `user` (object): User data object
- `onView` (function): Callback for view action
- `onEdit` (function): Callback for edit action
- `onMoreActions` (function): Callback for more actions menu
- `showActions` (boolean): Whether to show action buttons (default: true)
- `showVerificationStatus` (boolean): Whether to show verification badges (default: true)
- `showJoinDate` (boolean): Whether to show join date (default: true)
- `size` (string): Card size - 'compact', 'default', 'large' (default: 'default')

**Usage:**
```jsx
import { UserCard } from '../components/ui/cards';

<UserCard
  user={userData}
  onView={handleViewUser}
  onEdit={handleEditUser}
  size="default"
/>
```

### ClassroomCard
Displays classroom information with stats, teacher info, and actions.

**Props:**
- `classroom` (object): Classroom data object
- `onView` (function): Callback for view action
- `onEdit` (function): Callback for edit action
- `onMoreActions` (function): Callback for more actions menu
- `showActions` (boolean): Whether to show action buttons (default: true)
- `showStats` (boolean): Whether to show classroom stats (default: true)
- `showTeacher` (boolean): Whether to show teacher info (default: false)
- `userRole` (string): Current user role - 'teacher', 'student', 'admin' (default: 'teacher')
- `size` (string): Card size - 'compact', 'default', 'large' (default: 'default')

**Usage:**
```jsx
import { ClassroomCard } from '../components/ui/cards';

<ClassroomCard
  classroom={classroomData}
  onView={handleViewClassroom}
  userRole="student"
  showTeacher={true}
/>
```

### DashboardCard
Flexible dashboard widget container with support for charts, stats, and custom content.

**Props:**
- `title` (string): Card title
- `subtitle` (string): Card subtitle
- `value` (string|number): Main value to display
- `change` (string|number): Change indicator
- `changeType` (string): Change type - 'positive', 'negative', 'neutral' (default: 'positive')
- `icon` (component): Icon component to display
- `chart` (component): Chart component to render
- `children` (node): Custom content
- `menuItems` (array): Menu items for dropdown
- `onMenuAction` (function): Menu action callback
- `size` (string): Card size - 'compact', 'default', 'large' (default: 'default')
- `variant` (string): Card variant - 'default', 'gradient', 'minimal' (default: 'default')

**Usage:**
```jsx
import { DashboardCard, StatsCard } from '../components/ui/cards';

<DashboardCard
  title="Revenue"
  value="$24,780"
  change={49}
  changeType="positive"
  icon={FiDollarSign}
  chart={LineChart}
/>

<StatsCard
  title="Total Users"
  value={1234}
  change={12}
  icon={FiUsers}
/>
```

### InfoCard
Displays information, alerts, and notifications with different types and variants.

**Props:**
- `title` (string): Card title
- `message` (string): Card message
- `type` (string): Card type - 'info', 'success', 'warning', 'error' (default: 'info')
- `icon` (component): Custom icon component
- `onClose` (function): Close button callback
- `actions` (array): Action buttons array
- `children` (node): Custom content
- `size` (string): Card size - 'compact', 'default', 'large' (default: 'default')
- `variant` (string): Card variant - 'default', 'filled', 'outlined' (default: 'default')

**Usage:**
```jsx
import { InfoCard, AnnouncementCard, NotificationCard } from '../components/ui/cards';

<InfoCard
  title="Welcome!"
  message="Your account has been created successfully."
  type="success"
  onClose={handleClose}
  actions={[
    { label: 'Get Started', onClick: handleGetStarted }
  ]}
/>

<AnnouncementCard
  announcement={announcementData}
  onEdit={handleEdit}
  showActions={true}
/>

<NotificationCard
  notification={notificationData}
  onMarkAsRead={handleMarkAsRead}
  onDismiss={handleDismiss}
/>
```

## Styling

All card components use Tailwind CSS classes and support dark mode automatically. They follow the application's design system with:

- Consistent border radius (rounded-lg)
- Hover effects (shadow transitions)
- Dark mode support
- Responsive design
- Consistent spacing and typography

## Accessibility

- Proper semantic HTML structure
- Keyboard navigation support
- Screen reader friendly
- Focus indicators
- ARIA labels where appropriate

## Migration Guide

To migrate existing card implementations:

1. Import the appropriate card component
2. Replace custom card JSX with the component
3. Pass data through props instead of inline JSX
4. Update event handlers to use component callbacks
5. Remove custom styling in favor of component props

**Before:**
```jsx
<div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
  <div className="flex items-center space-x-3">
    <div className="w-12 h-12 bg-violet-100 rounded-full">
      {/* avatar content */}
    </div>
    {/* user info */}
  </div>
</div>
```

**After:**
```jsx
<UserCard
  user={userData}
  onView={handleView}
  onEdit={handleEdit}
/>
```
