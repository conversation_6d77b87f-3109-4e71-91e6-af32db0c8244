# JoinRequestCard Component

A specialized card component for displaying student classroom join requests with the specific API response structure from EduFair.

## API Response Structure

The component is designed to handle the following API response structure:

```json
[
  {
    "id": "023cc3fe-88db-4349-9584-d140176256c1",
    "student_id": "36c61d4c-a70c-4ef3-8840-6b7434209b91",
    "classroom_id": "78253a2e-20c8-4573-9950-ed14eae0b0b3",
    "student_user": {
      "username": "Student",
      "email": "<EMAIL>",
      "mobile": "923351456504",
      "user_type": "student",
      "country": "PK",
      "profile_picture": null,
      "id": "36c61d4c-a70c-4ef3-8840-6b7434209b91",
      "is_email_verified": false,
      "is_mobile_verified": false
    },
    "classroom": "78253a2e-20c8-4573-9950-ed14eae0b0b3"
  }
]
```

## Props

- `request` (object): Join request data object
- `onApprove` (function): Callback for approving the request
- `onReject` (function): Callback for rejecting the request
- `onView` (function): Callback for viewing request details
- `showActions` (boolean): Whether to show action buttons (default: true)
- `showStudentDetails` (boolean): Whether to show student details (default: true)
- `showClassroomInfo` (boolean): Whether to show classroom info (default: true)
- `size` (string): Card size - 'compact', 'default', 'large'

## Usage

### Basic Usage
```jsx
import { JoinRequestCard } from '../components/ui/cards';

<JoinRequestCard
  request={joinRequest}
  onApprove={handleApprove}
  onReject={handleReject}
  onView={handleView}
/>
```

### With Custom Handlers
```jsx
const handleApprove = async (request) => {
  try {
    await approveJoinRequest(request.id);
    // Handle success
  } catch (error) {
    // Handle error
  }
};

const handleReject = async (request) => {
  try {
    await rejectJoinRequest(request.id);
    // Handle success
  } catch (error) {
    // Handle error
  }
};

<JoinRequestCard
  request={request}
  onApprove={handleApprove}
  onReject={handleReject}
  showClassroomInfo={true}
/>
```

### Compact Variant
```jsx
import { CompactJoinRequestCard } from '../components/ui/cards';

<CompactJoinRequestCard
  request={request}
  onApprove={handleApprove}
  onReject={handleReject}
/>
```

## Features

### Student Information Display
- Student avatar (profile picture or initials)
- Username and email
- Mobile number (if available)
- Verification status indicators

### Classroom Information
- Classroom name (or ID if name not available)
- Request date
- Status indicators

### Action Buttons
- Approve button (green)
- Reject button (red)
- View details button
- Conditional display based on request status

### Status Handling
- Pending requests: Show action buttons
- Approved requests: Show approval confirmation
- Rejected requests: Show rejection confirmation

## API Integration Notes

### Classroom Data Issue
The current API returns `classroom` as a string ID instead of an object:
```json
"classroom": "78253a2e-20c8-4573-9950-ed14eae0b0b3"
```

**Recommended API Enhancement:**
```json
"classroom": {
  "id": "78253a2e-20c8-4573-9950-ed14eae0b0b3",
  "name": "Mathematics 101",
  "teacher": {
    "username": "teacher_name",
    "email": "<EMAIL>"
  }
}
```

### Workaround
The component handles this by:
1. Checking if `classroom` is a string or object
2. Displaying a fallback name using the last 8 characters of the ID
3. Supporting enhanced data through the `useJoinRequests` hook

## Integration with useJoinRequests Hook

```jsx
import { useJoinRequests } from '../hooks/useJoinRequests';
import { JoinRequestCard } from '../components/ui/cards';

function JoinRequestsPage() {
  const {
    requests,
    approveRequest,
    rejectRequest,
    loading,
    error
  } = useJoinRequests();

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {requests.map(request => (
        <JoinRequestCard
          key={request.id}
          request={request}
          onApprove={() => approveRequest(request.id)}
          onReject={() => rejectRequest(request.id)}
        />
      ))}
    </div>
  );
}
```

## Styling

The component uses Tailwind CSS classes and supports:
- Dark mode automatically
- Hover effects
- Responsive design
- Status-based color coding
- Loading states (through parent component)

## Accessibility

- Proper button labels and ARIA attributes
- Keyboard navigation support
- Screen reader friendly structure
- Color contrast compliance
