import React from 'react';
import { FiMoreVertical } from 'react-icons/fi';

/**
 * Reusable DashboardCard component for dashboard widgets
 * Standardizes the layout and styling of dashboard cards
 */
const DashboardCard = ({
  title,
  subtitle,
  value,
  change,
  changeType = 'positive', // 'positive', 'negative', 'neutral'
  icon: IconComponent,
  chart: ChartComponent,
  children,
  menuItems = [],
  onMenuAction,
  className = '',
  size = 'default', // 'compact', 'default', 'large'
  variant = 'default' // 'default', 'gradient', 'minimal'
}) => {
  const sizeClasses = {
    compact: 'p-4',
    default: 'px-5 pt-5',
    large: 'p-8'
  };

  const variantClasses = {
    default: 'bg-white dark:bg-gray-800 shadow-xs',
    gradient: 'bg-gradient-to-br from-violet-500 to-blue-600 text-white shadow-lg',
    minimal: 'bg-gray-50 dark:bg-gray-900 border border-gray-200 dark:border-gray-700'
  };

  const getChangeColor = (type) => {
    const colors = {
      positive: 'text-green-700 bg-green-500/20',
      negative: 'text-red-700 bg-red-500/20',
      neutral: 'text-gray-700 bg-gray-500/20 dark:text-gray-300'
    };
    return colors[type] || colors.neutral;
  };

  const formatValue = (val) => {
    if (typeof val === 'number') {
      if (val >= 1000000) {
        return `${(val / 1000000).toFixed(1)}M`;
      } else if (val >= 1000) {
        return `${(val / 1000).toFixed(1)}K`;
      }
      return val.toLocaleString();
    }
    return val;
  };

  return (
    <div className={`
      flex flex-col rounded-xl transition-all duration-200 hover:shadow-md
      ${variantClasses[variant]} ${sizeClasses[size]} ${className}
    `}>
      {/* Header Section */}
      <div className="flex justify-between items-start mb-2">
        <div className="flex items-center space-x-3">
          {/* Icon */}
          {IconComponent && (
            <div className={`
              p-2 rounded-lg flex-shrink-0
              ${variant === 'gradient' 
                ? 'bg-white/20' 
                : 'bg-violet-100 dark:bg-violet-900/30'
              }
            `}>
              <IconComponent 
                className={`
                  w-5 h-5
                  ${variant === 'gradient' 
                    ? 'text-white' 
                    : 'text-violet-600 dark:text-violet-400'
                  }
                `} 
              />
            </div>
          )}

          {/* Title and Subtitle */}
          <div>
            <h2 className={`
              text-lg font-semibold mb-1
              ${variant === 'gradient' 
                ? 'text-white' 
                : 'text-gray-800 dark:text-gray-100'
              }
            `}>
              {title}
            </h2>
            {subtitle && (
              <div className={`
                text-xs font-semibold uppercase tracking-wide
                ${variant === 'gradient' 
                  ? 'text-white/80' 
                  : 'text-gray-400 dark:text-gray-500'
                }
              `}>
                {subtitle}
              </div>
            )}
          </div>
        </div>

        {/* Menu Button */}
        {menuItems.length > 0 && (
          <div className="relative">
            <button
              className={`
                p-1 rounded-md transition-colors
                ${variant === 'gradient' 
                  ? 'text-white/80 hover:text-white hover:bg-white/10' 
                  : 'text-gray-400 hover:text-gray-600 dark:hover:text-gray-300'
                }
              `}
              onClick={() => onMenuAction?.('toggle')}
            >
              <FiMoreVertical className="w-4 h-4" />
            </button>
            {/* Menu dropdown would be implemented here */}
          </div>
        )}
      </div>

      {/* Value and Change Section */}
      {(value !== undefined || change !== undefined) && (
        <div className="flex items-end space-x-2 mb-4">
          {value !== undefined && (
            <div className={`
              text-3xl font-bold
              ${variant === 'gradient' 
                ? 'text-white' 
                : 'text-gray-800 dark:text-gray-100'
              }
            `}>
              {formatValue(value)}
            </div>
          )}
          
          {change !== undefined && (
            <div className={`
              text-sm font-medium px-1.5 py-0.5 rounded-full
              ${variant === 'gradient' 
                ? 'bg-white/20 text-white' 
                : getChangeColor(changeType)
              }
            `}>
              {typeof change === 'number' ? 
                `${change > 0 ? '+' : ''}${change}%` : 
                change
              }
            </div>
          )}
        </div>
      )}

      {/* Chart Section */}
      {ChartComponent && (
        <div className="grow max-sm:max-h-[128px] xl:max-h-[128px] mb-4">
          <ChartComponent />
        </div>
      )}

      {/* Custom Content */}
      {children && (
        <div className="flex-1">
          {children}
        </div>
      )}
    </div>
  );
};

/**
 * Specialized StatsCard for simple metric display
 */
export const StatsCard = ({
  title,
  value,
  change,
  changeType = 'positive',
  icon: IconComponent,
  className = '',
  onClick
}) => {
  return (
    <DashboardCard
      title={title}
      value={value}
      change={change}
      changeType={changeType}
      icon={IconComponent}
      size="compact"
      className={`${onClick ? 'cursor-pointer hover:scale-105' : ''} ${className}`}
      onClick={onClick}
    />
  );
};

/**
 * Specialized ChartCard for charts with minimal header
 */
export const ChartCard = ({
  title,
  chart: ChartComponent,
  menuItems = [],
  onMenuAction,
  className = ''
}) => {
  return (
    <DashboardCard
      title={title}
      chart={ChartComponent}
      menuItems={menuItems}
      onMenuAction={onMenuAction}
      className={className}
    />
  );
};

export default DashboardCard;
