import React from 'react';
import ReactDOM from 'react-dom/client';
import { <PERSON><PERSON><PERSON><PERSON>outer as Router } from 'react-router-dom';
import { Provider } from 'react-redux';
import store from './app/Store.js';
import ThemeProvider from './utils/ThemeContext';
import App from './App';

ReactDOM.createRoot(document.getElementById('root')).render(
  <React.StrictMode>
    <Provider store={store}>
      <Router>
        <ThemeProvider>
          <App />
        </ThemeProvider>
      </Router>
    </Provider>
  </React.StrictMode>
);
