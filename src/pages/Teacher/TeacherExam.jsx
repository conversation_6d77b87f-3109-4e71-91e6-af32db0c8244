import React, { useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useThemeProvider } from "../../utils/ThemeContext";

import { fetchAllOwnClasses } from "../../features/classroom/ClassroomSlice";
import { fetchSubjects } from "../../features/subjects/SubjectSlice";
import { fetchChaptersBySubject } from "../../features/chapters/ChapterSlice";
import { fetchTopicsByChapter } from "../../features/topics/TopicSlice";
import { fetchSubtopicsByTopic } from "../../features/subtopics/SubtopicSlice";
import { aiGenerateQuestions } from "../../features/question/QuestionSlice";
import { createExamWithAssignment } from "../../features/exams/ExamSlice";
import {
  FiCalendar,
  FiClock,
  FiFileText,
  FiPlus,
  FiTrash2,
  FiEdit3,
  <PERSON>Check,
  FiX,
  FiBookOpen,
  FiTarget,
  FiZap,
  FiSave,
  FiArrowRight,
  FiArrowLeft
} from "react-icons/fi";

const initialExamState = {
  title: "",
  description: "",
  total_marks: 0,
  total_duration: 0,
  start_time: "",
  question_ids: [],
};

const initialQuestionState = {
  text: "",
  answer: "",
  Type: "MCQS",
  Level: "EASY",
  imageUrl: "",
  marks: 1,
  options: [
    { option_text: "", is_correct: false },
    { option_text: "", is_correct: false },
    { option_text: "", is_correct: false },
    { option_text: "", is_correct: false },
  ],
};

const initialDescriptiveState = {
  text: "",
  answer: "",
  Type: "DESCRIPTIVE",
  Level: "EASY",
  imageUrl: "",
  marks: 1,
  options: [],
};

export default function TeacherExam() {
  const dispatch = useDispatch();
  const { currentTheme } = useThemeProvider();
  const themeBg = currentTheme === "dark" ? "bg-gray-900" : "bg-white";
  const themeText = currentTheme === "dark" ? "text-gray-100" : "text-gray-900";
  const themeInput = currentTheme === "dark" ? "bg-gray-800 text-gray-100 border-gray-700" : "bg-gray-50 text-gray-900 border-gray-300";
  const themeLabel = currentTheme === "dark" ? "text-gray-300" : "text-gray-700";

  // Redux state for classrooms, subjects, chapters
  const { classrooms, loading: classroomsLoading } = useSelector((state) => state.classroom);
  const { subjects, loading: subjectsLoading } = useSelector((state) => state.subjects);
  const { chaptersBySubject = [], loading: chaptersLoading } = useSelector((state) => state.chapters);
  const { topicsByChapter = [], loading: topicsLoading } = useSelector((state) => state.topics);
  const { subtopicsByTopic = [], loading: subtopicsLoading } = useSelector((state) => state.subtopics);

  // Step state - Step 1: Exam Details, Step 2: Add Questions
  const [step, setStep] = useState(1);
  const [questionType, setQuestionType] = useState("MCQS");
  const [exam, setExam] = useState(initialExamState);
  const [questions, setQuestions] = useState([]); // created question objects
  const [questionForm, setQuestionForm] = useState(initialQuestionState);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState("");
  const [success, setSuccess] = useState("");
  const [progress, setProgress] = useState(0);
  const [classId, setClassId] = useState("");
  const [subjectId, setSubjectId] = useState("");
  const [chapterId, setChapterId] = useState("");
  const [topicId, setTopicId] = useState("");
  const [subtopicId, setSubtopicId] = useState("");
  const [descType, setDescType] = useState("SHORT");
  const [examDetailsValid, setExamDetailsValid] = useState(false);

  // Redux loading state
  const { loading: examLoading } = useSelector((state) => state.exams);
  const { loading: questionLoading } = useSelector((state) => state.questions);

  // Fetch classrooms on mount
  React.useEffect(() => {
    dispatch(fetchAllOwnClasses());
  }, [dispatch]);

  // Set default classroom when loaded
  React.useEffect(() => {
    if (classrooms && classrooms.length > 0 && !classId) {
      setClassId(classrooms[0].id);
    }
  }, [classrooms, classId]);

  // Fetch subjects when classroom changes
  React.useEffect(() => {
    if (classId) {
      dispatch(fetchSubjects()); // If you need to filter by classroom, pass { classroomId: classId }
    }
  }, [dispatch, classId]);

  // Set default subject when loaded
  React.useEffect(() => {
    if (subjects && subjects.length > 0 && !subjectId) {
      setSubjectId(subjects[0].id);
    }
  }, [subjects, subjectId]);

  // Fetch chapters when subject changes
  React.useEffect(() => {
    if (subjectId) {
      dispatch(fetchChaptersBySubject({ subjectId }));
    }
  }, [dispatch, subjectId]);

  // Set default chapter when loaded
  React.useEffect(() => {
    if (chaptersBySubject && chaptersBySubject.length > 0 && !chapterId) {
      setChapterId(chaptersBySubject[0].id);
    }
  }, [chaptersBySubject, chapterId]);

  // Fetch topics when chapter changes
  React.useEffect(() => {
    if (chapterId) {
      dispatch(fetchTopicsByChapter({ chapterId }));
      setTopicId("");
      setSubtopicId("");
    }
  }, [dispatch, chapterId]);

  // Set default topic when loaded
  React.useEffect(() => {
    if (topicsByChapter && topicsByChapter.length > 0 && !topicId) {
      setTopicId(topicsByChapter[0].id);
    }
  }, [topicsByChapter, topicId]);

  // Fetch subtopics when topic changes
  React.useEffect(() => {
    if (topicId) {
      dispatch(fetchSubtopicsByTopic({ topicId }));
      setSubtopicId("");
    }
  }, [dispatch, topicId]);

  // Set default subtopic when loaded
  React.useEffect(() => {
    if (subtopicsByTopic && subtopicsByTopic.length > 0 && !subtopicId) {
      setSubtopicId(subtopicsByTopic[0].id);
    }
  }, [subtopicsByTopic, subtopicId]);

  // Step progress - Step 1: Exam Details (50%), Step 2: Add Questions (100%)
  React.useEffect(() => {
    setProgress(step === 1 ? 50 : 100);
  }, [step]);

  // Validate exam details
  React.useEffect(() => {
    const isValid = exam.title.trim() &&
                   exam.start_time &&
                   exam.total_duration > 0 &&
                   classId &&
                   subjectId;
    setExamDetailsValid(isValid);
  }, [exam.title, exam.start_time, exam.total_duration, classId, subjectId]);

  // Exam form handlers
  const handleExamChange = (e) => {
    const { name, value } = e.target;
    setExam((prev) => ({ ...prev, [name]: value }));
  };

  // Question form handlers
  const handleQuestionChange = (e) => {
    const { name, value } = e.target;
    setQuestionForm(prev => ({ ...prev, [name]: value }));
  };
  const handleOptionChange = (idx, field, value) => {
    setQuestionForm(prev => ({
      ...prev,
      options: prev.options.map((opt, i) =>
        i === idx ? { ...opt, [field]: field === "is_correct" ? value : value } : opt
      ),
    }));
  };
  const handleCorrectOption = (idx) => {
    setQuestionForm(prev => ({
      ...prev,
      options: prev.options.map((opt, i) => ({ ...opt, is_correct: i === idx })),
    }));
  };

  // Switch question type
  const handleQuestionTypeChange = (type) => {
    setQuestionType(type);
    setQuestionForm(type === "MCQS" ? initialQuestionState : initialDescriptiveState);
    if (type === "DESCRIPTIVE") setDescType("SHORT");
  };

  // Add question (manual)
  const handleAddQuestion = async (e) => {
    e.preventDefault();
    setError("");
    setIsSubmitting(true);
    try {
      // Validate question
      if (!questionForm.text.trim()) throw new Error("Question text is required");
      if (!chapterId) throw new Error("Please select chapter");
      if (questionType === "MCQS") {
        if (!questionForm.options.some((opt) => opt.is_correct)) throw new Error("Select a correct option");
        if (questionForm.options.some((opt) => !opt.option_text.trim())) throw new Error("All options required");
      }
      // Prepare question object
      const questionObj = {
        ...questionForm,
        Type: questionType === "MCQS" ? "MCQS" : descType, // "SHORT" or "LONG"
        chapter_id: chapterId,
        topic_id: topicId || undefined,
        subtopic_id: subtopicId || undefined,
        id: Date.now() + Math.random(), // Temporary unique id for local array
      };
      setQuestions((prev) => [...prev, questionObj]);
      setQuestionForm(questionType === "MCQS" ? initialQuestionState : initialDescriptiveState);
    } catch (err) {
      setError(err?.message || "Failed to add question");
    } finally {
      setIsSubmitting(false);
    }
  };

  // AI Suggest
  const handleAISuggest = async () => {
    setError("");
    if (!chapterId) {
      setError("Please select chapter for AI suggestion");
      return;
    }
    setIsSubmitting(true);
    try {
      // Find names for class, subject, chapter, topic, subtopic
      const classObj = classrooms.find(c => c.id === classId);
      const subjectObj = subjects.find(s => s.id === subjectId);
      const chapterObj = chaptersBySubject.find(c => c.id === chapterId);
      const topicObj = topicsByChapter.find(t => t.id === topicId);
      const subtopicObj = subtopicsByTopic.find(s => s.id === subtopicId);
      const aiPayload = {
        class: classObj ? classObj.name : undefined,
        subject: subjectObj ? subjectObj.name : undefined,
        chapter: chapterObj ? chapterObj.name : undefined,
        topic: topicObj ? topicObj.name : undefined,
        subtopic: subtopicObj ? subtopicObj.name : undefined,
        type: questionType,
      };
      const result = await dispatch(aiGenerateQuestions(aiPayload)).unwrap();
      if (result && Array.isArray(result) && result.length > 0) {
        setQuestionForm(prev => ({
          ...prev,
          text: result[0].text || "",
          options: result[0].options || initialQuestionState.options,
          answer: result[0].answer || "",
        }));
      } else {
        setError("No AI suggestions returned");
      }
    } catch (err) {
      setError(err?.message || "AI suggestion failed");
    } finally {
      setIsSubmitting(false);
    }
  };

  // Step 2: Submit exam
  const handleSubmitExam = async (e) => {
    e.preventDefault();
    setError("");
    setSuccess("");
    setIsSubmitting(true);
    try {
      if (!exam.title.trim()) throw new Error("Exam title is required");
      if (questions.length === 0) throw new Error("Add at least one question");
      if (!classId || !subjectId) throw new Error("Please select class and subject in exam details");
      // Find subject name from subjects array
      const subjectObj = subjects.find(s => s.id === subjectId);
      const subjectName = subjectObj ? subjectObj.name : "";
      if (!subjectName) throw new Error("Please select a valid subject");
      // For each question, attach subject name and submit to API
      const questionsWithSubjectName = questions.map(q => ({
        ...q,
        subject_name: subjectName,
      }));
      const payload = {
        title: exam.title,
        assignment: {
          ...exam,
          total_marks: questionsWithSubjectName.reduce((sum, q) => sum + (q.marks || 1), 0),
          questions: questionsWithSubjectName,
          class_id: classId,
          subject_id: subjectId,
        }
      };
      await dispatch(createExamWithAssignment(payload)).unwrap();
      setSuccess("Exam created successfully!");
      setExam(initialExamState);
      setQuestions([]);
      setStep(1);
    } catch (err) {
      setError(err?.message || "Failed to create exam");
    } finally {
      setIsSubmitting(false);
    }
  };

  // Progress bar
  const ProgressBar = () => (
    <div className="w-full h-2 bg-gray-200 dark:bg-gray-700 rounded-full mb-6 overflow-hidden">
      <div
        className="h-2 bg-violet-600 dark:bg-violet-400 transition-all duration-700"
        style={{ width: `${progress}%` }}
      ></div>
    </div>
  );

  // Step indicator
  const StepIndicator = () => (
    <div className="flex items-center justify-center gap-4 mb-8">
      <div className={`flex items-center gap-3 px-4 py-2 rounded-lg transition-all duration-300 ${
        step === 1
          ? 'bg-violet-100 dark:bg-violet-900/30 text-violet-700 dark:text-violet-400 font-semibold'
          : step > 1
            ? 'bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-400'
            : 'bg-gray-100 dark:bg-gray-800 text-gray-500 dark:text-gray-400'
      }`}>
        <div className={`w-6 h-6 rounded-full flex items-center justify-center text-sm font-bold ${
          step === 1
            ? 'bg-violet-600 text-white'
            : step > 1
              ? 'bg-green-600 text-white'
              : 'bg-gray-400 text-white'
        }`}>
          {step > 1 ? <FiCheck className="w-4 h-4" /> : '1'}
        </div>
        <span className="hidden sm:inline">Exam Details</span>
      </div>

      <div className={`w-12 h-1 rounded-full transition-all duration-300 ${
        step > 1 ? 'bg-violet-600 dark:bg-violet-400' : 'bg-gray-300 dark:bg-gray-600'
      }`}></div>

      <div className={`flex items-center gap-3 px-4 py-2 rounded-lg transition-all duration-300 ${
        step === 2
          ? 'bg-violet-100 dark:bg-violet-900/30 text-violet-700 dark:text-violet-400 font-semibold'
          : 'bg-gray-100 dark:bg-gray-800 text-gray-500 dark:text-gray-400'
      }`}>
        <div className={`w-6 h-6 rounded-full flex items-center justify-center text-sm font-bold ${
          step === 2 ? 'bg-violet-600 text-white' : 'bg-gray-400 text-white'
        }`}>
          2
        </div>
        <span className="hidden sm:inline">Add Questions</span>
      </div>
    </div>
  );

  // Animated container
  const AnimatedContainer = ({ children }) => (
    <div className="transition-all duration-700 ease-in-out transform">
      {children}
    </div>
  );

  return (
    <div className={`min-h-screen py-8 px-2 sm:px-8 ${themeBg} ${themeText} transition-colors duration-300`}>
      <div className="max-w-2xl mx-auto">
        <h1 className="text-3xl font-bold mb-6 text-violet-700 dark:text-violet-400 text-center">Create Exam</h1>
        <ProgressBar />
        <StepIndicator />
        <AnimatedContainer>
          {step === 1 && (
            <div className="space-y-6 bg-white dark:bg-gray-800 rounded-xl shadow-lg p-8 animate-fade-in">
              <div className="text-center mb-6">
                <div className="inline-flex items-center justify-center w-16 h-16 bg-violet-100 dark:bg-violet-900/30 rounded-full mb-4">
                  <FiFileText className="w-8 h-8 text-violet-600 dark:text-violet-400" />
                </div>
                <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-2">Exam Details</h2>
                <p className="text-gray-600 dark:text-gray-400">Set up your exam information and requirements</p>
              </div>

              <form className="space-y-6">
                {/* Basic Exam Information */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="md:col-span-2">
                    <label className={`block mb-2 font-medium ${themeLabel} flex items-center gap-2`}>
                      <FiBookOpen className="w-4 h-4" />
                      Exam Title <span className="text-red-500">*</span>
                    </label>
                    <input
                      type="text"
                      name="title"
                      value={exam.title}
                      onChange={handleExamChange}
                      className={`w-full rounded-lg px-4 py-3 border-2 focus:outline-none focus:ring-2 focus:ring-violet-500 focus:border-transparent transition-all duration-200 ${themeInput}`}
                      placeholder="Enter exam title (e.g., Mathematics Mid-term Exam)"
                      required
                    />
                  </div>

                  <div className="md:col-span-2">
                    <label className={`block mb-2 font-medium ${themeLabel} flex items-center gap-2`}>
                      <FiEdit3 className="w-4 h-4" />
                      Description
                    </label>
                    <textarea
                      name="description"
                      value={exam.description}
                      onChange={handleExamChange}
                      rows={3}
                      className={`w-full rounded-lg px-4 py-3 border-2 focus:outline-none focus:ring-2 focus:ring-violet-500 focus:border-transparent transition-all duration-200 ${themeInput}`}
                      placeholder="Provide exam instructions and description..."
                    />
                  </div>
                </div>

                {/* Class and Subject Selection */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className={`block mb-2 font-medium ${themeLabel} flex items-center gap-2`}>
                      <FiTarget className="w-4 h-4" />
                      Class <span className="text-red-500">*</span>
                    </label>
                    <select
                      value={classId}
                      onChange={e => setClassId(e.target.value)}
                      className={`w-full rounded-lg px-4 py-3 border-2 focus:outline-none focus:ring-2 focus:ring-violet-500 focus:border-transparent transition-all duration-200 ${themeInput}`}
                      required
                    >
                      <option value="">Select Class</option>
                      {classrooms && classrooms.map(cls => (
                        <option key={cls.id} value={cls.id}>{cls.name}</option>
                      ))}
                    </select>
                  </div>

                  <div>
                    <label className={`block mb-2 font-medium ${themeLabel} flex items-center gap-2`}>
                      <FiBookOpen className="w-4 h-4" />
                      Subject <span className="text-red-500">*</span>
                    </label>
                    <select
                      value={subjectId}
                      onChange={e => setSubjectId(e.target.value)}
                      className={`w-full rounded-lg px-4 py-3 border-2 focus:outline-none focus:ring-2 focus:ring-violet-500 focus:border-transparent transition-all duration-200 ${themeInput}`}
                      required
                    >
                      <option value="">Select Subject</option>
                      {subjects && subjects.map(subject => (
                        <option key={subject.id} value={subject.id}>{subject.name}</option>
                      ))}
                    </select>
                  </div>
                </div>

                {/* Timing Information */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className={`block mb-2 font-medium ${themeLabel} flex items-center gap-2`}>
                      <FiCalendar className="w-4 h-4" />
                      Start Date & Time <span className="text-red-500">*</span>
                    </label>
                    <input
                      type="datetime-local"
                      name="start_time"
                      value={exam.start_time}
                      onChange={handleExamChange}
                      className={`w-full rounded-lg px-4 py-3 border-2 focus:outline-none focus:ring-2 focus:ring-violet-500 focus:border-transparent transition-all duration-200 ${themeInput}`}
                      required
                    />
                  </div>

                  <div>
                    <label className={`block mb-2 font-medium ${themeLabel} flex items-center gap-2`}>
                      <FiClock className="w-4 h-4" />
                      Duration (minutes) <span className="text-red-500">*</span>
                    </label>
                    <input
                      type="number"
                      name="total_duration"
                      value={exam.total_duration}
                      onChange={handleExamChange}
                      min={1}
                      max={480}
                      className={`w-full rounded-lg px-4 py-3 border-2 focus:outline-none focus:ring-2 focus:ring-violet-500 focus:border-transparent transition-all duration-200 ${themeInput}`}
                      placeholder="e.g., 60"
                      required
                    />
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="flex justify-end pt-6 border-t border-gray-200 dark:border-gray-700">
                  <button
                    type="button"
                    onClick={() => setStep(2)}
                    disabled={!examDetailsValid}
                    className={`px-8 py-3 rounded-lg font-semibold shadow-lg transition-all duration-200 flex items-center gap-2 ${
                      examDetailsValid
                        ? 'bg-violet-600 hover:bg-violet-700 text-white transform hover:scale-105'
                        : 'bg-gray-300 dark:bg-gray-700 text-gray-500 dark:text-gray-400 cursor-not-allowed'
                    }`}
                  >
                    Next: Add Questions
                    <FiArrowRight className="w-4 h-4" />
                  </button>
                </div>

                {error && (
                  <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4 flex items-center gap-3">
                    <FiX className="w-5 h-5 text-red-500" />
                    <span className="text-red-700 dark:text-red-400">{error}</span>
                  </div>
                )}
              </form>
            </div>
          )}

          {step === 2 && (
            <div className="space-y-6 bg-white dark:bg-gray-800 rounded-xl shadow-lg p-8 animate-fade-in">
              <div className="text-center mb-6">
                <div className="inline-flex items-center justify-center w-16 h-16 bg-violet-100 dark:bg-violet-900/30 rounded-full mb-4">
                  <FiPlus className="w-8 h-8 text-violet-600 dark:text-violet-400" />
                </div>
                <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-2">Add Questions</h2>
                <p className="text-gray-600 dark:text-gray-400">Create questions for your exam</p>
              </div>

              {/* Exam Summary */}
              <div className="bg-violet-50 dark:bg-violet-900/20 rounded-lg p-4 mb-6">
                <h3 className="font-semibold text-violet-700 dark:text-violet-300 mb-2">Exam Summary</h3>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                  <div>
                    <span className="text-gray-600 dark:text-gray-400">Title:</span>
                    <p className="font-medium">{exam.title}</p>
                  </div>
                  <div>
                    <span className="text-gray-600 dark:text-gray-400">Duration:</span>
                    <p className="font-medium">{exam.total_duration} minutes</p>
                  </div>
                  <div>
                    <span className="text-gray-600 dark:text-gray-400">Questions:</span>
                    <p className="font-medium">{questions.length}</p>
                  </div>
                  <div>
                    <span className="text-gray-600 dark:text-gray-400">Total Marks:</span>
                    <p className="font-medium">{questions.reduce((sum, q) => sum + (q.marks || 1), 0)}</p>
                  </div>
                </div>
              </div>

              {/* Chapter/Topic/Subtopic Selection */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                <div>
                  <label className={`block mb-2 font-medium ${themeLabel}`}>
                    Chapter <span className="text-red-500">*</span>
                  </label>
                  <select
                    value={chapterId}
                    onChange={e => setChapterId(e.target.value)}
                    className={`w-full rounded-lg px-3 py-2 border ${themeInput}`}
                    required
                    disabled={chaptersLoading}
                  >
                    <option value="">{chaptersLoading ? "Loading..." : "Select Chapter"}</option>
                    {chaptersBySubject && chaptersBySubject.map(opt => (
                      <option key={opt.id} value={opt.id}>{opt.name}</option>
                    ))}
                  </select>
                </div>
                <div>
                  <label className={`block mb-2 font-medium ${themeLabel}`}>Topic (optional)</label>
                  <select
                    value={topicId}
                    onChange={e => setTopicId(e.target.value)}
                    className={`w-full rounded-lg px-3 py-2 border ${themeInput}`}
                    disabled={topicsLoading || !chapterId}
                  >
                    <option value="">{topicsLoading ? "Loading..." : chapterId ? "Select Topic" : "Select Chapter First"}</option>
                    {topicsByChapter && topicsByChapter.map(opt => (
                      <option key={opt.id} value={opt.id}>{opt.name}</option>
                    ))}
                  </select>
                </div>
                <div>
                  <label className={`block mb-2 font-medium ${themeLabel}`}>Subtopic (optional)</label>
                  <select
                    value={subtopicId}
                    onChange={e => setSubtopicId(e.target.value)}
                    className={`w-full rounded-lg px-3 py-2 border ${themeInput}`}
                    disabled={subtopicsLoading || !topicId}
                  >
                    <option value="">{subtopicsLoading ? "Loading..." : topicId ? "Select Subtopic" : "Select Topic First"}</option>
                    {subtopicsByTopic && subtopicsByTopic.map(opt => (
                      <option key={opt.id} value={opt.id}>{opt.name}</option>
                    ))}
                  </select>
                </div>
              </div>

              {/* Question Type Selection */}
              <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-6">
                <div className="flex gap-2">
                  <button
                    type="button"
                    className={`px-6 py-3 rounded-lg font-semibold transition-all duration-200 ${
                      questionType === "MCQS"
                        ? "bg-violet-600 text-white shadow-lg"
                        : "bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-200 hover:bg-gray-300 dark:hover:bg-gray-600"
                    }`}
                    onClick={() => handleQuestionTypeChange("MCQS")}
                  >
                    Multiple Choice
                  </button>
                  <button
                    type="button"
                    className={`px-6 py-3 rounded-lg font-semibold transition-all duration-200 ${
                      questionType === "DESCRIPTIVE"
                        ? "bg-violet-600 text-white shadow-lg"
                        : "bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-200 hover:bg-gray-300 dark:hover:bg-gray-600"
                    }`}
                    onClick={() => handleQuestionTypeChange("DESCRIPTIVE")}
                  >
                    Descriptive
                  </button>
                </div>
                <button
                  type="button"
                  onClick={handleAISuggest}
                  className="px-4 py-2 bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-200 rounded-lg hover:bg-blue-200 dark:hover:bg-blue-800 transition-all duration-200 flex items-center gap-2"
                  disabled={!chapterId}
                >
                  <FiZap className="w-4 h-4" />
                  AI Suggest
                </button>
              </div>

              {/* Question Form */}
              {questionType === "DESCRIPTIVE" && (
                <div className="mb-4">
                  <label className={`block mb-2 font-medium ${themeLabel}`}>Descriptive Type</label>
                  <select
                    value={descType}
                    onChange={e => setDescType(e.target.value)}
                    className={`w-full rounded-lg px-3 py-2 border ${themeInput}`}
                    required
                  >
                    <option value="SHORT">Short Answer</option>
                    <option value="LONG">Long Answer</option>
                  </select>
                </div>
              )}

              <form onSubmit={handleAddQuestion} className="space-y-6">
                <div>
                  <label className={`block mb-2 font-medium ${themeLabel}`}>Question Text <span className="text-red-500">*</span></label>
                  <textarea
                    name="text"
                    value={questionForm.text}
                    onChange={handleQuestionChange}
                    rows={3}
                    className={`w-full rounded-lg px-4 py-3 border-2 focus:outline-none focus:ring-2 focus:ring-violet-500 focus:border-transparent transition-all duration-200 ${themeInput}`}
                    placeholder="Enter your question here..."
                    required
                  />
                </div>

                {questionType === "MCQS" && (
                  <div>
                    <label className={`block mb-2 font-medium ${themeLabel}`}>Options</label>
                    <div className="space-y-3">
                      {questionForm.options.map((opt, idx) => (
                        <div key={idx} className="flex items-center gap-3 p-3 border border-gray-200 dark:border-gray-700 rounded-lg">
                          <span className="w-6 h-6 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center text-sm font-medium">
                            {String.fromCharCode(65 + idx)}
                          </span>
                          <input
                            type="text"
                            value={opt.option_text}
                            onChange={(e) => handleOptionChange(idx, "option_text", e.target.value)}
                            className={`flex-1 rounded-lg px-3 py-2 border focus:outline-none focus:ring-2 focus:ring-violet-500 ${themeInput}`}
                            placeholder={`Option ${idx + 1}`}
                            required
                          />
                          <label className="flex items-center gap-2 cursor-pointer">
                            <input
                              type="radio"
                              name="correctOption"
                              checked={opt.is_correct}
                              onChange={() => handleCorrectOption(idx)}
                              className="w-4 h-4 text-violet-600 focus:ring-violet-500"
                            />
                            <span className="text-sm text-gray-600 dark:text-gray-400">Correct</span>
                          </label>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                <div>
                  <label className={`block mb-2 font-medium ${themeLabel}`}>
                    {questionType === "MCQS" ? "Answer Explanation (optional)" : "Expected Answer"}
                  </label>
                  <textarea
                    name="answer"
                    value={questionForm.answer}
                    onChange={handleQuestionChange}
                    rows={2}
                    className={`w-full rounded-lg px-4 py-3 border-2 focus:outline-none focus:ring-2 focus:ring-violet-500 focus:border-transparent transition-all duration-200 ${themeInput}`}
                    placeholder={questionType === "MCQS" ? "Provide explanation for the correct answer..." : "Enter the expected answer..."}
                  />
                </div>

                <div className="flex items-center gap-4">
                  <div className="flex-1">
                    <label className={`block mb-2 font-medium ${themeLabel}`}>Marks</label>
                    <input
                      type="number"
                      name="marks"
                      value={questionForm.marks}
                      onChange={handleQuestionChange}
                      min={1}
                      max={100}
                      className={`w-full rounded-lg px-3 py-2 border ${themeInput}`}
                    />
                  </div>
                  <div className="flex-1">
                    <label className={`block mb-2 font-medium ${themeLabel}`}>Difficulty Level</label>
                    <select
                      name="Level"
                      value={questionForm.Level}
                      onChange={handleQuestionChange}
                      className={`w-full rounded-lg px-3 py-2 border ${themeInput}`}
                    >
                      <option value="EASY">Easy</option>
                      <option value="MEDIUM">Medium</option>
                      <option value="HARD">Hard</option>
                    </select>
                  </div>
                </div>

                <div className="flex justify-end">
                  <button
                    type="submit"
                    className="px-6 py-3 bg-violet-600 text-white rounded-lg font-semibold shadow-lg hover:bg-violet-700 transition-all duration-200 flex items-center gap-2"
                    disabled={isSubmitting || questionLoading}
                  >
                    <FiPlus className="w-4 h-4" />
                    {isSubmitting || questionLoading ? "Adding..." : "Add Question"}
                  </button>
                </div>
              </form>

              {/* Questions List */}
              {questions.length > 0 && (
                <div className="mt-8">
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                      Questions Added ({questions.length})
                    </h3>
                    <div className="text-sm text-gray-600 dark:text-gray-400">
                      Total Marks: {questions.reduce((sum, q) => sum + (q.marks || 1), 0)}
                    </div>
                  </div>
                  <div className="space-y-4">
                    {questions.map((q, idx) => (
                      <div key={q.id || idx} className="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4 border border-gray-200 dark:border-gray-600">
                        <div className="flex items-start justify-between mb-2">
                          <div className="flex items-center gap-2">
                            <span className="bg-violet-100 dark:bg-violet-900/30 text-violet-700 dark:text-violet-300 px-2 py-1 rounded text-sm font-medium">
                              Q{idx + 1}
                            </span>
                            <span className="bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-300 px-2 py-1 rounded text-xs">
                              {q.Type === 'MCQS' ? 'MCQ' : q.Type}
                            </span>
                            <span className="bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 px-2 py-1 rounded text-xs">
                              {q.Level}
                            </span>
                            <span className="bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300 px-2 py-1 rounded text-xs">
                              {q.marks || 1} marks
                            </span>
                          </div>
                          <button
                            onClick={() => setQuestions(prev => prev.filter((_, i) => i !== idx))}
                            className="text-red-500 hover:text-red-700 transition-colors"
                          >
                            <FiTrash2 className="w-4 h-4" />
                          </button>
                        </div>
                        <p className="text-gray-900 dark:text-gray-100 mb-2">{q.text}</p>
                        {q.Type === 'MCQS' && (
                          <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 mt-2">
                            {q.options.map((opt, i) => (
                              <div key={i} className={`p-2 rounded text-sm ${
                                opt.is_correct
                                  ? "bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-200 font-medium"
                                  : "bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300"
                              }`}>
                                {String.fromCharCode(65 + i)}. {opt.option_text}
                                {opt.is_correct && <span className="ml-2 text-green-600">✓</span>}
                              </div>
                            ))}
                          </div>
                        )}
                        {q.answer && (
                          <div className="mt-2 p-2 bg-blue-50 dark:bg-blue-900/20 rounded text-sm">
                            <span className="font-medium text-blue-700 dark:text-blue-300">Answer: </span>
                            <span className="text-blue-600 dark:text-blue-400">{q.answer}</span>
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Action Buttons */}
              <div className="flex justify-between pt-6 border-t border-gray-200 dark:border-gray-700">
                <button
                  type="button"
                  onClick={() => setStep(1)}
                  className="px-6 py-3 bg-gray-300 dark:bg-gray-700 text-gray-800 dark:text-gray-200 rounded-lg font-semibold shadow hover:bg-gray-400 dark:hover:bg-gray-600 transition-all duration-200 flex items-center gap-2"
                >
                  <FiArrowLeft className="w-4 h-4" />
                  Back to Details
                </button>
                <button
                  type="button"
                  onClick={handleSubmitExam}
                  disabled={questions.length === 0 || isSubmitting || examLoading}
                  className={`px-8 py-3 rounded-lg font-semibold shadow-lg transition-all duration-200 flex items-center gap-2 ${
                    questions.length > 0 && !isSubmitting && !examLoading
                      ? 'bg-green-600 hover:bg-green-700 text-white transform hover:scale-105'
                      : 'bg-gray-300 dark:bg-gray-700 text-gray-500 dark:text-gray-400 cursor-not-allowed'
                  }`}
                >
                  {isSubmitting || examLoading ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                      Creating Exam...
                    </>
                  ) : (
                    <>
                      <FiSave className="w-4 h-4" />
                      Create Exam
                    </>
                  )}
                </button>
              </div>

              {/* Success/Error Messages */}
              {success && (
                <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4 flex items-center gap-3">
                  <FiCheck className="w-5 h-5 text-green-500" />
                  <span className="text-green-700 dark:text-green-400">{success}</span>
                </div>
              )}
              {error && (
                <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4 flex items-center gap-3">
                  <FiX className="w-5 h-5 text-red-500" />
                  <span className="text-red-700 dark:text-red-400">{error}</span>
                </div>
              )}
            </div>
          )}
        </AnimatedContainer>
      </div>
    </div>
  );
}
