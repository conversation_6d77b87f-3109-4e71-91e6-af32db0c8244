import React, { useEffect, useState } from "react";
// import { useParams } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import { fetchClassroomById } from "../../features/classroom/ClassroomSlice";
import { FiHome, FiUsers, FiClock } from "react-icons/fi";
import RequestForm from "./RequestForm";
import CreateTask from "./CreateTask";
// Announcements
import {
  fetchAnnouncements,
  createAnnouncement,
  updateAnnouncement,
  deleteAnnouncement,
} from "../../features/announcements/AnnouncementSlice";

// Import new components
import ClassroomTabs from "../../components/classroom/ClassroomTabs";
import InfoTab from "../../components/classroom/teacher/InfoTab";
import StudentsTab from "../../components/classroom/teacher/StudentsTab";
import RequestsTab from "../../components/classroom/teacher/RequestsTab";

function ClassInfo({ classroomId: propClassroomId }) {
  // const { classroomId } = useParams();
  const classroomId = propClassroomId; // Always use prop for now
  const dispatch = useDispatch();
  const [activeTab, setActiveTab] = useState("info");
  const [showRequestForm, setShowRequestForm] = useState(false);
  const [requestFormFetched, setRequestFormFetched] = useState(false);
  const [showCreateTask, setShowCreateForm] = useState(false);
  const [refreshingRequests, setRefreshingRequests] = useState(false);
  const { classroom, loading, error } = useSelector((state) => state.classroom);

  // Announcements state
  const {
    announcements,
    loading: announcementsLoading,
    error: announcementsError,
  } = useSelector((state) => state.announcements);
  
  // Debug logging (only log when announcements state changes)
  useEffect(() => {
    // Uncomment the next line if you want to log only when announcements state changes
    // console.log('Announcements state:', { announcements, announcementsLoading, announcementsError });
  }, [announcements, announcementsLoading, announcementsError]);
  const [showAnnouncementForm, setShowAnnouncementForm] = useState(false);
  const [editAnnouncementId, setEditAnnouncementId] = useState(null);
  const [announcementForm, setAnnouncementForm] = useState({
    title: "",
    content: "",
  });
  const [deleteAnnouncementId, setDeleteAnnouncementId] = useState(null);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);

  // Tab configuration
  const tabs = [
    { id: 'info', label: 'Info', icon: FiHome },
    { id: 'students', label: 'Students', icon: FiUsers },
    { id: 'requests', label: 'Requests', icon: FiClock },
  ];

  // Fetch classroom and announcements
  useEffect(() => {
    dispatch(fetchClassroomById(classroomId));
  }, [dispatch, classroomId]);

  useEffect(() => {
    if (activeTab === "info") {
      dispatch(fetchAnnouncements({ classroom_id: classroomId, skip: 0, limit: 100 }));
    }
  }, [dispatch, classroomId, activeTab]);

  // Reload requests function
  const handleRefreshRequests = async () => {
    setRefreshingRequests(true);
    try {
      await dispatch(fetchClassroomById(classroomId)).unwrap();
    } catch (error) {
      console.error('Failed to refresh classroom data:', error);
    } finally {
      setRefreshingRequests(false);
    }
  };

  // Announcement handlers
  const openCreateAnnouncement = () => {
    setEditAnnouncementId(null);
    setAnnouncementForm({ title: "", content: "" });
    setShowAnnouncementForm(true);
  };
  const openEditAnnouncement = (a) => {
    setEditAnnouncementId(a.id);
    setAnnouncementForm({ title: a.title, content: a.content });
    setShowAnnouncementForm(true);
  };
  const handleAnnouncementFormChange = (e) => {
    const { name, value } = e.target;
    setAnnouncementForm((prev) => ({ ...prev, [name]: value }));
  };
  const handleAnnouncementFormSubmit = async (e) => {
    e.preventDefault();
    if (editAnnouncementId) {
      await dispatch(updateAnnouncement({ id: editAnnouncementId, data: announcementForm }));
    } else {
      await dispatch(createAnnouncement({ data: announcementForm, classroom_id: classroomId }));
    }
    setShowAnnouncementForm(false);
    dispatch(fetchAnnouncements({ classroom_id: classroomId, skip: 0, limit: 100 }));
  };
  const handleDeleteAnnouncement = async () => {
    if (deleteAnnouncementId) {
      await dispatch(deleteAnnouncement(deleteAnnouncementId));
      setShowDeleteConfirm(false);
      setDeleteAnnouncementId(null);
      dispatch(fetchAnnouncements({ classroom_id: classroomId, skip: 0, limit: 100 }));
    }
  };

  if (loading) return <p className="text-center mt-10 text-gray-500 dark:text-gray-400">Loading...</p>;
  if (error) return <p className="text-center mt-10 text-red-500 dark:text-red-400">Error: {typeof error === 'object' ? error.detail || JSON.stringify(error) : error}</p>;
  if (!classroom) return null;

  return (
    <div className="p-0 sm:p-0 bg-gray-50 dark:bg-gray-900 min-h-screen">
      {/* Top Banner */}
      <div className="relative h-40 sm:h-56 md:h-64 bg-gradient-to-r from-violet-700 to-violet-400 dark:from-violet-800 dark:to-violet-600 rounded-b-3xl shadow-lg flex items-end px-4 sm:px-8 pb-6 sm:pb-8">
        <div>
          <h2 className="text-2xl sm:text-4xl md:text-5xl font-extrabold text-white drop-shadow mb-2 break-words">{classroom.name}</h2>
          <p className="text-violet-100 text-sm sm:text-md md:text-lg max-w-full sm:max-w-2xl font-normal mt-2 break-words">{classroom.description}</p>
        </div>
      </div>

      {/* Tabs */}
      <div className="flex flex-col sm:flex-row items-center mt-8 mb-2 max-w-3xl md:max-w-4xl lg:max-w-5xl mx-auto px-2 sm:px-4 gap-2 sm:gap-0">
        <div className="w-full sm:flex-1 flex justify-center">
          <div className="bg-white dark:bg-gray-800 rounded-full shadow px-2 py-1 flex gap-2 border border-gray-200 dark:border-gray-700">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`px-6 py-2 rounded-full font-semibold text-md transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-violet-300 ${
                  activeTab === tab.id
                    ? 'bg-violet-600 dark:bg-violet-500 text-white shadow'
                    : 'bg-transparent text-gray-600 dark:text-gray-300 hover:bg-violet-50 dark:hover:text-violet-900'
                }`}
              >
                {tab.label}
              </button>
            ))}
          </div>
        </div>
      </div>

      <div className="max-w-full sm:max-w-3xl mx-auto space-y-6 sm:space-y-8 mt-6 sm:mt-8 px-2 sm:px-4">
        {/* Info Tab */}
        {activeTab === "info" && (
          <>
            {/* Create Task button above Announcements, right-aligned in the same column */}
            <div className="flex justify-end mb-2 w-full gap-2 flex-wrap">
              <button
                onClick={() => setShowCreateForm(true)}
                className="px-4 sm:px-6 py-2 bg-violet-600 dark:bg-violet-500 text-white rounded-full font-semibold shadow hover:bg-violet-700 dark:hover:bg-violet-600 transition w-full sm:w-auto"
              >
                + Create Task
              </button>
              <button
                onClick={openCreateAnnouncement}
                className="px-4 sm:px-6 py-2 bg-violet-100 dark:bg-violet-700 text-violet-700 dark:text-violet-100 rounded-full font-semibold shadow hover:bg-violet-200 dark:hover:bg-violet-800 transition w-full sm:w-auto"
              >
                + Announcement
              </button>
            </div>

            {showCreateTask && <CreateTask onClose={() => setShowCreateForm(false)} />}

            <InfoTab
              announcements={announcements}
              announcementsLoading={announcementsLoading}
              onCreateAnnouncement={openCreateAnnouncement}
              onEditAnnouncement={openEditAnnouncement}
              onDeleteAnnouncement={(announcement) => {
                setDeleteAnnouncementId(announcement.id);
                setShowDeleteConfirm(true);
              }}
              currentTheme="light"
            />

            {/* Announcements Section */}
            <div className="bg-white dark:bg-gray-800 p-4 sm:p-8 rounded-2xl shadow">
              <h3 className="text-2xl font-bold text-violet-700 dark:text-violet-400 mb-4">Announcements</h3>
              {announcementsLoading ? (
                <div className="text-center text-gray-400 dark:text-gray-500">Loading...</div>
              ) : announcementsError ? (
                <div className="text-center text-red-500 dark:text-red-400">{typeof announcementsError === 'string' ? announcementsError : 'Failed to load announcements.'}</div>
              ) : !announcements || announcements.length === 0 ? (
                <p className="text-gray-400 dark:text-gray-500 italic">No announcements yet.</p>
              ) : (
                <div className="space-y-4">
                  {announcements.map((a) => (
                    <div key={a.id} className="bg-violet-50 dark:bg-violet-900/30 rounded-lg p-4 flex flex-col sm:flex-row sm:items-center justify-between gap-2 shadow-sm">
                      <div>
                        <div className="text-lg font-semibold text-violet-900 dark:text-violet-100">{a.title}</div>
                        <div className="text-gray-700 dark:text-gray-200 mt-1 whitespace-pre-line">{a.content}</div>
                        <div className="text-xs text-gray-400 dark:text-gray-500 mt-1">{new Date(a.created_at).toLocaleString()}</div>
                      </div>
                      <div className="flex gap-2 mt-2 sm:mt-0">
                        <button
                          className="px-3 py-1 text-xs rounded bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200 hover:underline"
                          onClick={() => openEditAnnouncement(a)}
                        >
                          Edit
                        </button>
                        <button
                          className="px-3 py-1 text-xs rounded bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200 hover:underline"
                          onClick={() => { setDeleteAnnouncementId(a.id); setShowDeleteConfirm(true); }}
                        >
                          Delete
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>

            {/* Announcement Modal */}
            {showAnnouncementForm && (
              <div className="fixed inset-0 z-50 flex items-center justify-center">
                <div className="absolute inset-0 bg-black/30 backdrop-blur-sm z-0" onClick={() => setShowAnnouncementForm(false)} />
                <div className="relative z-10 w-full max-w-md mx-auto rounded-lg shadow-lg p-8 bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100">
                  <h2 className="text-xl font-bold mb-4 text-violet-700 dark:text-violet-400">{editAnnouncementId ? "Edit Announcement" : "New Announcement"}</h2>
                  <form onSubmit={handleAnnouncementFormSubmit} className="space-y-4">
                    <div>
                      <label className="block mb-1 font-medium text-gray-700 dark:text-gray-300">Title</label>
                      <input
                        type="text"
                        name="title"
                        value={announcementForm.title}
                        onChange={handleAnnouncementFormChange}
                        className="w-full rounded px-3 py-2 border focus:outline-none focus:ring-2 focus:ring-violet-500 bg-gray-50 dark:bg-gray-800 text-gray-900 dark:text-gray-100 border-gray-300 dark:border-gray-700"
                        required
                      />
                    </div>
                    <div>
                      <label className="block mb-1 font-medium text-gray-700 dark:text-gray-300">Content</label>
                      <textarea
                        name="content"
                        value={announcementForm.content}
                        onChange={handleAnnouncementFormChange}
                        rows={3}
                        className="w-full rounded px-3 py-2 border focus:outline-none focus:ring-2 focus:ring-violet-500 bg-gray-50 dark:bg-gray-800 text-gray-900 dark:text-gray-100 border-gray-300 dark:border-gray-700"
                        required
                      />
                    </div>
                    <div className="flex gap-2 mt-4">
                      <button
                        type="submit"
                        className="px-4 py-2 bg-violet-600 text-white rounded hover:bg-violet-700 transition"
                        disabled={announcementsLoading}
                      >
                        {editAnnouncementId ? "Update" : "Create"}
                      </button>
                      <button
                        type="button"
                        className="px-4 py-2 bg-gray-300 dark:bg-gray-700 text-gray-800 dark:text-gray-200 rounded hover:bg-gray-400 dark:hover:bg-gray-600 transition"
                        onClick={() => setShowAnnouncementForm(false)}
                        disabled={announcementsLoading}
                      >
                        Cancel
                      </button>
                    </div>
                  </form>
                </div>
              </div>
            )}

            {/* Delete Confirmation Modal */}
            {showDeleteConfirm && (
              <div className="fixed inset-0 z-50 flex items-center justify-center">
                <div className="absolute inset-0 bg-black/30 backdrop-blur-sm z-0" onClick={() => setShowDeleteConfirm(false)} />
                <div className="relative z-10 w-full max-w-sm mx-auto rounded-lg shadow-lg p-6 bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100">
                  <h2 className="text-lg font-bold mb-4 text-red-600">Delete Announcement</h2>
                  <p className="mb-4">Are you sure you want to delete this announcement? This action cannot be undone.</p>
                  <div className="flex gap-2">
                    <button
                      className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 transition"
                      onClick={handleDeleteAnnouncement}
                      disabled={announcementsLoading}
                    >
                      Delete
                    </button>
                    <button
                      className="px-4 py-2 bg-gray-300 dark:bg-gray-700 text-gray-800 dark:text-gray-200 rounded hover:bg-gray-400 dark:hover:bg-gray-600 transition"
                      onClick={() => setShowDeleteConfirm(false)}
                      disabled={announcementsLoading}
                    >
                      Cancel
                    </button>
                  </div>
                </div>
              </div>
            )}
          </>
        )}

        {/* Students Tab */}
        {activeTab === "students" && (
          <div className="space-y-3 sm:space-y-4">
            {classroom.students?.length > 0 ? (
              classroom.students.map((student) => (
                <div
                  key={student.id}
                  className="bg-white dark:bg-gray-800 p-4 sm:p-6 rounded-2xl shadow flex flex-col sm:flex-row items-center gap-3 sm:gap-4"
                >
                  {/* Profile Picture */}
                  {student.profile_picture ? (
                    <img
                      src={student.profile_picture}
                      alt={student.username}
                      className="w-14 h-14 rounded-full object-cover border border-gray-200 dark:border-gray-700"
                    />
                  ) : (
                    <div className="w-14 h-14 rounded-full bg-violet-100 dark:bg-violet-700 flex items-center justify-center text-2xl font-bold text-violet-600 dark:text-violet-200 border border-gray-200 dark:border-gray-700">
                      {student.username ? student.username.charAt(0).toUpperCase() : '?'}
                    </div>
                  )}
                  <div>
                    <div className="text-lg font-semibold text-gray-900 dark:text-gray-100">{student.username}</div>
                    <sub className="text-gray-500 dark:text-gray-400 text-xs">{student.email}</sub>
                  </div>
                </div>
              ))
            ) : (
              <p className="text-center text-gray-500 dark:text-gray-400">No students enrolled yet.</p>
            )}
          </div>
        )}

        {/* Requests Tab */}
        {activeTab === "requests" && (
          <div className="space-y-3 sm:space-y-4">
            <div className="flex justify-between items-center">
              <div className="flex items-center space-x-3">
                <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-100">
                  Student Requests
                </h3>
                <button
                  onClick={handleRefreshRequests}
                  disabled={refreshingRequests}
                  className="flex items-center space-x-2 px-4 py-2 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-full font-medium shadow hover:bg-gray-200 dark:hover:bg-gray-600 transition disabled:opacity-50 disabled:cursor-not-allowed"
                  title="Refresh requests"
                >
                  <FiRefreshCw className={`w-4 h-4 ${refreshingRequests ? 'animate-spin' : ''}`} />
                  <span className="hidden sm:inline">{refreshingRequests ? 'Refreshing...' : 'Refresh'}</span>
                </button>
              </div>
              <button
                onClick={() => setShowRequestForm(true)}
                className="px-6 py-2 bg-violet-600 dark:bg-violet-500 text-white rounded-full font-semibold shadow hover:bg-violet-700 dark:hover:bg-violet-600 transition"
              >
                + Request Students
              </button>
            </div>

            {refreshingRequests ? (
              <div className="bg-white dark:bg-gray-800 p-8 rounded-2xl shadow">
                <div className="flex items-center justify-center">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-violet-600"></div>
                  <span className="ml-3 text-gray-600 dark:text-gray-400">Loading requests...</span>
                </div>
              </div>
            ) : classroom.class_requests?.length > 0 ? (
              classroom.class_requests.map((req) => (
                <div
                  key={req.id}
                  className="bg-white dark:bg-gray-800 p-4 sm:p-6 rounded-2xl shadow flex flex-col sm:flex-row items-center gap-3 sm:gap-4"
                >
                  {/* Profile Picture */}
                  {req.student_user?.profile_picture ? (
                    <img
                      src={req.student_user.profile_picture}
                      alt={req.student_user.username}
                      className="w-14 h-14 rounded-full object-cover border border-gray-200 dark:border-gray-700"
                    />
                  ) : (
                    <div className="w-14 h-14 rounded-full bg-violet-100 dark:bg-violet-700 flex items-center justify-center text-2xl font-bold text-violet-600 dark:text-violet-200 border border-gray-200 dark:border-gray-700">
                      {req.student_user?.username ? req.student_user.username.charAt(0).toUpperCase() : '?'}
                    </div>
                  )}
                  <div>
                    <div className="text-lg font-semibold text-gray-900 dark:text-gray-100">{req.student_user?.username}</div>
                    <sub className="text-gray-500 dark:text-gray-400 text-xs">{req.student_user?.email}</sub>
                  </div>
                </div>
              ))
            ) : (
              <p className="text-center text-gray-500 dark:text-gray-400">No join requests yet.</p>
            )}
          </div>
        )}
      </div>

      {/* Request Form Modal */}
      <RequestForm
        show={showRequestForm}
        onClose={() => { setShowRequestForm(false); setRequestFormFetched(false); }}
        classID={classroomId}
        fetched={requestFormFetched}
        setFetched={setRequestFormFetched}
      />
    </div>
  );
}

export default ClassInfo;
