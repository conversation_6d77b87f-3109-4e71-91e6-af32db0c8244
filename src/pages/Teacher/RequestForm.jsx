import React, { useEffect, useState } from "react";
import { useSelector, useDispatch } from "react-redux";
import { fetchStudentsNotInClassroom, requestStudentToJoinClassroom } from "../../features/classroom/ClassroomSlice";
import { FiRefreshCw } from "react-icons/fi";


function RequestForm({ show, onClose, classID, fetched, setFetched }) {
  const [search, setSearch] = useState("");
  const [confirmStudent, setConfirmStudent] = useState(null);
  const [error, setError] = useState("");
  const [isReloading, setIsReloading] = useState(false);
  const dispatch = useDispatch();

  const { studentsNotInClassroom, loading } = useSelector((state) => state.classroom);

  useEffect(() => {
    if (classID && !fetched) {
      dispatch(fetchStudentsNotInClassroom({ classroom_id: classID, skip: 0, limit: 10 }));
      setFetched(true);
    }
  }, [dispatch, classID, fetched, setFetched]);

  const filteredStudents = Array.isArray(studentsNotInClassroom)
    ? studentsNotInClassroom.filter((s) =>
        `${s.username} ${s.email}`.toLowerCase().includes(search.toLowerCase())
      )
    : [];

  const handleConfirm = async () => {
    setError("");
    try {
      await dispatch(requestStudentToJoinClassroom({
        classroom_id: classID,
        requestData: { student_id: confirmStudent.id },
      })).unwrap();
      setConfirmStudent(null);
    } catch (err) {
      // Handle different error formats
      let errorMessage = "Failed to send request. Please try again.";

      if (typeof err === 'string') {
        errorMessage = err;
      } else if (err?.message) {
        errorMessage = err.message;
      } else if (err?.detail) {
        errorMessage = err.detail;
      } else if (err?.error) {
        errorMessage = err.error;
      }

      setError(errorMessage);
    }
  };

  const handleReload = async () => {
    setIsReloading(true);
    setError("");
    try {
      await dispatch(fetchStudentsNotInClassroom({ classroom_id: classID, skip: 0, limit: 10 })).unwrap();
    } catch (err) {
      // Handle reload error
      let errorMessage = "Failed to reload students. Please try again.";

      if (typeof err === 'string') {
        errorMessage = err;
      } else if (err?.message) {
        errorMessage = err.message;
      } else if (err?.detail) {
        errorMessage = err.detail;
      } else if (err?.error) {
        errorMessage = err.error;
      }

      setError(errorMessage);
    } finally {
      setIsReloading(false);
    }
  };

  if (!show) return null;
  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      {/* Overlay */}
      <div
        className="absolute inset-0 bg-black/30 backdrop-blur-sm"
        onClick={onClose}
      />

      {/* Modal */}
      <div className="relative z-10 w-full max-w-lg bg-white dark:bg-gray-800 p-6 rounded-xl shadow-xl max-h-[80vh] overflow-hidden">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-2xl font-bold text-violet-700 dark:text-violet-400">Request Students</h2>
          <button
            onClick={() => { setError(""); onClose(); }}
            className="text-gray-500 dark:text-gray-400 hover:text-red-600 dark:hover:text-red-400 text-2xl font-bold"
          >
            &times;
          </button>
        </div>

        {/* Error Display */}
        {error && (
          <div className="mb-4 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md">
            <p className="text-red-600 dark:text-red-400 text-sm">{error}</p>
          </div>
        )}

        {/* Search Input and Reload Button */}
        <div className="flex gap-2 mb-4">
          <input
            type="text"
            placeholder="Search by name or email..."
            value={search}
            onChange={(e) => setSearch(e.target.value)}
            className="flex-1 px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-violet-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400"
          />
          <button
            onClick={handleReload}
            disabled={isReloading || loading}
            className="px-4 py-2 bg-violet-600 dark:bg-violet-500 text-white rounded-md hover:bg-violet-700 dark:hover:bg-violet-600 transition disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2"
            title="Reload students list"
          >
            <FiRefreshCw className={`w-4 h-4 ${isReloading ? 'animate-spin' : ''}`} />
            {isReloading ? 'Loading...' : 'Reload'}
          </button>
        </div>

        {/* Student List */}
        <div className="space-y-3 overflow-y-auto max-h-64 pr-1">
          {(loading || isReloading) ? (
            <div className="flex items-center justify-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-violet-600"></div>
              <span className="ml-2 text-gray-600 dark:text-gray-400">
                {isReloading ? 'Reloading students...' : 'Loading students...'}
              </span>
            </div>
          ) : filteredStudents.length > 0 ? (
            filteredStudents.map((student) => (
              <div
                key={student.id}
                className="flex items-center justify-between bg-gray-50 dark:bg-gray-900 border border-gray-200 dark:border-gray-700 rounded-md px-4 py-3 shadow-sm hover:bg-gray-100 dark:hover:bg-gray-800"
              >
                <div className="flex items-center space-x-4">
                  {student.profile_picture ? (
                    <img
                      src={student.profile_picture}
                      alt={student.username}
                      className="w-12 h-12 rounded-full object-cover"
                    />
                  ) : (
                    <div className="w-12 h-12 rounded-full bg-violet-100 dark:bg-violet-700 flex items-center justify-center text-lg font-bold text-violet-600 dark:text-violet-200 border border-gray-200 dark:border-gray-700">
                      {student.username ? student.username.charAt(0).toUpperCase() : "?"}
                    </div>
                  )}
                  <div>
                    <p className="font-semibold text-gray-800 dark:text-gray-100">{student.username}</p>
                    <p className="text-sm text-gray-500 dark:text-gray-400">{student.email}</p>
                  </div>
                </div>
                <button
                  onClick={() => setConfirmStudent(student)}
                  className="bg-violet-600 dark:bg-violet-500 text-white px-3 py-1 rounded-md hover:bg-violet-700 dark:hover:bg-violet-600 transition"
                >
                  Request
                </button>
              </div>
            ))
          ) : (
            <p className="text-center text-gray-500 dark:text-gray-400">No students found.</p>
          )}
        </div>
      </div>

      {/* Confirm Dialog */}
      {confirmStudent && (
        <div className="fixed inset-0 z-60 flex items-center justify-center">
          <div
            className="absolute inset-0 bg-black/40 backdrop-blur-sm"
            onClick={() => { setError(""); setConfirmStudent(null); }}
          />
          <div className="relative bg-white dark:bg-gray-800 p-6 rounded-lg shadow-2xl w-full max-w-sm z-70 text-center">
            {error && (
              <div className="mb-2 text-red-600 dark:text-red-400 text-sm font-semibold">{error}</div>
            )}
            {confirmStudent.profile_picture ? (
              <img
                src={confirmStudent.profile_picture}
                alt={confirmStudent.username}
                className="w-16 h-16 mx-auto rounded-full mb-3"
              />
            ) : (
              <div className="w-16 h-16 mx-auto rounded-full mb-3 bg-violet-100 dark:bg-violet-700 flex items-center justify-center text-2xl font-bold text-violet-600 dark:text-violet-200 border border-gray-200 dark:border-gray-700">
                {confirmStudent.username ? confirmStudent.username.charAt(0).toUpperCase() : "?"}
              </div>
            )}
            <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-100 mb-2">
              Send request to <span className="text-violet-600 dark:text-violet-400">{confirmStudent.username}</span>?
            </h3>
            <p className="text-sm text-gray-500 dark:text-gray-400">{confirmStudent.email}</p>
            <div className="mt-5 flex justify-center gap-4">
              <button
                onClick={() => { setError(""); setConfirmStudent(null); }}
                className="px-4 py-2 rounded-md border border-gray-400 dark:border-gray-600 text-gray-700 dark:text-gray-200 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 transition"
              >
                Cancel
              </button>
              <button
                onClick={handleConfirm}
                className="px-4 py-2 rounded-md bg-violet-600 dark:bg-violet-500 text-white hover:bg-violet-700 dark:hover:bg-violet-600 transition disabled:opacity-60 disabled:cursor-not-allowed"
                disabled={loading}
              >
                {loading ? (
                  <span className="flex items-center justify-center gap-2">
                    <svg className="animate-spin h-4 w-4 text-white" viewBox="0 0 24 24"><circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" fill="none" /><path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8v8z" /></svg>
                    Loading...
                  </span>
                ) : (
                  'Confirm'
                )}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

export default RequestForm;
