import { useEffect, useState, useMemo } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { fetchAllOwnClasses } from '../../features/classroom/ClassroomSlice';
import { fetchCurrentUser } from '../../features/users/userSlice';
import SkeletonLoader from '../../components/ui/SkeletonLoader';
import {
  FiUsers,
  FiBookOpen,
  FiCheckCircle,
  FiTrendingUp,
  FiPlus,
  FiRefreshCw,
  FiMoreVertical
} from 'react-icons/fi';
import logger from '../../utils/logger';
import { handleApiError } from '../../utils/errorHandler';

function TeacherDashboard() {
  const dispatch = useDispatch();
  const { classrooms, loading: classroomsLoading, error: classroomsError } = useSelector((state) => state.classroom);
  const { currentUser, loading: userLoading } = useSelector((state) => state.users);

  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    const loadData = async () => {
      try {
        await Promise.all([
          dispatch(fetchCurrentUser()).unwrap(),
          dispatch(fetchAllOwnClasses()).unwrap()
        ]);
        logger.info('Teacher dashboard data loaded successfully', null, 'TeacherDashboard');
      } catch (error) {
        const errorData = handleApiError(error, 'TeacherDashboard');
        logger.error('Failed to load teacher dashboard data', errorData, 'TeacherDashboard');
      }
    };

    loadData();
  }, [dispatch]);

  const handleRefresh = async () => {
    setRefreshing(true);
    try {
      await Promise.all([
        dispatch(fetchCurrentUser()).unwrap(),
        dispatch(fetchAllOwnClasses()).unwrap()
      ]);
      logger.info('Teacher dashboard refreshed successfully', null, 'TeacherDashboard');
    } catch (error) {
      const errorData = handleApiError(error, 'TeacherDashboard');
      logger.error('Failed to refresh teacher dashboard', errorData, 'TeacherDashboard');
    } finally {
      setRefreshing(false);
    }
  };

  // Calculate dashboard statistics
  const stats = useMemo(() => {
    if (!classrooms) return { totalClasses: 0, totalStudents: 0, activeTasks: 0, completedTasks: 0 };

    const totalClasses = classrooms.length;
    const totalStudents = classrooms.reduce((sum, classroom) => sum + (classroom.students?.length || 0), 0);
    // These would come from actual task data in a real implementation
    const activeTasks = Math.floor(totalClasses * 2.5); // Mock calculation
    const completedTasks = Math.floor(totalClasses * 8.3); // Mock calculation

    return { totalClasses, totalStudents, activeTasks, completedTasks };
  }, [classrooms]);

  const isLoading = classroomsLoading || userLoading;

  if (isLoading) {
    return (
      <div className="space-y-6">
        <SkeletonLoader type="page" showStats={true} showCards={true} />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-2xl md:text-3xl text-gray-800 dark:text-gray-100 font-bold">
            Teacher Dashboard
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            Welcome back, {currentUser?.username || 'Teacher'}! Here's your teaching overview.
          </p>
        </div>
        <div className="flex items-center gap-3 mt-4 sm:mt-0">
          <button
            onClick={handleRefresh}
            disabled={refreshing}
            className="btn btn-sm bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600 text-gray-600 dark:text-gray-300"
          >
            <FiRefreshCw className={`w-4 h-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
            Refresh
          </button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6 hover:shadow-md transition-shadow">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Total Classes</p>
              <p className="text-3xl font-bold text-gray-900 dark:text-gray-100 mt-2">{stats.totalClasses}</p>
            </div>
            <div className="w-12 h-12 bg-blue-100 dark:bg-blue-900/30 rounded-xl flex items-center justify-center">
              <FiBookOpen className="text-blue-600 dark:text-blue-400" size={24} />
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6 hover:shadow-md transition-shadow">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Total Students</p>
              <p className="text-3xl font-bold text-gray-900 dark:text-gray-100 mt-2">{stats.totalStudents}</p>
            </div>
            <div className="w-12 h-12 bg-green-100 dark:bg-green-900/30 rounded-xl flex items-center justify-center">
              <FiUsers className="text-green-600 dark:text-green-400" size={24} />
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6 hover:shadow-md transition-shadow">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Active Tasks</p>
              <p className="text-3xl font-bold text-gray-900 dark:text-gray-100 mt-2">{stats.activeTasks}</p>
            </div>
            <div className="w-12 h-12 bg-orange-100 dark:bg-orange-900/30 rounded-xl flex items-center justify-center">
              <FiCheckCircle className="text-orange-600 dark:text-orange-400" size={24} />
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6 hover:shadow-md transition-shadow">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Completed Tasks</p>
              <p className="text-3xl font-bold text-gray-900 dark:text-gray-100 mt-2">{stats.completedTasks}</p>
            </div>
            <div className="w-12 h-12 bg-violet-100 dark:bg-violet-900/30 rounded-xl flex items-center justify-center">
              <FiTrendingUp className="text-violet-600 dark:text-violet-400" size={24} />
            </div>
          </div>
        </div>
      </div>

      {/* Recent Classrooms */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
        <div className="p-6 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <h2 className="text-lg font-semibold text-gray-900 dark:text-gray-100">Recent Classrooms</h2>
            <button className="text-violet-600 hover:text-violet-700 dark:text-violet-400 dark:hover:text-violet-300 text-sm font-medium">
              View All
            </button>
          </div>
        </div>

        <div className="p-6">
          {classroomsError ? (
            <div className="text-center py-8">
              <div className="text-red-500 dark:text-red-400 mb-2">Failed to load classrooms</div>
              <button
                onClick={handleRefresh}
                className="text-violet-600 hover:text-violet-700 dark:text-violet-400 dark:hover:text-violet-300 text-sm"
              >
                Try again
              </button>
            </div>
          ) : classrooms && classrooms.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {classrooms.slice(0, 6).map((classroom) => (
                <div
                  key={classroom.id}
                  className="p-4 border border-gray-200 dark:border-gray-700 rounded-lg hover:border-violet-300 dark:hover:border-violet-600 transition-colors cursor-pointer"
                >
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <h3 className="font-medium text-gray-900 dark:text-gray-100 mb-1">
                        {classroom.name}
                      </h3>
                      <p className="text-sm text-gray-500 dark:text-gray-400 mb-2">
                        {classroom.description || 'No description'}
                      </p>
                      <div className="flex items-center text-xs text-gray-500 dark:text-gray-400">
                        <FiUsers className="w-3 h-3 mr-1" />
                        {classroom.students?.length || 0} students
                      </div>
                    </div>
                    <button className="p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
                      <FiMoreVertical className="w-4 h-4" />
                    </button>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8">
              <FiBookOpen className="mx-auto text-gray-400 mb-4" size={48} />
              <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">No classrooms yet</h3>
              <p className="text-gray-500 dark:text-gray-400 mb-4">
                Create your first classroom to start teaching
              </p>
              <button className="btn bg-violet-500 hover:bg-violet-600 text-white">
                <FiPlus className="w-4 h-4 mr-2" />
                Create Classroom
              </button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

export default TeacherDashboard;

