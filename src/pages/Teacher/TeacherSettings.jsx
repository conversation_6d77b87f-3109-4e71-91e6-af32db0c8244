import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import {
  fetchTeacherByUserId,
  updateTeacherById,
  createTeacherProfile
} from '../../features/teacher/TeacherSlice';
import {
  fetchCurrentUser,
  updateUserProfile
} from '../../features/users/userSlice';
import { useThemeProvider } from '../../utils/ThemeContext';
import {
  FiUser,
  FiMail,
  FiPhone,
  FiGlobe,
  FiSave,
  FiEdit3,
  FiCheck,
  FiX,
  FiBell,
  FiLock,
  FiEye,
  FiEyeOff,
  FiStar,
  FiBookOpen,
  FiClock,
  FiAward,
  FiRefreshCw
} from 'react-icons/fi';

function TeacherSettings() {
  const dispatch = useDispatch();
  const { currentTheme } = useThemeProvider();
  const { currentUser, loading: userLoading } = useSelector((state) => state.users);
  const { teacher, loading: teacherLoading, error } = useSelector((state) => state.teacher);

  const [activeTab, setActiveTab] = useState('profile');
  const [isEditingProfile, setIsEditingProfile] = useState(false);
  const [isEditingTeacher, setIsEditingTeacher] = useState(false);
  const [isSaving, setSaving] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [successMessage, setSuccessMessage] = useState('');
  const [refreshing, setRefreshing] = useState(false);

  const [profileForm, setProfileForm] = useState({
    username: '',
    email: '',
    mobile: '',
    country: '',
    bio: ''
  });

  const [teacherForm, setTeacherForm] = useState({
    specialization: '',
    experience_years: 0,
    bio: '',
    website: '',
    office_hours: ''
  });

  const [notificationSettings, setNotificationSettings] = useState({
    examReminders: true,
    classUpdates: true,
    studentSubmissions: true,
    emailNotifications: false
  });

  // Theme classes
  const cardBg = currentTheme === "dark" ? "bg-gray-800" : "bg-white";
  const inputBg = currentTheme === "dark" ? "bg-gray-700 border-gray-600" : "bg-white border-gray-300";
  const textPrimary = currentTheme === "dark" ? "text-gray-100" : "text-gray-900";
  const textSecondary = currentTheme === "dark" ? "text-gray-400" : "text-gray-600";

  useEffect(() => {
    if (!currentUser) {
      dispatch(fetchCurrentUser());
    }
  }, [dispatch, currentUser]);

  useEffect(() => {
    if (currentUser?.id) {
      dispatch(fetchTeacherByUserId(currentUser.id));
    }
  }, [dispatch, currentUser]);

  useEffect(() => {
    if (currentUser) {
      setProfileForm({
        username: currentUser.username || '',
        email: currentUser.email || '',
        mobile: currentUser.mobile || '',
        country: currentUser.country || '',
        bio: currentUser.bio || ''
      });
    }
  }, [currentUser]);

  useEffect(() => {
    if (teacher) {
      setTeacherForm({
        specialization: teacher.specialization || '',
        experience_years: teacher.experience_years || 0,
        bio: teacher.bio || '',
        website: teacher.website || '',
        office_hours: teacher.office_hours || ''
      });
    }
  }, [teacher]);

  // Clear success message after 3 seconds
  useEffect(() => {
    if (successMessage) {
      const timer = setTimeout(() => {
        setSuccessMessage('');
      }, 3000);
      return () => clearTimeout(timer);
    }
  }, [successMessage]);

  const handleRefresh = async () => {
    setRefreshing(true);
    try {
      await Promise.all([
        dispatch(fetchCurrentUser()).unwrap(),
        currentUser?.id && dispatch(fetchTeacherByUserId(currentUser.id)).unwrap()
      ]);
    } catch (error) {
      console.error('Failed to refresh data:', error);
    } finally {
      setRefreshing(false);
    }
  };

  const handleProfileChange = (e) => {
    const { name, value } = e.target;
    setProfileForm(prev => ({ ...prev, [name]: value }));
  };

  const handleTeacherChange = (e) => {
    const { name, value } = e.target;
    setTeacherForm(prev => ({ ...prev, [name]: value }));
  };

  const handleNotificationChange = (setting) => {
    setNotificationSettings(prev => ({
      ...prev,
      [setting]: !prev[setting]
    }));
  };

  const handleSaveProfile = async () => {
    setSaving(true);
    try {
      await dispatch(updateUserProfile(profileForm)).unwrap();
      setSuccessMessage('Profile updated successfully!');
      setIsEditingProfile(false);
    } catch (error) {
      console.error('Failed to update profile:', error);
    } finally {
      setSaving(false);
    }
  };

  const handleSaveTeacherProfile = async () => {
    setSaving(true);
    try {
      if (teacher) {
        await dispatch(updateTeacherById(teacherForm)).unwrap();
      } else {
        await dispatch(createTeacherProfile(teacherForm)).unwrap();
      }
      setSuccessMessage('Teaching profile updated successfully!');
      setIsEditingTeacher(false);
      // Refresh teacher data
      if (currentUser?.id) {
        dispatch(fetchTeacherByUserId(currentUser.id));
      }
    } catch (error) {
      console.error('Failed to update teacher profile:', error);
    } finally {
      setSaving(false);
    }
  };

  const handleCancelProfileEdit = () => {
    if (currentUser) {
      setProfileForm({
        username: currentUser.username || '',
        email: currentUser.email || '',
        mobile: currentUser.mobile || '',
        country: currentUser.country || '',
        bio: currentUser.bio || ''
      });
    }
    setIsEditingProfile(false);
  };

  const handleCancelTeacherEdit = () => {
    if (teacher) {
      setTeacherForm({
        specialization: teacher.specialization || '',
        experience_years: teacher.experience_years || 0,
        bio: teacher.bio || '',
        website: teacher.website || '',
        office_hours: teacher.office_hours || ''
      });
    }
    setIsEditingTeacher(false);
  };

  const tabs = [
    { id: 'profile', label: 'Profile', icon: FiUser },
    { id: 'teaching', label: 'Teaching Profile', icon: FiBookOpen },
    { id: 'notifications', label: 'Notifications', icon: FiBell },
    { id: 'security', label: 'Security', icon: FiLock }
  ];

  return (
    <div className="p-4 sm:p-8">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-8">
          <div>
            <h1 className="text-2xl md:text-3xl text-gray-800 dark:text-gray-100 font-bold mb-2">
              Settings
            </h1>
            <p className="text-gray-600 dark:text-gray-400">
              Manage your account and teaching preferences
            </p>
          </div>
          <button
            onClick={handleRefresh}
            disabled={refreshing}
            className="mt-4 sm:mt-0 px-4 py-2 bg-violet-600 text-white rounded-lg hover:bg-violet-700 transition-colors duration-200 flex items-center gap-2"
          >
            <FiRefreshCw className={`w-4 h-4 ${refreshing ? 'animate-spin' : ''}`} />
            Refresh
          </button>
        </div>

        {/* Success Message */}
        {successMessage && (
          <div className="mb-6 p-4 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg flex items-center gap-3">
            <FiCheck className="w-5 h-5 text-green-600" />
            <span className="text-green-700 dark:text-green-400">{successMessage}</span>
          </div>
        )}

        {/* Tabs */}
        <div className="flex space-x-1 mb-8 bg-gray-100 dark:bg-gray-700 p-1 rounded-lg overflow-x-auto">
          {tabs.map((tab) => {
            const Icon = tab.icon;
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`flex items-center justify-center gap-2 px-4 py-2 rounded-md transition-colors whitespace-nowrap ${
                  activeTab === tab.id
                    ? 'bg-white dark:bg-gray-800 text-violet-600 shadow-sm'
                    : 'text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200'
                }`}
              >
                <Icon className="w-4 h-4" />
                <span className="hidden sm:inline">{tab.label}</span>
              </button>
            );
          })}
        </div>

        {/* Tab Content */}
        <div className={`${cardBg} rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6`}>
          {activeTab === 'profile' && (
            <div>
              <div className="flex items-center justify-between mb-6">
                <h2 className={`text-lg font-semibold ${textPrimary}`}>Personal Information</h2>
                {!isEditingProfile ? (
                  <button
                    onClick={() => setIsEditingProfile(true)}
                    className="flex items-center gap-2 px-4 py-2 bg-violet-600 text-white rounded-lg hover:bg-violet-700 transition-colors"
                  >
                    <FiEdit3 className="w-4 h-4" />
                    Edit Profile
                  </button>
                ) : (
                  <div className="flex gap-2">
                    <button
                      onClick={handleCancelProfileEdit}
                      className="flex items-center gap-2 px-4 py-2 bg-gray-300 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-400 dark:hover:bg-gray-600 transition-colors"
                    >
                      <FiX className="w-4 h-4" />
                      Cancel
                    </button>
                    <button
                      onClick={handleSaveProfile}
                      disabled={isSaving}
                      className="flex items-center gap-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors disabled:opacity-50"
                    >
                      <FiSave className="w-4 h-4" />
                      {isSaving ? 'Saving...' : 'Save'}
                    </button>
                  </div>
                )}
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className={`block text-sm font-medium ${textSecondary} mb-2`}>
                    Username
                  </label>
                  <div className="relative">
                    <FiUser className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                    <input
                      type="text"
                      name="username"
                      value={profileForm.username}
                      onChange={handleProfileChange}
                      disabled={!isEditingProfile}
                      className={`w-full pl-10 pr-4 py-3 rounded-lg border focus:outline-none focus:ring-2 focus:ring-violet-500 ${inputBg} ${
                        !isEditingProfile ? 'opacity-60 cursor-not-allowed' : ''
                      }`}
                    />
                  </div>
                </div>

                <div>
                  <label className={`block text-sm font-medium ${textSecondary} mb-2`}>
                    Email
                  </label>
                  <div className="relative">
                    <FiMail className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                    <input
                      type="email"
                      name="email"
                      value={profileForm.email}
                      onChange={handleProfileChange}
                      disabled={!isEditingProfile}
                      className={`w-full pl-10 pr-4 py-3 rounded-lg border focus:outline-none focus:ring-2 focus:ring-violet-500 ${inputBg} ${
                        !isEditingProfile ? 'opacity-60 cursor-not-allowed' : ''
                      }`}
                    />
                  </div>
                </div>

                <div>
                  <label className={`block text-sm font-medium ${textSecondary} mb-2`}>
                    Mobile
                  </label>
                  <div className="relative">
                    <FiPhone className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                    <input
                      type="tel"
                      name="mobile"
                      value={profileForm.mobile}
                      onChange={handleProfileChange}
                      disabled={!isEditingProfile}
                      className={`w-full pl-10 pr-4 py-3 rounded-lg border focus:outline-none focus:ring-2 focus:ring-violet-500 ${inputBg} ${
                        !isEditingProfile ? 'opacity-60 cursor-not-allowed' : ''
                      }`}
                    />
                  </div>
                </div>

                <div>
                  <label className={`block text-sm font-medium ${textSecondary} mb-2`}>
                    Country
                  </label>
                  <div className="relative">
                    <FiGlobe className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                    <input
                      type="text"
                      name="country"
                      value={profileForm.country}
                      onChange={handleProfileChange}
                      disabled={!isEditingProfile}
                      className={`w-full pl-10 pr-4 py-3 rounded-lg border focus:outline-none focus:ring-2 focus:ring-violet-500 ${inputBg} ${
                        !isEditingProfile ? 'opacity-60 cursor-not-allowed' : ''
                      }`}
                    />
                  </div>
                </div>

                <div className="md:col-span-2">
                  <label className={`block text-sm font-medium ${textSecondary} mb-2`}>
                    Bio
                  </label>
                  <textarea
                    name="bio"
                    value={profileForm.bio}
                    onChange={handleProfileChange}
                    disabled={!isEditingProfile}
                    rows={4}
                    className={`w-full px-4 py-3 rounded-lg border focus:outline-none focus:ring-2 focus:ring-violet-500 ${inputBg} ${
                      !isEditingProfile ? 'opacity-60 cursor-not-allowed' : ''
                    }`}
                    placeholder="Tell us about yourself..."
                  />
                </div>
              </div>
            </div>
          )}

          {activeTab === 'teaching' && (
            <div>
              <div className="flex items-center justify-between mb-6">
                <h2 className={`text-lg font-semibold ${textPrimary}`}>Teaching Profile</h2>
                {!isEditingTeacher ? (
                  <button
                    onClick={() => setIsEditingTeacher(true)}
                    className="flex items-center gap-2 px-4 py-2 bg-violet-600 text-white rounded-lg hover:bg-violet-700 transition-colors"
                  >
                    <FiEdit3 className="w-4 h-4" />
                    Edit Teaching Profile
                  </button>
                ) : (
                  <div className="flex gap-2">
                    <button
                      onClick={handleCancelTeacherEdit}
                      className="flex items-center gap-2 px-4 py-2 bg-gray-300 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-400 dark:hover:bg-gray-600 transition-colors"
                    >
                      <FiX className="w-4 h-4" />
                      Cancel
                    </button>
                    <button
                      onClick={handleSaveTeacherProfile}
                      disabled={isSaving}
                      className="flex items-center gap-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors disabled:opacity-50"
                    >
                      <FiSave className="w-4 h-4" />
                      {isSaving ? 'Saving...' : 'Save'}
                    </button>
                  </div>
                )}
              </div>

              {!teacher && !isEditingTeacher && (
                <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4 mb-6">
                  <div className="flex items-center gap-3">
                    <FiBookOpen className="w-5 h-5 text-blue-600" />
                    <div>
                      <h3 className="font-medium text-blue-800 dark:text-blue-200">Complete Your Teaching Profile</h3>
                      <p className="text-sm text-blue-700 dark:text-blue-300 mt-1">
                        Add your teaching credentials and experience to help students learn more about you.
                      </p>
                    </div>
                  </div>
                </div>
              )}

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className={`block text-sm font-medium ${textSecondary} mb-2`}>
                    Specialization
                  </label>
                  <div className="relative">
                    <FiStar className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                    <input
                      type="text"
                      name="specialization"
                      value={teacherForm.specialization}
                      onChange={handleTeacherChange}
                      disabled={!isEditingTeacher}
                      className={`w-full pl-10 pr-4 py-3 rounded-lg border focus:outline-none focus:ring-2 focus:ring-violet-500 ${inputBg} ${
                        !isEditingTeacher ? 'opacity-60 cursor-not-allowed' : ''
                      }`}
                      placeholder="e.g., Mathematics, Physics, Computer Science"
                    />
                  </div>
                </div>

                <div>
                  <label className={`block text-sm font-medium ${textSecondary} mb-2`}>
                    Years of Experience
                  </label>
                  <div className="relative">
                    <FiAward className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                    <input
                      type="number"
                      name="experience_years"
                      value={teacherForm.experience_years}
                      onChange={handleTeacherChange}
                      disabled={!isEditingTeacher}
                      min="0"
                      max="50"
                      className={`w-full pl-10 pr-4 py-3 rounded-lg border focus:outline-none focus:ring-2 focus:ring-violet-500 ${inputBg} ${
                        !isEditingTeacher ? 'opacity-60 cursor-not-allowed' : ''
                      }`}
                      placeholder="0"
                    />
                  </div>
                </div>

                <div>
                  <label className={`block text-sm font-medium ${textSecondary} mb-2`}>
                    Website
                  </label>
                  <div className="relative">
                    <FiGlobe className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                    <input
                      type="url"
                      name="website"
                      value={teacherForm.website}
                      onChange={handleTeacherChange}
                      disabled={!isEditingTeacher}
                      className={`w-full pl-10 pr-4 py-3 rounded-lg border focus:outline-none focus:ring-2 focus:ring-violet-500 ${inputBg} ${
                        !isEditingTeacher ? 'opacity-60 cursor-not-allowed' : ''
                      }`}
                      placeholder="https://your-website.com"
                    />
                  </div>
                </div>

                <div>
                  <label className={`block text-sm font-medium ${textSecondary} mb-2`}>
                    Office Hours
                  </label>
                  <div className="relative">
                    <FiClock className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                    <input
                      type="text"
                      name="office_hours"
                      value={teacherForm.office_hours}
                      onChange={handleTeacherChange}
                      disabled={!isEditingTeacher}
                      className={`w-full pl-10 pr-4 py-3 rounded-lg border focus:outline-none focus:ring-2 focus:ring-violet-500 ${inputBg} ${
                        !isEditingTeacher ? 'opacity-60 cursor-not-allowed' : ''
                      }`}
                      placeholder="e.g., Mon-Fri 9:00 AM - 5:00 PM"
                    />
                  </div>
                </div>

                <div className="md:col-span-2">
                  <label className={`block text-sm font-medium ${textSecondary} mb-2`}>
                    Teaching Bio
                  </label>
                  <textarea
                    name="bio"
                    value={teacherForm.bio}
                    onChange={handleTeacherChange}
                    disabled={!isEditingTeacher}
                    rows={4}
                    className={`w-full px-4 py-3 rounded-lg border focus:outline-none focus:ring-2 focus:ring-violet-500 ${inputBg} ${
                      !isEditingTeacher ? 'opacity-60 cursor-not-allowed' : ''
                    }`}
                    placeholder="Describe your teaching philosophy, methods, and what makes you passionate about education..."
                  />
                </div>
              </div>

              {teacher && (
                <div className="mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
                  <h3 className={`font-medium ${textPrimary} mb-4`}>Teaching Statistics</h3>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="text-center p-4 bg-violet-50 dark:bg-violet-900/20 rounded-lg">
                      <div className="text-2xl font-bold text-violet-600 dark:text-violet-400">
                        {teacher.experience_years || 0}
                      </div>
                      <div className="text-sm text-gray-600 dark:text-gray-400">Years Experience</div>
                    </div>
                    <div className="text-center p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                      <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">
                        {teacher.total_classes || 0}
                      </div>
                      <div className="text-sm text-gray-600 dark:text-gray-400">Classes Created</div>
                    </div>
                    <div className="text-center p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
                      <div className="text-2xl font-bold text-green-600 dark:text-green-400">
                        {teacher.total_students || 0}
                      </div>
                      <div className="text-sm text-gray-600 dark:text-gray-400">Students Taught</div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          )}

          {activeTab === 'notifications' && (
            <div>
              <h2 className={`text-lg font-semibold ${textPrimary} mb-6`}>Notification Preferences</h2>
              <div className="space-y-4">
                {Object.entries(notificationSettings).map(([key, value]) => (
                  <div key={key} className="flex items-center justify-between p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
                    <div>
                      <h3 className={`font-medium ${textPrimary}`}>
                        {key === 'examReminders' && 'Exam Reminders'}
                        {key === 'classUpdates' && 'Class Updates'}
                        {key === 'studentSubmissions' && 'Student Submissions'}
                        {key === 'emailNotifications' && 'Email Notifications'}
                      </h3>
                      <p className={`text-sm ${textSecondary}`}>
                        {key === 'examReminders' && 'Get notified about upcoming exams and deadlines'}
                        {key === 'classUpdates' && 'Receive updates about your classes and students'}
                        {key === 'studentSubmissions' && 'Get notified when students submit assignments'}
                        {key === 'emailNotifications' && 'Receive notifications via email'}
                      </p>
                    </div>
                    <button
                      onClick={() => handleNotificationChange(key)}
                      className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                        value ? 'bg-violet-600' : 'bg-gray-200 dark:bg-gray-700'
                      }`}
                    >
                      <span
                        className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                          value ? 'translate-x-6' : 'translate-x-1'
                        }`}
                      />
                    </button>
                  </div>
                ))}
              </div>
            </div>
          )}

          {activeTab === 'security' && (
            <div>
              <h2 className={`text-lg font-semibold ${textPrimary} mb-6`}>Security Settings</h2>
              <div className="space-y-6">
                <div>
                  <h3 className={`font-medium ${textPrimary} mb-4`}>Change Password</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className={`block text-sm font-medium ${textSecondary} mb-2`}>
                        Current Password
                      </label>
                      <div className="relative">
                        <input
                          type={showPassword ? 'text' : 'password'}
                          className={`w-full px-4 py-3 rounded-lg border focus:outline-none focus:ring-2 focus:ring-violet-500 ${inputBg}`}
                          placeholder="Enter current password"
                        />
                        <button
                          type="button"
                          onClick={() => setShowPassword(!showPassword)}
                          className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                        >
                          {showPassword ? <FiEyeOff className="w-4 h-4" /> : <FiEye className="w-4 h-4" />}
                        </button>
                      </div>
                    </div>
                    <div>
                      <label className={`block text-sm font-medium ${textSecondary} mb-2`}>
                        New Password
                      </label>
                      <input
                        type="password"
                        className={`w-full px-4 py-3 rounded-lg border focus:outline-none focus:ring-2 focus:ring-violet-500 ${inputBg}`}
                        placeholder="Enter new password"
                      />
                    </div>
                  </div>
                  <button className="mt-4 px-6 py-2 bg-violet-600 text-white rounded-lg hover:bg-violet-700 transition-colors">
                    Update Password
                  </button>
                </div>

                <div className="border-t border-gray-200 dark:border-gray-700 pt-6">
                  <h3 className={`font-medium ${textPrimary} mb-4`}>Account Status</h3>
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <span className={textSecondary}>Email Verification</span>
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                        currentUser?.is_email_verified
                          ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400'
                          : 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400'
                      }`}>
                        {currentUser?.is_email_verified ? 'Verified' : 'Not Verified'}
                      </span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className={textSecondary}>Mobile Verification</span>
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                        currentUser?.is_mobile_verified
                          ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400'
                          : 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400'
                      }`}>
                        {currentUser?.is_mobile_verified ? 'Verified' : 'Not Verified'}
                      </span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className={textSecondary}>Teaching Profile</span>
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                        teacher
                          ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400'
                          : 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400'
                      }`}>
                        {teacher ? 'Complete' : 'Incomplete'}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

export default TeacherSettings;