export default function ForTeachersInstitutions() {
  return (
    <section className="relative bg-gradient-to-br from-sky-50 via-white to-violet-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900 py-20 px-4 md:px-16 overflow-hidden">
      {/* Decorative SVG Divider */}
      <div className="absolute top-0 left-0 w-full h-16 -z-1">
        <svg viewBox="0 0 1440 320" className="w-full h-full">
          <path fill="#8b5cf6" fillOpacity="0.07" d="M0,64L80,85.3C160,107,320,149,480,154.7C640,160,800,128,960,128C1120,128,1280,160,1360,176L1440,192L1440,0L1360,0C1280,0,1120,0,960,0C800,0,640,0,480,0C320,0,160,0,80,0L0,0Z"></path>
        </svg>
      </div>
      <div className="max-w-6xl mx-auto flex flex-col md:flex-row items-center gap-12 relative z-10">
        {/* Left: Text & Features */}
        <div className="flex-1 text-center md:text-left">
          <h2 className="text-3xl md:text-4xl font-extrabold mb-6 text-gray-800 dark:text-gray-100 animate-fadeInUp">Empowering Teachers & Institutions</h2>
          <p className="text-lg text-gray-600 dark:text-gray-400 mb-8 max-w-2xl animate-fadeInUp" style={{animationDelay: '0.1s'}}>
            We provide powerful tools for educators and institutions to design, assign, and monitor exams — all within a secured AI-invigilated environment.
          </p>
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-6 mb-8 animate-fadeInUp" style={{animationDelay: '0.2s'}}>
            <div className="bg-white dark:bg-gray-800 p-6 rounded-xl shadow-lg border border-violet-100 dark:border-violet-900 hover:scale-105 transition-all duration-300 group cursor-pointer">
              <h3 className="text-lg font-semibold mb-2 text-violet-700 dark:text-violet-300">Design Custom Tests</h3>
              <p className="text-gray-600 dark:text-gray-400">Create multiple choice, coding, and descriptive questions with ease using our intuitive test builder interface.</p>
            </div>
            <div className="bg-white dark:bg-gray-800 p-6 rounded-xl shadow-lg border border-sky-100 dark:border-sky-900 hover:scale-105 transition-all duration-300 group cursor-pointer">
              <h3 className="text-lg font-semibold mb-2 text-sky-700 dark:text-sky-300">Automated Scoring</h3>
              <p className="text-gray-600 dark:text-gray-400">Let AI evaluate objective questions instantly — saving time and increasing accuracy with advanced algorithms.</p>
            </div>
            <div className="bg-white dark:bg-gray-800 p-6 rounded-xl shadow-lg border border-violet-100 dark:border-violet-900 hover:scale-105 transition-all duration-300 group cursor-pointer">
              <h3 className="text-lg font-semibold mb-2 text-violet-700 dark:text-violet-300">Detailed Analytics</h3>
              <p className="text-gray-600 dark:text-gray-400">Track student performance with comprehensive visual reports and identify learning gaps instantly.</p>
            </div>
            <div className="bg-white dark:bg-gray-800 p-6 rounded-xl shadow-lg border border-sky-100 dark:border-sky-900 hover:scale-105 transition-all duration-300 group cursor-pointer">
              <h3 className="text-lg font-semibold mb-2 text-sky-700 dark:text-sky-300">Secure AI Invigilation</h3>
              <p className="text-gray-600 dark:text-gray-400">Ensure exam integrity with advanced AI monitoring and proctoring tools.</p>
            </div>
          </div>
          <button className="bg-violet-600 hover:bg-violet-700 text-white px-7 py-3 rounded-lg font-semibold shadow-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-violet-400 animate-fadeInUp" style={{animationDelay: '0.3s'}}>
            Book a Demo for Institutions
          </button>
        </div>
        {/* Right: Testimonial/Quote */}
        <div className="flex-1 flex flex-col items-center justify-center animate-fadeInUp" style={{animationDelay: '0.4s'}}>
          <div className="bg-white dark:bg-gray-800 p-8 rounded-2xl shadow-xl border border-violet-100 dark:border-violet-900 max-w-md">
            <p className="text-lg italic text-gray-700 dark:text-gray-300 mb-4">“The analytics and AI-invigilated tests have made our exam process seamless and credible. Our teachers and students love the platform!”</p>
            <div className="flex items-center gap-4">
              <img src="https://randomuser.me/api/portraits/men/44.jpg" alt="Teacher" className="w-12 h-12 rounded-full object-cover border-2 border-violet-200 dark:border-violet-700" />
              <div>
                <h4 className="font-semibold text-gray-800 dark:text-gray-100 text-base">Prof. Ahmed Raza</h4>
                <p className="text-sm text-gray-500 dark:text-gray-400">Head of Department, ABC Institute</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
