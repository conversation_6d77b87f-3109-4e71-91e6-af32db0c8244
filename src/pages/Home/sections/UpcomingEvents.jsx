import React from "react";

const events = [
  {
    date: "2024-06-15",
    title: "AI in Education Webinar",
    desc: "Join our expert panel to discuss the future of AI-invigilated exams and certifications.",
  },
  {
    date: "2024-07-01",
    title: "Summer Coding Bootcamp",
    desc: "A 2-week intensive bootcamp for students and teachers. Limited seats available!",
  },
  {
    date: "2024-07-20",
    title: "Institution Onboarding Day",
    desc: "Special onboarding and Q&A for new partner institutions.",
  },
];

export default function UpcomingEvents() {
  return (
    <section id="sponsors" className="relative bg-gradient-to-br from-violet-50 via-white to-sky-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900 py-16 px-4 md:px-16 overflow-hidden">
      <div className="max-w-5xl mx-auto text-center relative z-10">
        <h2 className="text-3xl md:text-4xl font-extrabold mb-6 text-gray-800 dark:text-gray-100 animate-fadeInUp">Upcoming Events</h2>
        <p className="text-lg text-gray-600 dark:text-gray-400 mb-10 max-w-2xl mx-auto animate-fadeInUp" style={{animationDelay: '0.1s'}}>
          Stay up to date with our latest webinars, bootcamps, and community events.
        </p>
        <div className="flex flex-col md:flex-row gap-8 justify-center items-stretch animate-fadeInUp" style={{animationDelay: '0.2s'}}>
          {events.map((event, idx) => (
            <div key={idx} className="bg-white dark:bg-gray-800 shadow-xl rounded-2xl p-6 border border-gray-200 dark:border-gray-700 flex-1 min-w-[250px] hover:scale-105 hover:shadow-2xl transition-all duration-300 cursor-pointer flex flex-col items-center">
              <div className="bg-violet-100 dark:bg-violet-900/30 text-violet-700 dark:text-violet-300 px-4 py-2 rounded-full font-semibold text-sm mb-4">
                {new Date(event.date).toLocaleDateString(undefined, { month: 'short', day: 'numeric', year: 'numeric' })}
              </div>
              <h3 className="text-xl font-semibold text-gray-800 dark:text-gray-100 mb-2">{event.title}</h3>
              <p className="text-gray-600 dark:text-gray-400 mb-2 text-center">{event.desc}</p>
            </div>
          ))}
        </div>
        <button className="mt-10 bg-violet-600 hover:bg-violet-700 text-white px-7 py-3 rounded-lg font-semibold shadow-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-violet-400 animate-fadeInUp" style={{animationDelay: '0.3s'}}>
          View All Events
        </button>
      </div>
    </section>
  );
} 