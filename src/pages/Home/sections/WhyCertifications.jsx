export default function WhyCertifications() {
  return (
    <section className="relative bg-gradient-to-br from-violet-50 via-white to-sky-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900 py-20 px-4 md:px-16 overflow-hidden">
      {/* Decorative SVG Divider */}
      <div className="absolute top-0 left-0 w-full h-16 -z-1">
        <svg viewBox="0 0 1440 320" className="w-full h-full">
          <path fill="#38bdf8" fillOpacity="0.07" d="M0,64L80,85.3C160,107,320,149,480,154.7C640,160,800,128,960,128C1120,128,1280,160,1360,176L1440,192L1440,0L1360,0C1280,0,1120,0,960,0C800,0,640,0,480,0C320,0,160,0,80,0L0,0Z"></path>
        </svg>
      </div>
      <div className="max-w-6xl mx-auto text-center relative z-10">
        <h2 className="text-3xl md:text-4xl font-extrabold mb-6 text-gray-800 dark:text-gray-100 animate-fadeInUp">Why Certifications Matter</h2>
        <p className="text-lg text-gray-600 dark:text-gray-400 mb-12 max-w-3xl mx-auto animate-fadeInUp" style={{animationDelay: '0.1s'}}>
          Certifications validate your skills and help you stand out in a competitive job market. With our AI-invigilated tests, your credibility is backed by technology and trust.
        </p>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 animate-fadeInUp" style={{animationDelay: '0.2s'}}>
          {/* Card 1 */}
          <div className="bg-white dark:bg-gray-800 p-8 rounded-2xl shadow-xl border border-violet-100 dark:border-violet-900 hover:scale-105 hover:shadow-2xl transition-all duration-300 group cursor-pointer">
            <div className="w-16 h-16 mx-auto mb-6 bg-gradient-to-tr from-violet-200 via-violet-400 to-violet-600 dark:from-violet-900 dark:to-violet-700 rounded-xl flex items-center justify-center animate-bounce">
              <svg className="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M6 6V5a3 3 0 013-3h2a3 3 0 013 3v1h2a2 2 0 012 2v3.57A22.952 22.952 0 0110 13a22.95 22.95 0 01-8-1.43V8a2 2 0 012-2h2zm2-1a1 1 0 011-1h2a1 1 0 011 1v1H8V5zm1 5a1 1 0 011-1h.01a1 1 0 110 2H10a1 1 0 01-1-1z" clipRule="evenodd" />
                <path d="M2 13.692V16a2 2 0 002 2h12a2 2 0 002-2v-2.308A24.974 24.974 0 0110 15c-2.796 0-5.487-.46-8-1.308z" />
              </svg>
            </div>
            <h3 className="text-xl font-semibold mb-3 text-gray-800 dark:text-gray-100">Boost Your Resume</h3>
            <p className="text-gray-600 dark:text-gray-400">Stand out with certifications that prove your skills to employers worldwide.</p>
          </div>
          {/* Card 2 */}
          <div className="bg-white dark:bg-gray-800 p-8 rounded-2xl shadow-xl border border-sky-100 dark:border-sky-900 hover:scale-105 hover:shadow-2xl transition-all duration-300 group cursor-pointer">
            <div className="w-16 h-16 mx-auto mb-6 bg-gradient-to-tr from-sky-200 via-sky-400 to-sky-600 dark:from-sky-900 dark:to-sky-700 rounded-xl flex items-center justify-center animate-bounce">
              <svg className="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M3 6a3 3 0 013-3h10a1 1 0 01.8 1.6L14.25 8l2.55 3.4A1 1 0 0116 13H6a1 1 0 00-1 1v3a1 1 0 11-2 0V6z" clipRule="evenodd" />
              </svg>
            </div>
            <h3 className="text-xl font-semibold mb-3 text-gray-800 dark:text-gray-100">Globally Recognized</h3>
            <p className="text-gray-600 dark:text-gray-400">Our certifications are respected by institutions and organizations around the world.</p>
          </div>
          {/* Card 3 */}
          <div className="bg-white dark:bg-gray-800 p-8 rounded-2xl shadow-xl border border-violet-100 dark:border-violet-900 hover:scale-105 hover:shadow-2xl transition-all duration-300 group cursor-pointer">
            <div className="w-16 h-16 mx-auto mb-6 bg-gradient-to-tr from-violet-200 via-violet-400 to-violet-600 dark:from-violet-900 dark:to-violet-700 rounded-xl flex items-center justify-center animate-bounce">
              <svg className="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M6 6V5a3 3 0 013-3h2a3 3 0 013 3v1h2a2 2 0 012 2v3.57A22.952 22.952 0 0110 13a22.95 22.95 0 01-8-1.43V8a2 2 0 012-2h2zm2-1a1 1 0 011-1h2a1 1 0 011 1v1H8V5zm1 5a1 1 0 011-1h.01a1 1 0 110 2H10a1 1 0 01-1-1z" clipRule="evenodd" />
                <path d="M2 13.692V16a2 2 0 002 2h12a2 2 0 002-2v-2.308A24.974 24.974 0 0110 15c-2.796 0-5.487-.46-8-1.308z" />
              </svg>
            </div>
            <h3 className="text-xl font-semibold mb-3 text-gray-800 dark:text-gray-100">Career Opportunities</h3>
            <p className="text-gray-600 dark:text-gray-400">Unlock doors to internships, jobs, and freelance opportunities with your credentials.</p>
          </div>
        </div>
      </div>
    </section>
  );
}
