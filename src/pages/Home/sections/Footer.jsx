export default function Footer() {
  return (
    <footer className="bg-gray-900 dark:bg-gray-950 text-white dark:text-gray-200 py-12 px-4 md:px-16">
      <div className="max-w-6xl mx-auto grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-8">
        {/* Logo & Tagline */}
        <div>
          <h3 className="text-2xl font-bold mb-2 text-violet-400">EduFair</h3>
          <p className="text-sm text-gray-300 dark:text-gray-400">
            Empowering students, teachers, and institutions through certified education.
          </p>
        </div>

        {/* Quick Links */}
        <div>
          <h4 className="font-semibold text-white dark:text-gray-100 mb-3">Quick Links</h4>
          <ul className="space-y-2 text-sm text-gray-300 dark:text-gray-400">
            <li><a href="#" className="hover:text-violet-400 dark:hover:text-violet-300 transition-colors duration-200">Home</a></li>
            <li><a href="#" className="hover:text-violet-400 dark:hover:text-violet-300 transition-colors duration-200">Exams</a></li>
            <li><a href="#" className="hover:text-violet-400 dark:hover:text-violet-300 transition-colors duration-200">Certifications</a></li>
            <li><a href="#" className="hover:text-violet-400 dark:hover:text-violet-300 transition-colors duration-200">Contact</a></li>
          </ul>
        </div>

        {/* Resources */}
        <div>
          <h4 className="font-semibold text-white dark:text-gray-100 mb-3">Resources</h4>
          <ul className="space-y-2 text-sm text-gray-300 dark:text-gray-400">
            <li><a href="#" className="hover:text-violet-400 dark:hover:text-violet-300 transition-colors duration-200">FAQ</a></li>
            <li><a href="#" className="hover:text-violet-400 dark:hover:text-violet-300 transition-colors duration-200">Support</a></li>
            <li><a href="#" className="hover:text-violet-400 dark:hover:text-violet-300 transition-colors duration-200">Privacy Policy</a></li>
            <li><a href="#" className="hover:text-violet-400 dark:hover:text-violet-300 transition-colors duration-200">Terms of Service</a></li>
          </ul>
        </div>

        {/* Socials */}
        <div>
          <h4 className="font-semibold text-white dark:text-gray-100 mb-3">Connect with us</h4>
          <div className="flex space-x-4 mt-2">
            {[
              { icon: "facebook", link: "#" },
              { icon: "twitter", link: "#" },
              { icon: "linkedin", link: "#" },
              { icon: "instagram", link: "#" },
            ].map((social, idx) => (
              <a
                key={idx}
                href={social.link}
                className="w-10 h-10 flex items-center justify-center rounded-full bg-white dark:bg-gray-800 text-violet-600 dark:text-violet-300 hover:bg-violet-50 dark:hover:bg-gray-700 transition-colors duration-200"
              >
                <i className={`fab fa-${social.icon}`}></i>
              </a>
            ))}
          </div>
        </div>
      </div>

      <div className="text-center text-sm text-gray-400 dark:text-gray-500 mt-10 border-t border-gray-700 dark:border-gray-800 pt-6">
        © {new Date().getFullYear()} EduFair. All rights reserved.
      </div>
    </footer>
  );
}
