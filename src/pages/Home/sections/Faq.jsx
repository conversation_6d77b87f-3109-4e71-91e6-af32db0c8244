import { useState } from "react";

const faqs = [
  {
    question: "Are the certificates verified or recognized?",
    answer:
      "Yes! Our certificates are digitally verified and can be added to your resume, LinkedIn, and shared with employers. Some are endorsed by partnering institutions.",
  },
  {
    question: "How does AI invigilation work?",
    answer:
      "AI monitors the test environment using your webcam and microphone. It flags suspicious activities like multiple faces, device usage, or switching tabs.",
  },
  {
    question: "Can institutions create their own exams?",
    answer:
      "Absolutely. Institutions can register, create exam templates, invite students, and review performance reports — all through their dedicated dashboard.",
  },
  {
    question: "Is there any fee for certifications?",
    answer:
      "Some certifications are free, while others may have a minimal fee supported by sponsors. We try to keep learning affordable for everyone.",
  },
];

export default function Faq() {
  const [openIndex, setOpenIndex] = useState(null);

  const toggle = (index) => {
    setOpenIndex(openIndex === index ? null : index);
  };

  return (
    <section id="faq" className="bg-white dark:bg-gray-800 py-20 px-4 md:px-16">
      <div className="max-w-4xl mx-auto">
        <h2 className="text-3xl md:text-4xl font-bold mb-6 text-center text-gray-800 dark:text-gray-100">
          Frequently Asked Questions
        </h2>
        <div className="space-y-4 mt-10">
          {faqs.map((faq, index) => (
            <div
              key={index}
              className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-xl overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300"
            >
              <button
                className="w-full text-left px-8 py-6 bg-white dark:bg-gray-800 text-lg font-medium text-gray-800 dark:text-gray-100 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200 flex items-center justify-between"
                onClick={() => toggle(index)}
              >
                <span>{faq.question}</span>
                <svg 
                  className={`w-6 h-6 text-violet-500 transition-transform duration-200 ${openIndex === index ? 'rotate-180' : ''}`} 
                  fill="none" 
                  stroke="currentColor" 
                  viewBox="0 0 24 24"
                >
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                </svg>
              </button>
              {openIndex === index && (
                <div className="px-8 py-6 text-gray-600 dark:text-gray-400 bg-gray-50 dark:bg-gray-700 border-t border-gray-200 dark:border-gray-600 text-sm leading-relaxed">
                  {faq.answer}
                </div>
              )}
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}
