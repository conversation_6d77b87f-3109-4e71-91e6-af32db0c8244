import React from 'react';

function Courses() {
  return (
    <section className="py-16 bg-white dark:bg-gray-800">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold text-gray-800 dark:text-gray-100 mb-4">Our Courses</h2>
          <p className="text-lg text-gray-600 dark:text-gray-400 max-w-2xl mx-auto">
            Discover our comprehensive range of courses designed to help you succeed in your educational journey.
          </p>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {/* Course cards with Dashboard theme */}
          <div className="bg-white dark:bg-gray-800 shadow-sm rounded-xl p-6 border border-gray-200 dark:border-gray-700 hover:shadow-md transition-shadow duration-200">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-xl font-semibold text-gray-800 dark:text-gray-100">Web Development</h3>
              <span className="text-xs font-semibold text-violet-500 bg-violet-50 dark:bg-violet-900/20 px-2 py-1 rounded-full">Popular</span>
            </div>
            <p className="text-gray-600 dark:text-gray-400 mb-4">Learn modern web development with hands-on projects and real-world applications.</p>
            <div className="flex items-center justify-between">
              <span className="text-2xl font-bold text-gray-800 dark:text-gray-100">$299</span>
              <button className="bg-violet-500 hover:bg-violet-600 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors duration-200">
                Enroll Now
              </button>
            </div>
          </div>
          
          <div className="bg-white dark:bg-gray-800 shadow-sm rounded-xl p-6 border border-gray-200 dark:border-gray-700 hover:shadow-md transition-shadow duration-200">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-xl font-semibold text-gray-800 dark:text-gray-100">Data Science</h3>
              <span className="text-xs font-semibold text-sky-500 bg-sky-50 dark:bg-sky-900/20 px-2 py-1 rounded-full">New</span>
            </div>
            <p className="text-gray-600 dark:text-gray-400 mb-4">Master data analysis, machine learning, and statistical modeling techniques.</p>
            <div className="flex items-center justify-between">
              <span className="text-2xl font-bold text-gray-800 dark:text-gray-100">$399</span>
              <button className="bg-sky-500 hover:bg-sky-600 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors duration-200">
                Enroll Now
              </button>
            </div>
          </div>
          
          <div className="bg-white dark:bg-gray-800 shadow-sm rounded-xl p-6 border border-gray-200 dark:border-gray-700 hover:shadow-md transition-shadow duration-200">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-xl font-semibold text-gray-800 dark:text-gray-100">Digital Marketing</h3>
              <span className="text-xs font-semibold text-violet-500 bg-violet-50 dark:bg-violet-900/20 px-2 py-1 rounded-full">Trending</span>
            </div>
            <p className="text-gray-600 dark:text-gray-400 mb-4">Learn digital marketing strategies, SEO, and social media management.</p>
            <div className="flex items-center justify-between">
              <span className="text-2xl font-bold text-gray-800 dark:text-gray-100">$249</span>
              <button className="bg-violet-500 hover:bg-violet-600 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors duration-200">
                Enroll Now
              </button>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}

export default Courses;