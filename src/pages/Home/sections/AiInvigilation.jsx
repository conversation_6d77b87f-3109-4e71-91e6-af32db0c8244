export default function AiInvigilation() {
  return (
    <section className="bg-gray-900 dark:bg-gray-950 text-white py-20 px-4 md:px-16">
      <div className="max-w-6xl mx-auto text-center">
        <h2 className="text-3xl md:text-4xl font-bold mb-6 text-violet-400">
          Smarter Testing with AI Invigilation
        </h2>
        <p className="text-lg text-gray-300 mb-12 max-w-3xl mx-auto">
          Say goodbye to traditional cheating-prone exams. Our platform uses facial detection, environment scanning, and pattern monitoring to ensure credible, secure testing — all powered by AI.
        </p>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          <div className="bg-white dark:bg-gray-800 text-gray-800 dark:text-gray-100 p-8 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-200 dark:border-gray-700">
            <div className="w-16 h-16 mx-auto mb-6 bg-violet-100 dark:bg-violet-900/20 rounded-xl flex items-center justify-center">
              <svg className="w-8 h-8 text-violet-500" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clipRule="evenodd" />
              </svg>
            </div>
            <h3 className="text-xl font-semibold mb-3 text-gray-800 dark:text-gray-100">Face Detection</h3>
            <p className="text-gray-600 dark:text-gray-400">Ensure the test-taker is present and verified throughout the session with advanced facial recognition technology.</p>
          </div>

          <div className="bg-white dark:bg-gray-800 text-gray-800 dark:text-gray-100 p-8 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-200 dark:border-gray-700">
            <div className="w-16 h-16 mx-auto mb-6 bg-sky-100 dark:bg-sky-900/20 rounded-xl flex items-center justify-center">
              <svg className="w-8 h-8 text-sky-500" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clipRule="evenodd" />
              </svg>
            </div>
            <h3 className="text-xl font-semibold mb-3 text-gray-800 dark:text-gray-100">Environment Check</h3>
            <p className="text-gray-600 dark:text-gray-400">Detect multiple people, mobile phones, or background distractions using advanced webcam analysis.</p>
          </div>

          <div className="bg-white dark:bg-gray-800 text-gray-800 dark:text-gray-100 p-8 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-200 dark:border-gray-700">
            <div className="w-16 h-16 mx-auto mb-6 bg-red-100 dark:bg-red-900/20 rounded-xl flex items-center justify-center">
              <svg className="w-8 h-8 text-red-500" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
              </svg>
            </div>
            <h3 className="text-xl font-semibold mb-3 text-gray-800 dark:text-gray-100">Suspicion Alerts</h3>
            <p className="text-gray-600 dark:text-gray-400">Our smart model flags any suspicious activity or eye movement during the test with real-time monitoring.</p>
          </div>
        </div>
      </div>
    </section>
  );
}
