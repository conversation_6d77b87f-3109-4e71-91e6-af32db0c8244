import { useNavigate } from "react-router-dom";

import certification from "../../../Assets/images/certifications/certification-01.jpg";

export default function Hero() {
  const navigate = useNavigate();

  return (
    <section id="hero" className="relative bg-gradient-to-tr from-violet-500/10 via-sky-400/10 to-white dark:from-gray-900 dark:via-violet-900/30 dark:to-gray-900 text-gray-800 dark:text-gray-100 py-24 px-4 md:px-16 overflow-hidden">
      {/* Decorative SVG Wave Divider */}
      <div className="absolute top-0 left-0 w-full h-24 -z-1">
        <svg viewBox="0 0 1440 320" className="w-full h-full">
          <path fill="#8b5cf6" fillOpacity="0.08" d="M0,160L80,170.7C160,181,320,203,480,197.3C640,192,800,160,960,133.3C1120,107,1280,85,1360,74.7L1440,64L1440,0L1360,0C1280,0,1120,0,960,0C800,0,640,0,480,0C320,0,160,0,80,0L0,0Z"></path>
        </svg>
      </div>
      <div className="max-w-6xl mx-auto flex flex-col md:flex-row items-center justify-between gap-12 relative z-10">
        <div className="flex-1 text-center md:text-left">
          <span className="inline-block bg-violet-100 dark:bg-violet-900/30 text-violet-700 dark:text-violet-300 px-4 py-1 rounded-full text-sm font-semibold mb-4 animate-fadeInUp">Future-Proof Your Career</span>
          <h1 className="text-4xl md:text-5xl font-extrabold mb-6 leading-tight text-gray-800 dark:text-gray-100 drop-shadow-sm animate-fadeInUp" style={{animationDelay: '0.1s'}}>
            Earn <span className="text-violet-600 dark:text-violet-400">Verified Certifications</span> with <span className="text-sky-500 dark:text-sky-400">AI-Invigilated</span> Tests
          </h1>
          <p className="text-lg md:text-xl text-gray-600 dark:text-gray-400 mb-8 animate-fadeInUp" style={{animationDelay: '0.2s'}}>
            Register for top-rated exams, pass skill-based tests, and get certified to boost your career. Trusted by students, teachers, and institutions worldwide.
          </p>
          <div className="flex flex-wrap gap-4 justify-center md:justify-start animate-fadeInUp" style={{animationDelay: '0.3s'}}>
            <button className="bg-violet-600 hover:bg-violet-700 text-white px-7 py-3 cursor-pointer rounded-lg transition-all duration-200 font-semibold shadow-lg flex items-center gap-2 focus:outline-none focus:ring-2 focus:ring-violet-400"
              onClick={() => { navigate("/signup/student") }}
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" d="M12 4v16m8-8H4" /></svg>
              Get Started as Student
            </button>
            <button className="bg-white dark:bg-gray-800 text-violet-700 dark:text-violet-300 border border-violet-200 dark:border-violet-700 hover:bg-violet-50 dark:hover:bg-violet-900 px-7 py-3 cursor-pointer rounded-lg transition-all duration-200 font-semibold shadow flex items-center gap-2 focus:outline-none focus:ring-2 focus:ring-violet-400"
              onClick={() => { navigate("/signup/teacher") }}
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" d="M16 7a4 4 0 01-8 0" /></svg>
              Join as Teacher
            </button>
            <button className="bg-sky-500 hover:bg-sky-600 text-white px-7 py-3 cursor-pointer rounded-lg transition-all duration-200 font-semibold shadow flex items-center gap-2 focus:outline-none focus:ring-2 focus:ring-sky-300"
              onClick={() => { window.scrollTo({top: 800, behavior: 'smooth'}); }}
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" d="M9 5l7 7-7 7" /></svg>
              Try Demo
            </button>
          </div>
        </div>
        <div className="relative w-full max-w-xl mx-auto md:mx-0 overflow-hidden rounded-xl shadow-2xl group bg-white dark:bg-gray-800 animate-fadeInUp" style={{animationDelay: '0.4s'}}>
          {/* Gradient Overlay */}
          <div className="absolute inset-0 bg-gradient-to-tr from-violet-500/20 via-transparent to-sky-500/20 opacity-0 group-hover:opacity-100 transition-opacity duration-500 z-10"></div>
          {/* Animated Badge */}
          <div className="absolute top-4 right-4 z-20 bg-violet-600 text-white px-3 py-1 rounded-full text-xs font-bold shadow animate-bounce">
            New
          </div>
          {/* Image */}
          <img
            src={certification}
            alt="Certification Illustration"
            className="w-full h-auto object-cover transition-transform duration-500 group-hover:scale-105 group-hover:rotate-1"
            style={{boxShadow: '0 8px 32px 0 rgba(139,92,246,0.15)'}}
          />
        </div>
      </div>
    </section>
  );
}

// Animations (add to your global CSS or Tailwind config):
// .animate-fadeInUp { animation: fadeInUp 0.7s both; }
// @keyframes fadeInUp { from { opacity: 0; transform: translateY(40px); } to { opacity: 1; transform: none; } }
