import React, { useEffect, useState } from "react";
import { useParams, useLocation, useNavigate } from "react-router-dom";
import { useThemeProvider } from "../../utils/ThemeContext";
import {
  FiCheckCircle,
  FiXCircle,
  FiClock,
  FiTrendingUp,
  FiTrendingDown,
  FiMinus,
  FiArrowLeft,
  FiDownload,
  FiShare2,
  FiEye,
  FiEyeOff
} from "react-icons/fi";

function StudentExamResults() {
  const { examId } = useParams();
  const location = useLocation();
  const navigate = useNavigate();
  const { currentTheme } = useThemeProvider();
  
  const [showAnswers, setShowAnswers] = useState(false);
  const [loading, setLoading] = useState(true);
  const [examResult, setExamResult] = useState(null);

  // Theme classes
  const themeBg = currentTheme === "dark" ? "bg-gray-900" : "bg-gray-50";
  const themeText = currentTheme === "dark" ? "text-gray-100" : "text-gray-900";
  const cardBg = currentTheme === "dark" ? "bg-gray-800" : "bg-white";

  useEffect(() => {
    // Simulate loading exam results
    // In a real app, you would fetch from API
    const loadResults = async () => {
      setLoading(true);
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      // Mock exam result data
      const mockResult = {
        id: examId,
        title: location.state?.examTitle || "Mathematics Mid-term Exam",
        subject: "Mathematics",
        totalMarks: 100,
        obtainedMarks: 78,
        percentage: 78,
        grade: "B+",
        status: "passed",
        submittedAt: new Date().toISOString(),
        timeTaken: "45 minutes",
        autoSubmitted: location.state?.autoSubmitted || false,
        questions: [
          {
            id: 1,
            text: "What is the derivative of x²?",
            type: "MCQS",
            marks: 5,
            studentAnswer: "2x",
            correctAnswer: "2x",
            isCorrect: true,
            options: ["x", "2x", "x²", "2x²"]
          },
          {
            id: 2,
            text: "Solve for x: 2x + 5 = 15",
            type: "MCQS",
            marks: 5,
            studentAnswer: "5",
            correctAnswer: "5",
            isCorrect: true,
            options: ["3", "5", "7", "10"]
          },
          {
            id: 3,
            text: "What is the area of a circle with radius 5?",
            type: "MCQS",
            marks: 5,
            studentAnswer: "25π",
            correctAnswer: "25π",
            isCorrect: true,
            options: ["10π", "25π", "50π", "100π"]
          },
          {
            id: 4,
            text: "Explain the concept of limits in calculus.",
            type: "DESCRIPTIVE",
            marks: 10,
            studentAnswer: "A limit describes the value that a function approaches as the input approaches some value.",
            correctAnswer: "A limit is the value that a function approaches as the input approaches some value. It's fundamental to calculus and helps define derivatives and integrals.",
            isCorrect: true,
            partialMarks: 8
          },
          {
            id: 5,
            text: "Find the integral of 3x²",
            type: "MCQS",
            marks: 5,
            studentAnswer: "x³ + C",
            correctAnswer: "x³ + C",
            isCorrect: true,
            options: ["3x³", "x³", "x³ + C", "3x³ + C"]
          }
        ],
        classAverage: 72,
        rank: 3,
        totalStudents: 25
      };
      
      setExamResult(mockResult);
      setLoading(false);
    };

    loadResults();
  }, [examId, location.state]);

  const getGradeColor = (grade) => {
    switch (grade) {
      case "A+":
      case "A": return "text-green-600 dark:text-green-400";
      case "B+":
      case "B": return "text-blue-600 dark:text-blue-400";
      case "C+":
      case "C": return "text-yellow-600 dark:text-yellow-400";
      case "D":
      case "F": return "text-red-600 dark:text-red-400";
      default: return "text-gray-600 dark:text-gray-400";
    }
  };

  const getPerformanceIcon = (percentage) => {
    if (percentage >= 80) return <FiTrendingUp className="w-5 h-5 text-green-600" />;
    if (percentage >= 60) return <FiMinus className="w-5 h-5 text-yellow-600" />;
    return <FiTrendingDown className="w-5 h-5 text-red-600" />;
  };

  const getCorrectAnswersCount = () => {
    return examResult?.questions.filter(q => q.isCorrect).length || 0;
  };

  const handleDownloadResult = () => {
    // Implement PDF download functionality
    console.log("Downloading result...");
  };

  const handleShareResult = () => {
    // Implement share functionality
    if (navigator.share) {
      navigator.share({
        title: `Exam Result - ${examResult.title}`,
        text: `I scored ${examResult.percentage}% in ${examResult.title}`,
        url: window.location.href
      });
    }
  };

  if (loading) {
    return (
      <div className={`min-h-screen ${themeBg} ${themeText} flex items-center justify-center`}>
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-violet-600 mx-auto mb-4"></div>
          <p>Loading your results...</p>
        </div>
      </div>
    );
  }

  if (!examResult) {
    return (
      <div className={`min-h-screen ${themeBg} ${themeText} flex items-center justify-center`}>
        <div className={`${cardBg} rounded-xl p-8 max-w-md mx-auto text-center shadow-lg`}>
          <FiXCircle className="w-16 h-16 text-red-500 mx-auto mb-4" />
          <h2 className="text-xl font-semibold mb-2">Results Not Found</h2>
          <p className="text-gray-600 dark:text-gray-400 mb-4">
            Unable to load exam results.
          </p>
          <button
            onClick={() => navigate('/student/exams')}
            className="px-4 py-2 bg-violet-600 text-white rounded-lg hover:bg-violet-700 transition-colors"
          >
            Back to Exams
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className={`min-h-screen ${themeBg} ${themeText} p-4 sm:p-8`}>
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <button
            onClick={() => navigate('/student/exams')}
            className="flex items-center gap-2 text-violet-600 hover:text-violet-700 transition-colors"
          >
            <FiArrowLeft className="w-4 h-4" />
            Back to Exams
          </button>
          
          <div className="flex gap-2">
            <button
              onClick={handleDownloadResult}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center gap-2"
            >
              <FiDownload className="w-4 h-4" />
              Download
            </button>
            <button
              onClick={handleShareResult}
              className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors flex items-center gap-2"
            >
              <FiShare2 className="w-4 h-4" />
              Share
            </button>
          </div>
        </div>

        {/* Result Summary Card */}
        <div className={`${cardBg} rounded-xl p-8 shadow-lg mb-8`}>
          <div className="text-center mb-8">
            <div className={`inline-flex items-center justify-center w-20 h-20 rounded-full mb-4 ${
              examResult.percentage >= 60 ? 'bg-green-100 dark:bg-green-900/30' : 'bg-red-100 dark:bg-red-900/30'
            }`}>
              {examResult.percentage >= 60 ? (
                <FiCheckCircle className="w-10 h-10 text-green-600" />
              ) : (
                <FiXCircle className="w-10 h-10 text-red-600" />
              )}
            </div>
            
            <h1 className="text-2xl font-bold mb-2">{examResult.title}</h1>
            <p className="text-gray-600 dark:text-gray-400">{examResult.subject}</p>
            
            {examResult.autoSubmitted && (
              <div className="mt-4 inline-flex items-center gap-2 px-3 py-1 bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-200 rounded-full text-sm">
                <FiClock className="w-4 h-4" />
                Auto-submitted (Time expired)
              </div>
            )}
          </div>

          {/* Score Display */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div className="text-center">
              <div className="text-3xl font-bold text-violet-600 mb-1">
                {examResult.obtainedMarks}/{examResult.totalMarks}
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-400">Score</div>
            </div>
            
            <div className="text-center">
              <div className={`text-3xl font-bold mb-1 ${getGradeColor(examResult.grade)}`}>
                {examResult.percentage}%
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-400">Percentage</div>
            </div>
            
            <div className="text-center">
              <div className={`text-3xl font-bold mb-1 ${getGradeColor(examResult.grade)}`}>
                {examResult.grade}
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-400">Grade</div>
            </div>
            
            <div className="text-center">
              <div className="flex items-center justify-center gap-2 mb-1">
                {getPerformanceIcon(examResult.percentage)}
                <span className="text-2xl font-bold">#{examResult.rank}</span>
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-400">Rank</div>
            </div>
          </div>

          {/* Additional Stats */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 pt-6 border-t border-gray-200 dark:border-gray-700">
            <div className="text-center">
              <div className="text-lg font-semibold">{getCorrectAnswersCount()}/{examResult.questions.length}</div>
              <div className="text-sm text-gray-600 dark:text-gray-400">Correct Answers</div>
            </div>
            <div className="text-center">
              <div className="text-lg font-semibold">{examResult.timeTaken}</div>
              <div className="text-sm text-gray-600 dark:text-gray-400">Time Taken</div>
            </div>
            <div className="text-center">
              <div className="text-lg font-semibold">{examResult.classAverage}%</div>
              <div className="text-sm text-gray-600 dark:text-gray-400">Class Average</div>
            </div>
          </div>
        </div>

        {/* Question-wise Analysis */}
        <div className={`${cardBg} rounded-xl p-6 shadow-lg`}>
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-xl font-semibold">Question Analysis</h2>
            <button
              onClick={() => setShowAnswers(!showAnswers)}
              className="flex items-center gap-2 px-4 py-2 bg-violet-600 text-white rounded-lg hover:bg-violet-700 transition-colors"
            >
              {showAnswers ? <FiEyeOff className="w-4 h-4" /> : <FiEye className="w-4 h-4" />}
              {showAnswers ? 'Hide' : 'Show'} Answers
            </button>
          </div>

          <div className="space-y-4">
            {examResult.questions.map((question, index) => (
              <div
                key={question.id}
                className={`p-4 rounded-lg border-2 ${
                  question.isCorrect
                    ? 'border-green-200 bg-green-50 dark:border-green-800 dark:bg-green-900/20'
                    : 'border-red-200 bg-red-50 dark:border-red-800 dark:bg-red-900/20'
                }`}
              >
                <div className="flex items-start justify-between mb-3">
                  <div className="flex items-center gap-3">
                    <span className="text-sm font-medium text-gray-600 dark:text-gray-400">
                      Q{index + 1}
                    </span>
                    <span className={`inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium ${
                      question.isCorrect
                        ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400'
                        : 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400'
                    }`}>
                      {question.isCorrect ? (
                        <FiCheckCircle className="w-3 h-3" />
                      ) : (
                        <FiXCircle className="w-3 h-3" />
                      )}
                      {question.isCorrect ? 'Correct' : 'Incorrect'}
                    </span>
                  </div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">
                    {question.type === 'DESCRIPTIVE' && question.partialMarks 
                      ? `${question.partialMarks}/${question.marks}` 
                      : `${question.isCorrect ? question.marks : 0}/${question.marks}`
                    } marks
                  </div>
                </div>

                <p className="text-gray-800 dark:text-gray-200 mb-3">{question.text}</p>

                {showAnswers && (
                  <div className="space-y-2">
                    <div>
                      <span className="text-sm font-medium text-gray-600 dark:text-gray-400">Your Answer: </span>
                      <span className={question.isCorrect ? 'text-green-700 dark:text-green-300' : 'text-red-700 dark:text-red-300'}>
                        {question.studentAnswer || 'Not answered'}
                      </span>
                    </div>
                    {!question.isCorrect && (
                      <div>
                        <span className="text-sm font-medium text-gray-600 dark:text-gray-400">Correct Answer: </span>
                        <span className="text-green-700 dark:text-green-300">{question.correctAnswer}</span>
                      </div>
                    )}
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>

        {/* Performance Insights */}
        <div className={`${cardBg} rounded-xl p-6 shadow-lg mt-8`}>
          <h2 className="text-xl font-semibold mb-4">Performance Insights</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h3 className="font-medium mb-2">Strengths</h3>
              <ul className="text-sm text-gray-600 dark:text-gray-400 space-y-1">
                <li>• Strong performance in multiple choice questions</li>
                <li>• Good understanding of basic concepts</li>
                <li>• Completed exam within time limit</li>
              </ul>
            </div>
            <div>
              <h3 className="font-medium mb-2">Areas for Improvement</h3>
              <ul className="text-sm text-gray-600 dark:text-gray-400 space-y-1">
                <li>• Focus more on descriptive answers</li>
                <li>• Review calculation methods</li>
                <li>• Practice time management</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default StudentExamResults;
