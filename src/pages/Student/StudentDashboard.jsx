import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { getStudentUpcomingExams } from '../../features/exams/ExamSlice';
import { fetchMyClassrooms } from '../../features/classroom/ClassroomSlice';
import { fetchCurrentUser } from '../../features/users/userSlice';
import { useThemeProvider } from '../../utils/ThemeContext';
import {
  FiCalendar,
  FiClock,
  FiBookOpen,
  FiTrendingUp,
  FiUsers,
  FiPlay,
  FiEye,
  FiArrowRight,
  FiAlertCircle
} from 'react-icons/fi';

function StudentDashboard() {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { currentTheme } = useThemeProvider();

  const { exams, loading: examsLoading } = useSelector((state) => state.exams);
  const { myClassrooms, loading: classroomsLoading } = useSelector((state) => state.classroom);
  const { currentUser } = useSelector((state) => state.users);

  const [stats, setStats] = useState({
    totalClasses: 0,
    upcomingExams: 0,
    completedExams: 0,
    averageScore: 0
  });

  // Theme classes
  const cardBg = currentTheme === "dark" ? "bg-gray-800" : "bg-white";
  const textPrimary = currentTheme === "dark" ? "text-gray-100" : "text-gray-900";
  const textSecondary = currentTheme === "dark" ? "text-gray-400" : "text-gray-600";

  useEffect(() => {
    if (!currentUser) {
      dispatch(fetchCurrentUser());
    }
  }, [dispatch, currentUser]);

  useEffect(() => {
    if (currentUser?.id) {
      dispatch(getStudentUpcomingExams()).catch(error => {
        console.error('Failed to fetch student exams:', error);
      });
      dispatch(fetchMyClassrooms()).catch(error => {
        console.error('Failed to fetch my classrooms:', error);
      });
    }
  }, [dispatch, currentUser]);

  useEffect(() => {
    // Debug logging
    console.log('StudentDashboard - exams:', exams, 'type:', typeof exams, 'isArray:', Array.isArray(exams));
    console.log('StudentDashboard - myClassrooms:', myClassrooms, 'type:', typeof myClassrooms, 'isArray:', Array.isArray(myClassrooms));

    // Calculate stats
    const upcomingExams = Array.isArray(exams) ? exams.filter(exam => {
      const now = new Date();
      const startTime = new Date(exam.start_time);
      return startTime > now;
    }).length : 0;

    const completedExams = Array.isArray(exams) ? exams.filter(exam => exam.completed).length : 0;

    // Handle different possible data structures for myClassrooms
    let classroomsCount = 0;
    if (Array.isArray(myClassrooms)) {
      classroomsCount = myClassrooms.length;
    } else if (myClassrooms && Array.isArray(myClassrooms.data)) {
      classroomsCount = myClassrooms.data.length;
    } else if (myClassrooms && Array.isArray(myClassrooms.results)) {
      classroomsCount = myClassrooms.results.length;
    }

    setStats({
      totalClasses: classroomsCount,
      upcomingExams,
      completedExams,
      averageScore: 78 // Mock average score
    });
  }, [exams, myClassrooms]);

  const getExamStatus = (exam) => {
    const now = new Date();
    const startTime = new Date(exam.start_time);
    const endTime = new Date(startTime.getTime() + exam.total_duration * 60000);

    if (exam.completed) return "completed";
    if (now > endTime) return "missed";
    if (now >= startTime && now <= endTime) return "active";
    return "upcoming";
  };

  const upcomingExams = Array.isArray(exams) ? exams.filter(exam => {
    const status = getExamStatus(exam);
    return status === "upcoming" || status === "active";
  }).slice(0, 3) : [];

  // Handle different possible data structures for myClassrooms
  let classroomsArray = [];
  if (Array.isArray(myClassrooms)) {
    classroomsArray = myClassrooms;
  } else if (myClassrooms && Array.isArray(myClassrooms.data)) {
    classroomsArray = myClassrooms.data;
  } else if (myClassrooms && Array.isArray(myClassrooms.results)) {
    classroomsArray = myClassrooms.results;
  }

  const recentClasses = classroomsArray.slice(0, 4);

  return (
    <div className="p-4 sm:p-8">
      {/* Dashboard Header */}
      <div className="sm:flex sm:justify-between sm:items-center mb-8">
        <div className="mb-4 sm:mb-0">
          <h1 className="text-2xl md:text-3xl text-gray-800 dark:text-gray-100 font-bold">
            Welcome back, {currentUser?.username || 'Student'}!
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            Here's what's happening with your studies today
          </p>
        </div>
        <div className="flex gap-3">
          <button
            onClick={() => navigate('/student/exams')}
            className="px-4 py-2 bg-violet-600 text-white rounded-lg hover:bg-violet-700 transition-colors flex items-center gap-2"
          >
            <FiBookOpen className="w-4 h-4" />
            View All Exams
          </button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div className={`${cardBg} rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700`}>
          <div className="flex items-center justify-between">
            <div>
              <p className={`text-sm ${textSecondary}`}>Total Classes</p>
              <p className={`text-2xl font-bold ${textPrimary}`}>{stats.totalClasses}</p>
            </div>
            <div className="p-3 bg-blue-100 dark:bg-blue-900/30 rounded-lg">
              <FiUsers className="w-6 h-6 text-blue-600" />
            </div>
          </div>
        </div>

        <div className={`${cardBg} rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700`}>
          <div className="flex items-center justify-between">
            <div>
              <p className={`text-sm ${textSecondary}`}>Upcoming Exams</p>
              <p className={`text-2xl font-bold ${textPrimary}`}>{stats.upcomingExams}</p>
            </div>
            <div className="p-3 bg-orange-100 dark:bg-orange-900/30 rounded-lg">
              <FiClock className="w-6 h-6 text-orange-600" />
            </div>
          </div>
        </div>

        <div className={`${cardBg} rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700`}>
          <div className="flex items-center justify-between">
            <div>
              <p className={`text-sm ${textSecondary}`}>Completed Exams</p>
              <p className={`text-2xl font-bold ${textPrimary}`}>{stats.completedExams}</p>
            </div>
            <div className="p-3 bg-green-100 dark:bg-green-900/30 rounded-lg">
              <FiBookOpen className="w-6 h-6 text-green-600" />
            </div>
          </div>
        </div>

        <div className={`${cardBg} rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700`}>
          <div className="flex items-center justify-between">
            <div>
              <p className={`text-sm ${textSecondary}`}>Average Score</p>
              <p className={`text-2xl font-bold ${textPrimary}`}>{stats.averageScore}%</p>
            </div>
            <div className="p-3 bg-purple-100 dark:bg-purple-900/30 rounded-lg">
              <FiTrendingUp className="w-6 h-6 text-purple-600" />
            </div>
          </div>
        </div>
      </div>

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Upcoming Exams */}
        <div className="lg:col-span-2">
          <div className={`${cardBg} rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700`}>
            <div className="flex items-center justify-between mb-6">
              <h2 className={`text-lg font-semibold ${textPrimary}`}>Upcoming Exams</h2>
              <button
                onClick={() => navigate('/student/exams')}
                className="text-violet-600 hover:text-violet-700 text-sm flex items-center gap-1"
              >
                View All <FiArrowRight className="w-4 h-4" />
              </button>
            </div>

            {examsLoading ? (
              <div className="space-y-4">
                {[1, 2, 3].map(i => (
                  <div key={i} className="animate-pulse">
                    <div className="h-4 bg-gray-300 dark:bg-gray-700 rounded w-3/4 mb-2"></div>
                    <div className="h-3 bg-gray-300 dark:bg-gray-700 rounded w-1/2"></div>
                  </div>
                ))}
              </div>
            ) : upcomingExams.length > 0 ? (
              <div className="space-y-4">
                {upcomingExams.map((exam) => {
                  const status = getExamStatus(exam);
                  const startTime = new Date(exam.start_time);

                  return (
                    <div key={exam.id} className="p-4 border border-gray-200 dark:border-gray-700 rounded-lg hover:shadow-md transition-shadow">
                      <div className="flex items-center justify-between mb-2">
                        <h3 className={`font-medium ${textPrimary}`}>{exam.title}</h3>
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                          status === 'active'
                            ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400'
                            : 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400'
                        }`}>
                          {status === 'active' ? 'Active Now' : 'Upcoming'}
                        </span>
                      </div>
                      <div className="flex items-center gap-4 text-sm text-gray-600 dark:text-gray-400 mb-3">
                        <span className="flex items-center gap-1">
                          <FiCalendar className="w-4 h-4" />
                          {startTime.toLocaleDateString()}
                        </span>
                        <span className="flex items-center gap-1">
                          <FiClock className="w-4 h-4" />
                          {exam.total_duration} min
                        </span>
                      </div>
                      <div className="flex justify-end">
                        {status === 'active' ? (
                          <button
                            onClick={() => navigate(`/student/take-exam/${exam.id}`)}
                            className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors flex items-center gap-2 text-sm"
                          >
                            <FiPlay className="w-4 h-4" />
                            Take Now
                          </button>
                        ) : (
                          <button
                            onClick={() => navigate(`/student/exams`)}
                            className="px-4 py-2 bg-violet-600 text-white rounded-lg hover:bg-violet-700 transition-colors flex items-center gap-2 text-sm"
                          >
                            <FiEye className="w-4 h-4" />
                            View Details
                          </button>
                        )}
                      </div>
                    </div>
                  );
                })}
              </div>
            ) : (
              <div className="text-center py-8">
                <FiCalendar className="w-12 h-12 text-gray-400 mx-auto mb-3" />
                <p className={`${textSecondary}`}>No upcoming exams</p>
              </div>
            )}
          </div>
        </div>

        {/* My Classes */}
        <div>
          <div className={`${cardBg} rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700`}>
            <div className="flex items-center justify-between mb-6">
              <h2 className={`text-lg font-semibold ${textPrimary}`}>My Classes</h2>
              <button
                onClick={() => navigate('/student/classes')}
                className="text-violet-600 hover:text-violet-700 text-sm flex items-center gap-1"
              >
                View All <FiArrowRight className="w-4 h-4" />
              </button>
            </div>

            {classroomsLoading ? (
              <div className="space-y-3">
                {[1, 2, 3, 4].map(i => (
                  <div key={i} className="animate-pulse">
                    <div className="h-4 bg-gray-300 dark:bg-gray-700 rounded w-full mb-2"></div>
                    <div className="h-3 bg-gray-300 dark:bg-gray-700 rounded w-2/3"></div>
                  </div>
                ))}
              </div>
            ) : recentClasses.length > 0 ? (
              <div className="space-y-3">
                {recentClasses.map((classroom) => (
                  <div key={classroom.id} className="p-3 border border-gray-200 dark:border-gray-700 rounded-lg hover:shadow-sm transition-shadow cursor-pointer">
                    <h3 className={`font-medium ${textPrimary} mb-1`}>{classroom.name}</h3>
                    <p className={`text-sm ${textSecondary}`}>{classroom.subject?.name || 'No subject'}</p>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8">
                <FiUsers className="w-12 h-12 text-gray-400 mx-auto mb-3" />
                <p className={`${textSecondary}`}>No classes enrolled</p>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}

export default StudentDashboard;