import React, { useEffect, useState, useCallback } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useParams, useNavigate } from "react-router-dom";
import { getStudentExam } from "../../features/exams/ExamSlice";
import { useThemeProvider } from "../../utils/ThemeContext";
import {
  FiClock,
  FiAlertTriangle,
  FiCheck,
  FiArrowLeft,
  FiArrowRight,
  FiSend,
  FiBookOpen,
  FiUser
} from "react-icons/fi";

function StudentTakeExam() {
  const { examId } = useParams();
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { currentTheme } = useThemeProvider();
  
  const { currentExam, loading, error } = useSelector((state) => state.exams);
  
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [answers, setAnswers] = useState({});
  const [timeRemaining, setTimeRemaining] = useState(0);
  const [examStarted, setExamStarted] = useState(false);
  const [showSubmitConfirm, setShowSubmitConfirm] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Theme classes
  const themeBg = currentTheme === "dark" ? "bg-gray-900" : "bg-gray-50";
  const themeText = currentTheme === "dark" ? "text-gray-100" : "text-gray-900";
  const cardBg = currentTheme === "dark" ? "bg-gray-800" : "bg-white";

  useEffect(() => {
    if (examId) {
      dispatch(getStudentExam(examId));
    }
  }, [dispatch, examId]);

  useEffect(() => {
    if (currentExam && examStarted) {
      setTimeRemaining(currentExam.total_duration * 60); // Convert minutes to seconds
    }
  }, [currentExam, examStarted]);

  // Timer countdown
  useEffect(() => {
    if (examStarted && timeRemaining > 0) {
      const timer = setInterval(() => {
        setTimeRemaining(prev => {
          if (prev <= 1) {
            handleAutoSubmit();
            return 0;
          }
          return prev - 1;
        });
      }, 1000);

      return () => clearInterval(timer);
    }
  }, [examStarted, timeRemaining]);

  const formatTime = (seconds) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    
    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    }
    return `${minutes}:${secs.toString().padStart(2, '0')}`;
  };

  const handleStartExam = () => {
    setExamStarted(true);
  };

  const handleAnswerChange = (questionId, answer) => {
    setAnswers(prev => ({
      ...prev,
      [questionId]: answer
    }));
  };

  const handleNextQuestion = () => {
    if (currentQuestionIndex < currentExam.questions.length - 1) {
      setCurrentQuestionIndex(prev => prev + 1);
    }
  };

  const handlePreviousQuestion = () => {
    if (currentQuestionIndex > 0) {
      setCurrentQuestionIndex(prev => prev - 1);
    }
  };

  const handleQuestionNavigation = (index) => {
    setCurrentQuestionIndex(index);
  };

  const handleAutoSubmit = useCallback(() => {
    // Auto-submit when time runs out
    handleSubmitExam(true);
  }, []);

  const handleSubmitExam = async (autoSubmit = false) => {
    setIsSubmitting(true);
    try {
      // Here you would typically submit to an API
      // const result = await dispatch(submitExamAnswers({ examId, answers })).unwrap();
      
      // For now, simulate submission
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Navigate to results page
      navigate(`/student/exam-results/${examId}`, { 
        state: { 
          answers, 
          autoSubmitted: autoSubmit,
          examTitle: currentExam.title 
        } 
      });
    } catch (error) {
      console.error('Failed to submit exam:', error);
      setIsSubmitting(false);
    }
  };

  const getAnsweredCount = () => {
    return Object.keys(answers).length;
  };

  const isQuestionAnswered = (questionId) => {
    return answers.hasOwnProperty(questionId);
  };

  if (loading) {
    return (
      <div className={`min-h-screen ${themeBg} ${themeText} flex items-center justify-center`}>
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-violet-600 mx-auto mb-4"></div>
          <p>Loading exam...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`min-h-screen ${themeBg} ${themeText} flex items-center justify-center`}>
        <div className={`${cardBg} rounded-xl p-8 max-w-md mx-auto text-center shadow-lg`}>
          <FiAlertTriangle className="w-16 h-16 text-red-500 mx-auto mb-4" />
          <h2 className="text-xl font-semibold mb-2">Error Loading Exam</h2>
          <p className="text-gray-600 dark:text-gray-400 mb-4">
            {typeof error === 'string' ? error : 'Failed to load exam'}
          </p>
          <button
            onClick={() => navigate('/student/exams')}
            className="px-4 py-2 bg-violet-600 text-white rounded-lg hover:bg-violet-700 transition-colors"
          >
            Back to Exams
          </button>
        </div>
      </div>
    );
  }

  if (!currentExam) {
    return null;
  }

  // Pre-exam screen
  if (!examStarted) {
    return (
      <div className={`min-h-screen ${themeBg} ${themeText} flex items-center justify-center p-4`}>
        <div className={`${cardBg} rounded-xl p-8 max-w-2xl mx-auto shadow-lg`}>
          <div className="text-center mb-8">
            <FiBookOpen className="w-16 h-16 text-violet-600 mx-auto mb-4" />
            <h1 className="text-2xl font-bold mb-2">{currentExam.title}</h1>
            <p className="text-gray-600 dark:text-gray-400">{currentExam.description}</p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8">
            <div className="text-center p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
              <FiClock className="w-6 h-6 text-blue-600 mx-auto mb-2" />
              <div className="font-semibold">{currentExam.total_duration} minutes</div>
              <div className="text-sm text-gray-600 dark:text-gray-400">Duration</div>
            </div>
            <div className="text-center p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
              <FiBookOpen className="w-6 h-6 text-green-600 mx-auto mb-2" />
              <div className="font-semibold">{currentExam.questions?.length || 0} questions</div>
              <div className="text-sm text-gray-600 dark:text-gray-400">Total Questions</div>
            </div>
            <div className="text-center p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
              <FiUser className="w-6 h-6 text-purple-600 mx-auto mb-2" />
              <div className="font-semibold">{currentExam.total_marks} marks</div>
              <div className="text-sm text-gray-600 dark:text-gray-400">Total Marks</div>
            </div>
          </div>

          <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4 mb-8">
            <div className="flex items-start gap-3">
              <FiAlertTriangle className="w-5 h-5 text-yellow-600 mt-0.5" />
              <div>
                <h3 className="font-semibold text-yellow-800 dark:text-yellow-200 mb-2">Instructions:</h3>
                <ul className="text-sm text-yellow-700 dark:text-yellow-300 space-y-1">
                  <li>• Once you start the exam, the timer will begin counting down</li>
                  <li>• You can navigate between questions using the navigation panel</li>
                  <li>• Make sure to save your answers before the time runs out</li>
                  <li>• The exam will auto-submit when time expires</li>
                </ul>
              </div>
            </div>
          </div>

          <div className="flex gap-4 justify-center">
            <button
              onClick={() => navigate('/student/exams')}
              className="px-6 py-3 bg-gray-300 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-400 dark:hover:bg-gray-600 transition-colors flex items-center gap-2"
            >
              <FiArrowLeft className="w-4 h-4" />
              Back to Exams
            </button>
            <button
              onClick={handleStartExam}
              className="px-8 py-3 bg-violet-600 text-white rounded-lg hover:bg-violet-700 transition-colors font-semibold"
            >
              Start Exam
            </button>
          </div>
        </div>
      </div>
    );
  }

  const currentQuestion = currentExam.questions[currentQuestionIndex];
  const progress = ((currentQuestionIndex + 1) / currentExam.questions.length) * 100;

  return (
    <div className={`min-h-screen ${themeBg} ${themeText}`}>
      {/* Header with timer and progress */}
      <div className={`${cardBg} shadow-sm border-b border-gray-200 dark:border-gray-700 sticky top-0 z-10`}>
        <div className="max-w-6xl mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-lg font-semibold">{currentExam.title}</h1>
              <div className="text-sm text-gray-600 dark:text-gray-400">
                Question {currentQuestionIndex + 1} of {currentExam.questions.length}
              </div>
            </div>
            
            <div className="flex items-center gap-4">
              <div className="text-right">
                <div className={`text-lg font-mono font-bold ${timeRemaining < 300 ? 'text-red-600' : 'text-green-600'}`}>
                  {formatTime(timeRemaining)}
                </div>
                <div className="text-xs text-gray-600 dark:text-gray-400">Time Remaining</div>
              </div>
              
              <button
                onClick={() => setShowSubmitConfirm(true)}
                className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors flex items-center gap-2"
              >
                <FiSend className="w-4 h-4" />
                Submit
              </button>
            </div>
          </div>
          
          {/* Progress bar */}
          <div className="mt-4">
            <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
              <div 
                className="bg-violet-600 h-2 rounded-full transition-all duration-300"
                style={{ width: `${progress}%` }}
              ></div>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-6xl mx-auto p-4 flex gap-6">
        {/* Question Navigation Sidebar */}
        <div className={`${cardBg} rounded-xl p-4 w-64 h-fit sticky top-24 shadow-sm`}>
          <h3 className="font-semibold mb-4">Questions</h3>
          <div className="grid grid-cols-5 gap-2">
            {currentExam.questions.map((_, index) => (
              <button
                key={index}
                onClick={() => handleQuestionNavigation(index)}
                className={`w-10 h-10 rounded-lg text-sm font-medium transition-colors ${
                  index === currentQuestionIndex
                    ? 'bg-violet-600 text-white'
                    : isQuestionAnswered(currentExam.questions[index].id)
                    ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400'
                    : 'bg-gray-100 text-gray-600 dark:bg-gray-700 dark:text-gray-400 hover:bg-gray-200 dark:hover:bg-gray-600'
                }`}
              >
                {index + 1}
              </button>
            ))}
          </div>
          
          <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
            <div className="text-sm text-gray-600 dark:text-gray-400">
              Answered: {getAnsweredCount()} / {currentExam.questions.length}
            </div>
          </div>
        </div>

        {/* Main Question Area */}
        <div className="flex-1">
          <div className={`${cardBg} rounded-xl p-8 shadow-sm`}>
            {currentQuestion && (
              <>
                <div className="mb-6">
                  <div className="flex items-center justify-between mb-4">
                    <h2 className="text-xl font-semibold">
                      Question {currentQuestionIndex + 1}
                    </h2>
                    <span className="text-sm text-gray-600 dark:text-gray-400">
                      {currentQuestion.marks} mark{currentQuestion.marks !== 1 ? 's' : ''}
                    </span>
                  </div>
                  <p className="text-gray-800 dark:text-gray-200 leading-relaxed">
                    {currentQuestion.text}
                  </p>
                </div>

                {/* Answer Options */}
                <div className="mb-8">
                  {currentQuestion.Type === 'MCQS' ? (
                    <div className="space-y-3">
                      {currentQuestion.options.map((option, index) => (
                        <label
                          key={index}
                          className={`flex items-center p-4 rounded-lg border-2 cursor-pointer transition-colors ${
                            answers[currentQuestion.id] === option.option_text
                              ? 'border-violet-600 bg-violet-50 dark:bg-violet-900/20'
                              : 'border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600'
                          }`}
                        >
                          <input
                            type="radio"
                            name={`question-${currentQuestion.id}`}
                            value={option.option_text}
                            checked={answers[currentQuestion.id] === option.option_text}
                            onChange={(e) => handleAnswerChange(currentQuestion.id, e.target.value)}
                            className="w-4 h-4 text-violet-600 mr-3"
                          />
                          <span className="flex-1">{option.option_text}</span>
                        </label>
                      ))}
                    </div>
                  ) : (
                    <textarea
                      value={answers[currentQuestion.id] || ''}
                      onChange={(e) => handleAnswerChange(currentQuestion.id, e.target.value)}
                      placeholder="Type your answer here..."
                      rows={6}
                      className="w-full p-4 border-2 border-gray-200 dark:border-gray-700 rounded-lg focus:border-violet-600 focus:outline-none bg-white dark:bg-gray-800 resize-none"
                    />
                  )}
                </div>

                {/* Navigation Buttons */}
                <div className="flex justify-between">
                  <button
                    onClick={handlePreviousQuestion}
                    disabled={currentQuestionIndex === 0}
                    className="px-6 py-3 bg-gray-300 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-400 dark:hover:bg-gray-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2"
                  >
                    <FiArrowLeft className="w-4 h-4" />
                    Previous
                  </button>
                  
                  <button
                    onClick={handleNextQuestion}
                    disabled={currentQuestionIndex === currentExam.questions.length - 1}
                    className="px-6 py-3 bg-violet-600 text-white rounded-lg hover:bg-violet-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2"
                  >
                    Next
                    <FiArrowRight className="w-4 h-4" />
                  </button>
                </div>
              </>
            )}
          </div>
        </div>
      </div>

      {/* Submit Confirmation Modal */}
      {showSubmitConfirm && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
          <div className={`${cardBg} rounded-xl p-6 max-w-md w-full`}>
            <h3 className="text-lg font-semibold mb-4">Submit Exam?</h3>
            <p className="text-gray-600 dark:text-gray-400 mb-6">
              Are you sure you want to submit your exam? You have answered {getAnsweredCount()} out of {currentExam.questions.length} questions.
            </p>
            <div className="flex gap-3">
              <button
                onClick={() => setShowSubmitConfirm(false)}
                className="flex-1 px-4 py-2 bg-gray-300 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-400 dark:hover:bg-gray-600 transition-colors"
              >
                Cancel
              </button>
              <button
                onClick={() => handleSubmitExam()}
                disabled={isSubmitting}
                className="flex-1 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors disabled:opacity-50 flex items-center justify-center gap-2"
              >
                {isSubmitting ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                    Submitting...
                  </>
                ) : (
                  <>
                    <FiCheck className="w-4 h-4" />
                    Submit
                  </>
                )}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

export default StudentTakeExam;
