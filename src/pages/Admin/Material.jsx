import { useState, useEffect, memo, useMemo } from "react";
import { useDispatch, useSelector } from "react-redux";
import { SearchBar } from "../../components/common";
import {
  fetchSubjects,
  createSubject,
} from "../../features/subjects/SubjectSlice";
import {
  fetchChaptersBySubject,
  createChapter,
} from "../../features/chapters/ChapterSlice";
import {
  fetchTopicsByChapter,
  createTopic,
} from "../../features/topics/TopicSlice";
import {
  fetchSubtopicsByTopic,
  createSubtopic,
} from "../../features/subtopics/SubtopicSlice";
import {
  FiSearch,
  FiPlus,
  FiChevronRight,
  FiBook,
  FiBookOpen,
  FiFileText,
  FiLayers,
  FiX,
  FiCheck,
  FiArrowLeft
} from "react-icons/fi";

export default function Material() {
  const dispatch = useDispatch();

  const { subjects, loading: subjectsLoading } = useSelector((state) => state.subjects);
  const { chaptersBySubject } = useSelector((state) => state.chapters);
  const { topicsByChapter } = useSelector((state) => state.topics);
  const { subtopicsByTopic } = useSelector((state) => state.subtopics);

  // Navigation and view state
  const [currentView, setCurrentView] = useState('overview'); // 'overview', 'subject', 'chapter', 'topic'
  const [selectedSubject, setSelectedSubject] = useState(null);
  const [selectedChapter, setSelectedChapter] = useState(null);
  const [selectedTopic, setSelectedTopic] = useState(null);
  const [searchTerm, setSearchTerm] = useState("");

  // Creation state
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [createType, setCreateType] = useState(''); // 'subject', 'chapter', 'topic', 'subtopic'
  const [formData, setFormData] = useState({
    name: '',
    description: ''
  });

  useEffect(() => {
    dispatch(fetchSubjects({ skip: 0, limit: 100 }));
  }, [dispatch]);

  useEffect(() => {
    if (selectedSubject) {
      dispatch(fetchChaptersBySubject({ subjectId: selectedSubject.id, skip: 0, limit: 100 }));
    }
  }, [dispatch, selectedSubject]);

  useEffect(() => {
    if (selectedChapter) {
      dispatch(fetchTopicsByChapter({ chapterId: selectedChapter.id, skip: 0, limit: 100 }));
    }
  }, [dispatch, selectedChapter]);

  useEffect(() => {
    if (selectedTopic) {
      dispatch(fetchSubtopicsByTopic({ topicId: selectedTopic.id, skip: 0, limit: 100 }));
    }
  }, [dispatch, selectedTopic]);



  // Navigation handlers
  const handleSubjectClick = (subject) => {
    setSelectedSubject(subject);
    setSelectedChapter(null);
    setSelectedTopic(null);
    setCurrentView('subject');
  };

  const handleChapterClick = (chapter) => {
    setSelectedChapter(chapter);
    setSelectedTopic(null);
    setCurrentView('chapter');
  };

  const handleTopicClick = (topic) => {
    setSelectedTopic(topic);
    setCurrentView('topic');
  };

  const handleBackToOverview = () => {
    setCurrentView('overview');
    setSelectedSubject(null);
    setSelectedChapter(null);
    setSelectedTopic(null);
  };

  // Create handlers
  const handleCreateClick = (type) => {
    setCreateType(type);
    setFormData({ name: '', description: '' });
    setShowCreateForm(true);
  };

  const handleCreateSubmit = (e) => {
    e.preventDefault();
    if (!formData.name.trim()) return;

    const createData = { name: formData.name.trim() };
    if (formData.description.trim()) {
      createData.description = formData.description.trim();
    }

    let createAction;
    let refreshAction;

    switch (createType) {
      case 'subject':
        createAction = createSubject(createData);
        refreshAction = () => dispatch(fetchSubjects({ skip: 0, limit: 100 }));
        break;
      case 'chapter':
        createData.subject_id = selectedSubject.id;
        createAction = createChapter(createData);
        refreshAction = () => dispatch(fetchChaptersBySubject({ subjectId: selectedSubject.id, skip: 0, limit: 100 }));
        break;
      case 'topic':
        createData.chapter_id = selectedChapter.id;
        createAction = createTopic(createData);
        refreshAction = () => dispatch(fetchTopicsByChapter({ chapterId: selectedChapter.id, skip: 0, limit: 100 }));
        break;
      case 'subtopic':
        createData.topic_id = selectedTopic.id;
        createAction = createSubtopic(createData);
        refreshAction = () => dispatch(fetchSubtopicsByTopic({ topicId: selectedTopic.id, skip: 0, limit: 100 }));
        break;
      default:
        return;
    }

    dispatch(createAction).then(() => {
      setFormData({ name: '', description: '' });
      setShowCreateForm(false);
      setCreateType('');
      refreshAction();
    });
  };

  const handleCancelCreate = () => {
    setShowCreateForm(false);
    setCreateType('');
    setFormData({ name: '', description: '' });
  };

  // Search handlers
  const handleSearchChange = (value) => {
    setSearchTerm(value);
  };

  const handleSearchSubmit = (value) => {
    // Handle search action on Enter press
    setSearchTerm(value);
    // You can add additional search logic here if needed
    console.log('Search submitted:', value);
  };

  const handleSearchClear = () => {
    setSearchTerm('');
  };

  // Enhanced search function
  const searchInItem = (item, searchTerm) => {
    if (!searchTerm.trim()) return true;

    const searchLower = searchTerm.toLowerCase().trim();
    const name = item.name?.toLowerCase() || '';
    const description = item.description?.toLowerCase() || '';

    return name.includes(searchLower) || description.includes(searchLower);
  };

  // Memoized text highlighting component to prevent unnecessary re-renders
  const HighlightedText = memo(({ text, searchTerm }) => {
    if (!searchTerm.trim() || !text) return <span>{text}</span>;

    try {
      const escapedTerm = searchTerm.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
      const parts = text.split(new RegExp(`(${escapedTerm})`, 'gi'));

      return (
        <span>
          {parts.map((part, index) => {
            const isMatch = part.toLowerCase() === searchTerm.toLowerCase();
            return isMatch ? (
              <mark key={index} className="bg-yellow-200 dark:bg-yellow-800 px-1 rounded">
                {part}
              </mark>
            ) : (
              <span key={index}>{part}</span>
            );
          })}
        </span>
      );
    } catch (error) {
      return <span>{text}</span>;
    }
  });

  // Memoized filtering to prevent unnecessary recalculations
  const filteredSubjects = useMemo(() =>
    subjects?.filter(subject => searchInItem(subject, searchTerm)) || [],
    [subjects, searchTerm]
  );

  const filteredChapters = useMemo(() =>
    chaptersBySubject?.filter(chapter => searchInItem(chapter, searchTerm)) || [],
    [chaptersBySubject, searchTerm]
  );

  const filteredTopics = useMemo(() =>
    topicsByChapter?.filter(topic => searchInItem(topic, searchTerm)) || [],
    [topicsByChapter, searchTerm]
  );

  const filteredSubtopics = useMemo(() =>
    subtopicsByTopic?.filter(subtopic => searchInItem(subtopic, searchTerm)) || [],
    [subtopicsByTopic, searchTerm]
  );

  // Loading state
  if (subjectsLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  // Create Form Modal Component
  const CreateFormModal = () => {
    if (!showCreateForm) return null;

    const getTitle = () => {
      switch (createType) {
        case 'subject': return 'Create New Subject';
        case 'chapter': return `Create Chapter in "${selectedSubject?.name}"`;
        case 'topic': return `Create Topic in "${selectedChapter?.name}"`;
        case 'subtopic': return `Create Subtopic in "${selectedTopic?.name}"`;
        default: return 'Create Item';
      }
    };

    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl p-6 w-full max-w-md mx-4">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
              {getTitle()}
            </h3>
            <button
              onClick={handleCancelCreate}
              className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-200"
            >
              <FiX size={20} />
            </button>
          </div>
          
          <form onSubmit={handleCreateSubmit} className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Name *
              </label>
              <input
                type="text"
                value={formData.name}
                onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                placeholder="Enter name"
                autoFocus
                required
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Description (Optional)
              </label>
              <textarea
                value={formData.description}
                onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                placeholder="Enter description"
                rows={3}
              />
            </div>
            
            <div className="flex gap-3 pt-4">
              <button
                type="submit"
                className="flex-1 bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md transition-colors"
              >
                <FiCheck className="inline mr-2" size={16} />
                Create
              </button>
              <button
                type="button"
                onClick={handleCancelCreate}
                className="flex-1 bg-gray-300 dark:bg-gray-600 hover:bg-gray-400 dark:hover:bg-gray-500 text-gray-700 dark:text-gray-200 font-medium py-2 px-4 rounded-md transition-colors"
              >
                Cancel
              </button>
            </div>
          </form>
        </div>
      </div>
    );
  };

  // Breadcrumb Navigation
  const BreadcrumbNav = () => (
    <div className="flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-400 mb-6">
      <button
        onClick={handleBackToOverview}
        className="flex items-center hover:text-blue-600 dark:hover:text-blue-400 transition-colors"
      >
        <FiArrowLeft className="mr-1" size={14} />
        Overview
      </button>
      {selectedSubject && (
        <>
          <span>/</span>
          <button
            onClick={() => {
              setCurrentView('subject');
              setSelectedChapter(null);
              setSelectedTopic(null);
            }}
            className="hover:text-blue-600 dark:hover:text-blue-400 transition-colors"
          >
            {selectedSubject.name}
          </button>
        </>
      )}
      {selectedChapter && (
        <>
          <span>/</span>
          <button
            onClick={() => {
              setCurrentView('chapter');
              setSelectedTopic(null);
            }}
            className="hover:text-blue-600 dark:hover:text-blue-400 transition-colors"
          >
            {selectedChapter.name}
          </button>
        </>
      )}
      {selectedTopic && (
        <>
          <span>/</span>
          <span className="text-gray-700 dark:text-gray-300">{selectedTopic.name}</span>
        </>
      )}
    </div>
  );

  // Quick Action Buttons
  const QuickActions = () => {
    const getCreateButtonText = () => {
      switch (currentView) {
        case 'overview': return 'Add Subject';
        case 'subject': return 'Add Chapter';
        case 'chapter': return 'Add Topic';
        case 'topic': return 'Add Subtopic';
        default: return 'Add Item';
      }
    };

    const getCreateType = () => {
      switch (currentView) {
        case 'overview': return 'subject';
        case 'subject': return 'chapter';
        case 'chapter': return 'topic';
        case 'topic': return 'subtopic';
        default: return 'subject';
      }
    };

    return (
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-6">
        <div>
          <h1 className="text-2xl md:text-3xl text-gray-800 dark:text-gray-100 font-bold">
            Material Management
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            Organize your educational content hierarchy
          </p>
        </div>
        <button
          onClick={() => handleCreateClick(getCreateType())}
          className="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-md transition-colors shadow-sm"
        >
          <FiPlus className="mr-2" size={16} />
          {getCreateButtonText()}
        </button>
      </div>
    );
  };

  // Get context-aware search placeholder
  const getSearchPlaceholder = () => {
    switch (currentView) {
      case 'overview': return 'Search subjects...';
      case 'subject': return `Search chapters in "${selectedSubject?.name}"...`;
      case 'chapter': return `Search topics in "${selectedChapter?.name}"...`;
      case 'topic': return `Search subtopics in "${selectedTopic?.name}"...`;
      default: return 'Search materials...';
    }
  };

  // Get results count and type for current view
  const getSearchResults = () => {
    switch (currentView) {
      case 'overview':
        return { count: filteredSubjects.length, type: 'subjects' };
      case 'subject':
        return { count: filteredChapters.length, type: 'chapters' };
      case 'chapter':
        return { count: filteredTopics.length, type: 'topics' };
      case 'topic':
        return { count: filteredSubtopics.length, type: 'subtopics' };
      default:
        return { count: 0, type: 'items' };
    }
  };

  // Memoized Card Components for different views to prevent unnecessary re-renders
  const SubjectCard = memo(({ subject }) => (
    <div
      onClick={() => handleSubjectClick(subject)}
      className="bg-white dark:bg-gray-800 rounded-lg shadow hover:shadow-md transition-all duration-200 p-6 cursor-pointer border border-transparent hover:border-blue-200 dark:hover:border-blue-700"
    >
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <div className="p-2 bg-blue-100 dark:bg-blue-900 rounded-lg">
            <FiBook className="text-blue-600 dark:text-blue-400" size={20} />
          </div>
          <div>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
              <HighlightedText text={subject.name} searchTerm={searchTerm} />
            </h3>
            <p className="text-sm text-gray-500 dark:text-gray-400">
              {subject.description ? (
                <HighlightedText text={subject.description} searchTerm={searchTerm} />
              ) : (
                'Click to view chapters'
              )}
            </p>
          </div>
        </div>
        <FiChevronRight className="text-gray-400" size={20} />
      </div>
    </div>
  ));

  const ChapterCard = memo(({ chapter }) => (
    <div
      onClick={() => handleChapterClick(chapter)}
      className="bg-white dark:bg-gray-800 rounded-lg shadow hover:shadow-md transition-all duration-200 p-6 cursor-pointer border border-transparent hover:border-green-200 dark:hover:border-green-700"
    >
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <div className="p-2 bg-green-100 dark:bg-green-900 rounded-lg">
            <FiBookOpen className="text-green-600 dark:text-green-400" size={20} />
          </div>
          <div>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
              <HighlightedText text={chapter.name} searchTerm={searchTerm} />
            </h3>
            <p className="text-sm text-gray-500 dark:text-gray-400">
              {chapter.description ? (
                <HighlightedText text={chapter.description} searchTerm={searchTerm} />
              ) : (
                'Click to view topics'
              )}
            </p>
          </div>
        </div>
        <FiChevronRight className="text-gray-400" size={20} />
      </div>
    </div>
  ));

  const TopicCard = memo(({ topic }) => (
    <div
      onClick={() => handleTopicClick(topic)}
      className="bg-white dark:bg-gray-800 rounded-lg shadow hover:shadow-md transition-all duration-200 p-6 cursor-pointer border border-transparent hover:border-purple-200 dark:hover:border-purple-700"
    >
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <div className="p-2 bg-purple-100 dark:bg-purple-900 rounded-lg">
            <FiFileText className="text-purple-600 dark:text-purple-400" size={20} />
          </div>
          <div>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
              <HighlightedText text={topic.name} searchTerm={searchTerm} />
            </h3>
            <p className="text-sm text-gray-500 dark:text-gray-400">
              {topic.description ? (
                <HighlightedText text={topic.description} searchTerm={searchTerm} />
              ) : (
                'Click to view subtopics'
              )}
            </p>
          </div>
        </div>
        <FiChevronRight className="text-gray-400" size={20} />
      </div>
    </div>
  ));

  const SubtopicCard = memo(({ subtopic }) => (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6 border border-transparent">
      <div className="flex items-center space-x-3">
        <div className="p-2 bg-orange-100 dark:bg-orange-900 rounded-lg">
          <FiLayers className="text-orange-600 dark:text-orange-400" size={20} />
        </div>
        <div>
          <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
            <HighlightedText text={subtopic.name} searchTerm={searchTerm} />
          </h3>
          <p className="text-sm text-gray-500 dark:text-gray-400">
            {subtopic.description ? (
              <HighlightedText text={subtopic.description} searchTerm={searchTerm} />
            ) : (
              'Subtopic content'
            )}
          </p>
        </div>
      </div>
    </div>
  ));

  // Empty State Component
  const EmptyState = ({ type, onCreateClick }) => {
    const getEmptyStateContent = () => {
      switch (type) {
        case 'subjects':
          return {
            icon: FiBook,
            title: 'No subjects found',
            description: 'Start building your material hierarchy by creating your first subject.',
            buttonText: 'Create Subject'
          };
        case 'chapters':
          return {
            icon: FiBookOpen,
            title: 'No chapters found',
            description: `Add chapters to organize content within "${selectedSubject?.name}".`,
            buttonText: 'Create Chapter'
          };
        case 'topics':
          return {
            icon: FiFileText,
            title: 'No topics found',
            description: `Add topics to organize content within "${selectedChapter?.name}".`,
            buttonText: 'Create Topic'
          };
        case 'subtopics':
          return {
            icon: FiLayers,
            title: 'No subtopics found',
            description: `Add subtopics to organize content within "${selectedTopic?.name}".`,
            buttonText: 'Create Subtopic'
          };
        default:
          return {
            icon: FiBook,
            title: 'No items found',
            description: 'Start by creating your first item.',
            buttonText: 'Create Item'
          };
      }
    };

    const { icon: Icon, title, description, buttonText } = getEmptyStateContent();

    return (
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-12 text-center">
        <Icon className="mx-auto h-12 w-12 text-gray-400 mb-4" />
        <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
          {title}
        </h3>
        <p className="text-gray-500 dark:text-gray-400 mb-6">
          {description}
        </p>
        <button
          onClick={onCreateClick}
          className="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-md transition-colors"
        >
          <FiPlus className="mr-2" size={16} />
          {buttonText}
        </button>
      </div>
    );
  };

  // Main render function
  return (
    <div className="space-y-6">
      <CreateFormModal />

      {currentView !== 'overview' && <BreadcrumbNav />}

      <QuickActions />

      <SearchBar
        value={searchTerm}
        onChange={handleSearchChange}
        onSearch={handleSearchSubmit}
        onClear={handleSearchClear}
        placeholder={getSearchPlaceholder()}
        showKeyboardShortcut={true}
        showResultsCount={true}
        resultsCount={getSearchResults().count}
        resultsType={getSearchResults().type}
        className="mb-6"
      />

      {/* Content based on current view */}
      {currentView === 'overview' && (
        <div>
          {filteredSubjects.length === 0 ? (
            searchTerm ? (
              <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-12 text-center">
                <FiSearch className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
                  No subjects found for "{searchTerm}"
                </h3>
                <p className="text-gray-500 dark:text-gray-400 mb-6">
                  Try adjusting your search terms or create a new subject.
                </p>
                <div className="flex flex-col sm:flex-row gap-3 justify-center">
                  <button
                    onClick={() => setSearchTerm('')}
                    className="inline-flex items-center px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white font-medium rounded-md transition-colors"
                  >
                    Clear Search
                  </button>
                  <button
                    onClick={() => handleCreateClick('subject')}
                    className="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-md transition-colors"
                  >
                    <FiPlus className="mr-2" size={16} />
                    Create Subject
                  </button>
                </div>
              </div>
            ) : (
              <EmptyState
                type="subjects"
                onCreateClick={() => handleCreateClick('subject')}
              />
            )
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {filteredSubjects.map((subject) => (
                <SubjectCard key={subject.id} subject={subject} />
              ))}
            </div>
          )}
        </div>
      )}

      {currentView === 'subject' && selectedSubject && (
        <div>
          {filteredChapters.length === 0 ? (
            <EmptyState
              type="chapters"
              onCreateClick={() => handleCreateClick('chapter')}
            />
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {filteredChapters.map((chapter) => (
                <ChapterCard key={chapter.id} chapter={chapter} />
              ))}
            </div>
          )}
        </div>
      )}

      {currentView === 'chapter' && selectedChapter && (
        <div>
          {filteredTopics.length === 0 ? (
            <EmptyState
              type="topics"
              onCreateClick={() => handleCreateClick('topic')}
            />
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {filteredTopics.map((topic) => (
                <TopicCard key={topic.id} topic={topic} />
              ))}
            </div>
          )}
        </div>
      )}

      {currentView === 'topic' && selectedTopic && (
        <div>
          {filteredSubtopics.length === 0 ? (
            <EmptyState
              type="subtopics"
              onCreateClick={() => handleCreateClick('subtopic')}
            />
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {filteredSubtopics.map((subtopic) => (
                <SubtopicCard key={subtopic.id} subtopic={subtopic} />
              ))}
            </div>
          )}
        </div>
      )}
    </div>
  );
}
