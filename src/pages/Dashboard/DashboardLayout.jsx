import React, { useState, useEffect } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import { fetchCurrentUser } from '../../features/users/userSlice';
import Sidebar from '../../partials/Sidebar';
import Header from '../../partials/Header';
import Banner from '../../partials/Banner';
import { sidebarConfigs } from '../../partials/sidebarConfigs';

// Dashboard components
import AdminDashboard from '../Admin/AdminDashboard';
import StudentDashboard from '../Student/StudentDashboard';
import TeacherDashboard from '../Teacher/TeacherDashboard';
import MentorDashboard from '../mentor/MentorDashboard';
import InstituteDashboard from '../Institute/InstituteDashboard';
import SponsorDashboard from '../Sponsor/SponsorDashboard';

// Teacher components
import TeacherClasses from '../Teacher/TeacherClasses';
import Classroom from '../Teacher/Classroom';
import ClassInfo from '../Teacher/ClassInfo';
import TeacherSettings from '../Teacher/TeacherSettings';
import TeacherExam from '../Teacher/TeacherExam';
import TeacherClassroom from '../Teacher/TeacherClassroom';

// Admin components
import AdminUsers from '../Admin/Users';
import AdminSettings from '../Admin/AdminSettings';
import Material from '../Admin/Material';
import Plans from '../Admin/Plans';

// Student components
import StudentClasses from '../Student/StudentClasses';
import StudentSettings from '../Student/StudentSettings';
import StudentExams from '../Student/StudentExams';
import StudentTakeExam from '../Student/StudentTakeExam';
import StudentExamResults from '../Student/StudentExamResults';
import StudentClassroom from '../Student/StudentClassroom';

// Institute components
import InstituteTeachers from '../Institute/InstituteTeachers';
import InstituteStudents from '../Institute/InstituteStudents';
import InstituteSettings from '../Institute/InstituteSettings';

// Sponsor components
import SponsorInstitutes from '../Sponsor/SponsorInstitutes';
import SponsorFunding from '../Sponsor/SponsorFunding';
import SponsorSettings from '../Sponsor/SponsorSettings';

// Component mapping for different user types and sections
const componentMap = {
  admin: {
    dashboard: AdminDashboard,
    settings: AdminSettings,
    users: AdminUsers,
    material: Material,
    plans: Plans,
  },
  student: {
    dashboard: StudentDashboard,
    settings: StudentSettings,
    classes: StudentClasses,
    classroom: StudentClassroom,
    exams: StudentExams,
    'take-exam': StudentTakeExam,
    'exam-results': StudentExamResults,
  },
  teacher: {
    dashboard: TeacherDashboard,
    settings: TeacherSettings,
    classes: Classroom,
    classroom: TeacherClassroom,
    'classroom-old': ClassInfo,
    exams: TeacherExam,
  },
  mentor: {
    dashboard: MentorDashboard,
  },
  institute: {
    dashboard: InstituteDashboard,
    settings: InstituteSettings,
    teachers: InstituteTeachers,
    students: InstituteStudents,
  },
  sponsor: {
    dashboard: SponsorDashboard,
    settings: SponsorSettings,
    institutes: SponsorInstitutes,
    funding: SponsorFunding,
  },
};

// URL redirects for different user types
const userTypeRedirects = {
  '/Admin': '/admin/dashboard',
  '/Student': '/student/dashboard',
  '/Teacher': '/teacher/dashboard',
  '/Institute': '/institute/dashboard',
  '/Sponsor': '/sponsor/dashboard',
  '/admin': '/admin/dashboard',
  '/student': '/student/dashboard',
  '/teacher': '/teacher/dashboard',
  '/institute': '/institute/dashboard',
  '/sponsor': '/sponsor/dashboard',
};

function DashboardLayout() {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [isAuthChecking, setIsAuthChecking] = useState(true);
  
  const location = useLocation();
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const { currentUser, status, error } = useSelector((state) => state.users);

  // Extract user type and section from URL path
  const pathSegments = location.pathname.split('/');
  const userType = pathSegments[1];
  const currentSection = pathSegments[2] || 'dashboard';
  const subSection = pathSegments[3];

  // Handle URL redirects
  useEffect(() => {
    const redirectPath = userTypeRedirects[location.pathname];
    if (redirectPath) {
      navigate(redirectPath);
    }
  }, [location.pathname, navigate]);

  // Check authentication and fetch current user data
  useEffect(() => {
    let didCancel = false;
    const token = localStorage.getItem("token");
    const role = localStorage.getItem("role");

    if (!token || !role) {
      console.log('No token or role found in dashboard, redirecting to login');
      navigate('/Login');
      return;
    }

    if (!currentUser) {
      localStorage.removeItem("userdata");
      dispatch(fetchCurrentUser())
        .finally(() => {
          if (!didCancel) setIsAuthChecking(false);
        });
    } else {
      setIsAuthChecking(false);
    }

    return () => { didCancel = true; };
  }, [dispatch, currentUser, navigate]);

  // Store user data in localStorage
  useEffect(() => {
    if (currentUser) {
      localStorage.setItem("userdata", JSON.stringify(currentUser));
    }
  }, [currentUser]);

  // Handle authentication errors
  useEffect(() => {
    if (error && status === 'failed') {
      console.log('Error fetching current user, redirecting to login');
      localStorage.removeItem('token');
      localStorage.removeItem('role');
      localStorage.removeItem('userdata');
      navigate('/Login');
    } else if (status === 'succeeded' && currentUser) {
      setIsAuthChecking(false);
    }
  }, [error, status, navigate, currentUser]);

  // Role-based access control
  useEffect(() => {
    if (currentUser && !isAuthChecking) {
      const userRole = currentUser.user_type;
      const pathRole = location.pathname.split('/')[1];
      
      if (userRole && pathRole && userRole.toLowerCase() !== pathRole.toLowerCase()) {
        console.log(`Access denied: User role ${userRole} trying to access ${pathRole} dashboard`);
        navigate(`/${userRole.toLowerCase()}/dashboard`, { replace: true });
      }
    }
  }, [currentUser, isAuthChecking, location.pathname, navigate]);

  // Get sidebar config for current user type
  const sidebarConfig = sidebarConfigs[userType] || [];

  // Get component to render
  const getComponentToRender = () => {
    const userComponents = componentMap[userType];
    
    if (!userComponents) {
      return (
        <div className="text-center py-8">
          <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-200 mb-4">
            Invalid user type: {userType}
          </h2>
          <p className="text-gray-600 dark:text-gray-400">
            Available user types: {Object.keys(componentMap).join(', ')}
          </p>
        </div>
      );
    }

    const Component = userComponents[currentSection];
    
    if (!Component) {
      return (
        <div className="text-center py-8">
          <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-200 mb-4">
            Page not found: {currentSection}
          </h2>
          <p className="text-gray-600 dark:text-gray-400">
            Available sections for {userType}: {Object.keys(userComponents).join(', ')}
          </p>
        </div>
      );
    }

    // Handle classroom-specific component
    if (currentSection === "classroom" && subSection) {
      // Use different components based on user type
      if (userType === "teacher") {
        return <ClassInfo classroomId={subSection} />;
      } else if (userType === "student") {
        const StudentClassroomComponent = userComponents['classroom'];
        return StudentClassroomComponent ? <StudentClassroomComponent classroomId={subSection} /> : null;
      }
    }

    // Handle exam-specific components with IDs
    if (currentSection === "take-exam" && subSection) {
      const TakeExamComponent = userComponents['take-exam'];
      return TakeExamComponent ? <TakeExamComponent /> : null;
    }

    if (currentSection === "exam-results" && subSection) {
      const ExamResultsComponent = userComponents['exam-results'];
      return ExamResultsComponent ? <ExamResultsComponent /> : null;
    }

    return <Component />;
  };

  // Handle navigation from sidebar
  const handleNavigation = (path) => {
    navigate(path);
  };

  // Update sidebar config with navigation handler
  const sidebarConfigWithNavigation = sidebarConfig.map(item => ({
    ...item,
    onClick: () => handleNavigation(item.path)
  }));

  // Show loading spinner while checking authentication
  if (isAuthChecking || status === 'loading') {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="flex h-screen overflow-hidden">
      {/* Sidebar */}
      <Sidebar
        sidebarOpen={sidebarOpen}
        setSidebarOpen={setSidebarOpen}
        config={sidebarConfigWithNavigation}
      />

      {/* Content area */}
      <div className="relative flex flex-col flex-1 overflow-y-auto overflow-x-hidden">
        {/* Site header */}
        <Header sidebarOpen={sidebarOpen} setSidebarOpen={setSidebarOpen} />

        <main className="grow">
          <div className="px-4 sm:px-6 lg:px-8 py-8 w-full max-w-9xl mx-auto">
            {getComponentToRender()}
          </div>
        </main>

        <Banner />
      </div>
    </div>
  );
}

export default DashboardLayout; 