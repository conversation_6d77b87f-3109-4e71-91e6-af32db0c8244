import { useState, useEffect } from "react";
import { use<PERSON><PERSON><PERSON>, useNavigate } from "react-router-dom";
import { Link } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import { signupUser, clearSignupState } from "../../features/auth/signup/SignupSlice";
import axios from "axios";
import API_BASE_URL from "../../features/api_url/API_URL";

import 'react-phone-input-2/lib/style.css';
import PhoneInput from 'react-phone-input-2';
import 'react-phone-input-2/lib/style.css';

import {
  FiUser,
  FiMail,
  FiPhone,
  FiCreditCard,
  FiLock,
  FiEye,
  FiEyeOff,
} from "react-icons/fi";

import studentImage from "../../Assets/images/auth/student-signup-image.jpg";
import teacherImage from "../../Assets/images/auth/teacher-signup-image.png";
import institutionImage from "../../Assets/images/auth/institution-signup-image.png";
import sponsorImage from "../../Assets/images/auth/sponsor-signup-image.png";
import mentorImage from "../../Assets/images/auth/mentor-signup-image.png";
import Navbar from "../Home/navbar/Navbar";

export default function Signup() {
  const { role } = useParams();
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const { loading, success, error } = useSelector((state) => state.signup);

  // Check if user is already authenticated on component mount
  useEffect(() => {
    const token = localStorage.getItem('token');
    const storedRole = localStorage.getItem('role');
    
    if (token && storedRole) {
      // Validate token before redirecting
      const validateToken = async () => {
        try {
          await axios.get(`${API_BASE_URL}/api/users/me`);
          
          // Token is valid, redirect to dashboard
          const rolePath = storedRole.toLowerCase();
          navigate(`/${rolePath}/dashboard`);
        } catch (error) {
          // Token is invalid or network error, clear auth data
          localStorage.removeItem('token');
          localStorage.removeItem('role');
          localStorage.removeItem('userdata');
        }
      };

      validateToken();
    }
  }, [navigate]);

  // Handle successful signup
  useEffect(() => {
    if (success) {
      // Redirect to login page after successful signup
      navigate('/Login');
      // Clear signup state
      dispatch(clearSignupState());
    }
  }, [success, navigate, dispatch]);

  const roleImages = {
    student: studentImage,
    teacher: teacherImage,
    sponsor: sponsorImage,
    institute: institutionImage,
    Mentor: mentorImage,
  };

  const illustration = roleImages[role]

  const [formData, setFormData] = useState({
    fullName: "",
    email: "",
    phone: "",
    cnic: "",
    passport: "",
    password: "",
    confirmPassword: "",
  });


  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [errors, setErrors] = useState({});
  const [countryCode, setCountryCode] = useState("pk");

  const handleChange = (e) => {
    setFormData({ ...formData, [e.target.name]: e.target.value });
    setErrors({ ...errors, [e.target.name]: "" });
  };

  const validateEmail = (email) => {
    const pattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return pattern.test(email);
  };

  const handleIdentityChange = (e) => {
    const value = e.target.value;

    if (countryCode === "pk") {
      // Determine if it's CNIC (simple check for digits and length)
      const cnicPattern = /^[0-9]{13}$|^[0-9]{5}-[0-9]{7}-[0-9]{1}$/;
      if (cnicPattern.test(value)) {
        setFormData({ ...formData, cnic: value, passport: "" });
      } else {
        setFormData({ ...formData, passport: value, cnic: "" });
      }
    } else {
      setFormData({ ...formData, passport: value, cnic: "" });
    }

    setErrors({ ...errors, cnic: "", passport: "" });
  };


  const handleSubmit = (e) => {
    e.preventDefault();
    const newErrors = {};

    if (!formData.fullName.trim()) newErrors.fullName = "Full name is required.";
    if (!validateEmail(formData.email)) newErrors.email = "Invalid email format.";
    if (!formData.phone.trim()) newErrors.phone = "Phone number is required.";
    if (formData.password.length < 6) newErrors.password = "Password must be at least 6 characters.";
    if (formData.password !== formData.confirmPassword) newErrors.confirmPassword = "Passwords do not match.";

    if (countryCode === "pk") {
      if (!formData.cnic.trim() && !formData.passport.trim()) {
        newErrors.cnic = "Enter either CNIC or Passport.";
        newErrors.passport = "Enter either CNIC or Passport.";
      }
      if (formData.cnic.trim() && formData.passport.trim()) {
        newErrors.cnic = "Only one field should be filled.";
        newErrors.passport = "Only one field should be filled.";
      }
    } else {
      if (!formData.passport.trim()) {
        newErrors.passport = "Passport is required for non-Pakistani users.";
      }
      formData.cnic = "";
    }

    if (Object.keys(newErrors).length > 0) {
      setErrors(newErrors);
      return;
    }

    const userData = {
      username: formData.fullName,
      email: formData.email,
      mobile: formData.phone,
      user_type: role, // e.g., "student", "teacher"
      country: countryCode.toUpperCase(), // or use actual country if available
      password: formData.password,
      cnic: formData.cnic || "",
      passport: formData.passport || ""
    };

    dispatch(signupUser(userData));

  };


  return (
    <div className="min-h-screen flex items-center justify-center bg-white dark:bg-gray-900 px-4 pt-16 md:pt-0">
      <Navbar />
      <div className="max-w-6xl w-full grid grid-cols-1 md:grid-cols-2 gap-8 mt-10 items-center">

        {/* Illustration */}
        {
          (role === "institution" || role === "sponsor" || role === "Mentor") &&
          <div className="hidden md:block">
            <img src={illustration} alt="Teacher Illustration" className="w-full max-w-md mx-auto" />
          </div>
        }

        {/* Signup Form */}
        <form className="space-y-6" onSubmit={handleSubmit}>
          <h2 className="text-3xl font-bold text-gray-800 dark:text-gray-100">Start your free trial</h2>
          <p className="text-gray-600 dark:text-gray-400">No credit card required. Sign up with your work email.</p>

          <div className="space-y-4">
            {/* Full Name */}
            <div className="relative">
              <FiUser className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400 dark:text-gray-500" />
              <input
                type="text"
                name="fullName"
                placeholder="Full name"
                value={formData.fullName}
                onChange={handleChange}
                className="w-full pl-10 px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-violet-500 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400"
              />
              {errors.fullName && <p className="text-red-500 dark:text-red-400 text-sm mt-1">{errors.fullName}</p>}
            </div>

            {/* Email */}
            <div className="relative">
              <FiMail className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400 dark:text-gray-500" />
              <input
                type="email"
                name="email"
                placeholder="Email address"
                value={formData.email}
                onChange={handleChange}
                className="w-full pl-10 px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-violet-500 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400"
              />
              {errors.email && <p className="text-red-500 dark:text-red-400 text-sm mt-1">{errors.email}</p>}
            </div>

            {/* Phone */}
            <div className="">
              <PhoneInput
                country={'pk'}
                value={formData.phone}
                onChange={(phone, country) => {
                  setFormData({ ...formData, phone });
                  setCountryCode(country.countryCode); // e.g., 'pk', 'us'
                }}
                inputProps={{
                  name: 'phone',
                  required: true,
                  className: "w-full pl-14 px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-violet-500 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400"
                }}
                inputStyle={{ width: '100%' }}
              />

              {errors.phone && <p className="text-red-500 dark:text-red-400 text-sm mt-1">{errors.phone}</p>}
            </div>

            {/* Identity Field (Single visible, maps to either CNIC or Passport) */}
            <div className="relative">
              <FiCreditCard className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400 dark:text-gray-500" />
              <input
                type="text"
                placeholder={
                  countryCode === "pk" ? "CNIC or Passport Number" : "Passport Number"
                }
                value={
                  countryCode === "pk"
                    ? formData.cnic || formData.passport
                    : formData.passport
                }
                onChange={handleIdentityChange}
                className="w-full pl-10 px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-violet-500 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400"
              />
              {countryCode === "pk" && (errors.cnic || errors.passport) && (
                <p className="text-red-500 dark:text-red-400 text-sm mt-1">
                  {errors.cnic || errors.passport}
                </p>
              )}
              {countryCode !== "pk" && errors.passport && (
                <p className="text-red-500 dark:text-red-400 text-sm mt-1">{errors.passport}</p>
              )}
            </div>



            {/* Password */}
            <div className="relative">
              <FiLock className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400 dark:text-gray-500" />
              <input
                type={showPassword ? "text" : "password"}
                name="password"
                placeholder="Password"
                value={formData.password}
                onChange={handleChange}
                className="w-full pl-10 pr-10 px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-violet-500 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400"
              />
              <button
                type="button"
                onClick={() => setShowPassword(!showPassword)}
                className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-400 dark:text-gray-500 hover:text-violet-600 dark:hover:text-violet-400 cursor-pointer"
              >
                {showPassword ? <FiEyeOff /> : <FiEye />}
              </button>
              {errors.password && <p className="text-red-500 dark:text-red-400 text-sm mt-1">{errors.password}</p>}
            </div>

            {/* Confirm Password */}
            <div className="relative">
              <FiLock className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400 dark:text-gray-500" />
              <input
                type={showConfirmPassword ? "text" : "password"}
                name="confirmPassword"
                placeholder="Confirm Password"
                value={formData.confirmPassword}
                onChange={handleChange}
                className="w-full pl-10 pr-10 px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-violet-500 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400"
              />
              <button
                type="button"
                onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-400 dark:text-gray-500 hover:text-violet-600 dark:hover:text-violet-400 cursor-pointer"
              >
                {showConfirmPassword ? <FiEyeOff /> : <FiEye />}
              </button>
              {errors.confirmPassword && <p className="text-red-500 dark:text-red-400 text-sm mt-1">{errors.confirmPassword}</p>}
            </div>
          </div>

          <button
            type="submit"
            className={`w-full bg-violet-600 dark:bg-violet-500 text-white font-medium py-2 rounded-md transition ${loading ? "opacity-50 cursor-not-allowed" : "hover:bg-violet-700 dark:hover:bg-violet-600"}`}
            disabled={loading}
          >
            {loading ? "Signing up..." : "Sign up"}
          </button>

          {error && (
            <p className="text-red-600 dark:text-red-400 text-sm mt-2 border border-red-300 dark:border-red-600 bg-red-50 dark:bg-red-900/20 p-2 rounded">
              {error}
            </p>
          )}


          <p className="text-sm text-gray-500 dark:text-gray-400 text-center">
            By signing up, you agree to our&nbsp;
            <Link to="/" className="text-violet-600 dark:text-violet-400 underline">master subscription agreement</Link> and&nbsp;
            <Link to="/" className="text-violet-600 dark:text-violet-400 underline">privacy policy</Link>.
          </p>
        </form>

        {/* Illustration */}
        {
          (role === "student" || role === "teacher") &&
          <div className="hidden md:block">
            <img src={illustration} alt="Teacher Illustration" className="w-full max-w-md mx-auto" />
          </div>
        }
      </div>
    </div>
  );
}
